# WebView ZIP文件处理修复总结

## 问题描述

系统在尝试通过WebView打开ZIP文件时出现空白屏幕。日志显示以下错误：

```
WebView: File is not HTML: /data/data/com.mingsign.esop_client/cache/zippak/w2.zip
WebViewFileManager: Cannot load file: /data/data/com.mingsign.esop_client/cache/zippak/w2.zip
```

## 问题原因

1. 系统尝试直接用WebView打开ZIP文件，而不是先解压并找到其中的HTML文件
2. `WebViewCompatibility.canLoadFile`方法检查文件类型，只允许HTML文件被加载
3. 当尝试加载ZIP文件时，系统报错："WebView: File is not HTML"，导致WebView显示空白

## 解决方案

### 1. 修改`home_screen.dart`中的`_showFileViewer`方法

添加了对ZIP文件的特殊处理：

1. 如果文件类型是ZIP，先检查是否已经解压过
2. 如果已解压，直接使用解压后的HTML文件
3. 如果未解压，先解压ZIP文件，然后查找HTML文件
4. 如果找到HTML文件，使用它替代原来的ZIP文件
5. 如果没有找到HTML文件或解压失败，显示错误信息并记录详细日志

### 2. 增强`file_service.dart`中的ZIP文件验证功能

添加了对ZIP文件内容的验证：

1. 检查ZIP文件是否存在且非空
2. 检查ZIP文件是否包含至少一个HTML文件
3. 如果验证失败，删除可能损坏的文件并返回错误

### 3. 改进`findIndexHtmlFile`方法

实现了智能HTML文件查找策略：

1. 首先在根目录查找index.html
2. 然后在所有子目录中查找index.html
3. 如果找不到index.html，查找任何HTML文件作为备选
4. 优先选择名称中包含"main"、"default"或"home"的HTML文件
5. 如果没有这些优先文件，则使用找到的第一个HTML文件

```dart
void _showFileViewer(File file, String fileType) async {
  _appPreferencesService.updateLastOpenedFile(file.path);

  print('_showFileViewer called with file: ${file.path}, type: $fileType');

  // 检查是否是相同的文件，如果是则强制刷新WebView
  final isSameFile = _currentFile?.path == file.path;

  // 如果当前已经有文件在显示，先关闭现有的查看器
  if (_currentFile != null) {
    print('Closing existing viewer for: ${_currentFile!.path}');
    _viewerManager.closeAllViewers();
  }

  // 如果是ZIP文件，需要先解压并找到HTML文件
  if (fileType == 'zip') {
    final fileService = FileService();

    // 检查文件是否已经解压过
    final existingFile = await fileService.checkExistingFile(file.path);
    if (existingFile != null && existingFile['indexHtmlFile'] != null) {
      // 使用已解压的HTML文件
      file = existingFile['indexHtmlFile'] as File;
      fileType = 'html';
      print('Using existing extracted HTML file: ${file.path}');
    } else {
      // 解压ZIP文件
      print('Extracting ZIP file: ${file.path}');
      final extractPath = await fileService.extractZipFile(file);
      if (extractPath != null) {
        // 查找index.html文件
        final indexFile = await fileService.findIndexHtmlFile(extractPath);
        if (indexFile != null) {
          file = indexFile;
          fileType = 'html';
          print('Found index.html in ZIP: ${file.path}');
        } else {
          print('No index.html found in ZIP file');
          // 显示错误信息
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('ZIP文件中未找到index.html文件')),
          );
          return;
        }
      } else {
        print('Failed to extract ZIP file: ${file.path}');
        // 记录详细错误信息
        final reportService = getIt<ReportService>();
        reportService.reportSystemError(
          module: 'file_viewer',
          message: 'Failed to extract ZIP file',
          additionalInfo: {
            'file_path': file.path,
            'file_size': file.lengthSync(),
            'file_type': fileType,
          },
        );

        // 显示错误信息
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('无法解压ZIP文件，请检查文件是否完整'),
            duration: Duration(seconds: 5),
          ),
        );
        return;
      }
    }
  }

  setState(() {
    _currentFile = file;
    _currentFileType = fileType;
    _currentPageIndex = 1; // 切换到文件查看器页面
  });

  print('Updated _currentFile to: ${_currentFile!.path}');

  // 如果是相同文件或者是WebView类型，需要确保WebView重新加载
  if (isSameFile || fileType == 'html') {
    // 延迟一帧确保setState完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 强制WebView重新加载新内容
      final webViewFileManager = WebViewFileManager();
      webViewFileManager.loadFile(file);
    });
  }

  // 导航到文件查看器页面
  _pageController.animateToPage(
    1,
    duration: const Duration(milliseconds: 300),
    curve: Curves.easeInOut,
  );
}
```

## 相关组件

1. `WebViewRecoveryService`：当WebView内容显示异常时，会尝试恢复WebView的正常显示。这是一个额外的安全措施，但不能解决根本问题。

2. `WebViewInitializer`：负责WebView的初始化和兼容性脚本注入，确保WebView能够正常显示HTML内容。

3. `WebViewCompatibility`：提供WebView兼容性相关的工具方法，包括检查文件是否可以安全加载的`canLoadFile`方法。

## 测试建议

修改后，应该测试以下场景：

1. **正常场景**：
   - 包含index.html的ZIP文件
   - 已经解压过的ZIP文件

2. **备选场景**：
   - 不包含index.html但包含其他HTML文件的ZIP文件
   - 包含多个HTML文件的ZIP文件（验证优先级选择）

3. **错误场景**：
   - 不包含任何HTML文件的ZIP文件
   - 损坏的ZIP文件
   - 空的ZIP文件

## 注意事项

1. 这个修复确保了ZIP文件会被正确解压并找到HTML文件，即使ZIP文件中没有标准的index.html文件，系统也会尝试找到其他HTML文件作为备选。

2. 如果WebView仍然显示空白，可能需要进一步检查WebView的兼容性设置或者HTML文件的内容。

3. `WebViewRecoveryService`提供了额外的恢复机制，如果WebView显示异常，它会尝试恢复WebView的正常显示。

## 后续优化建议

1. **性能优化**：
   - 考虑在后台线程中预先解压ZIP文件，减少用户等待时间
   - 实现更智能的缓存策略，避免重复解压相同的ZIP文件

2. **用户体验改进**：
   - 添加加载指示器，显示解压进度
   - 提供重试选项，当解压失败时允许用户重试

3. **日志和监控**：
   - 添加更多的日志记录点，以便更好地跟踪文件处理流程
   - 实现远程日志收集，以便分析生产环境中的问题