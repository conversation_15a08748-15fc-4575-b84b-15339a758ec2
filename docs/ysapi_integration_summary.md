# YsApi集成总结

## 概述
成功使用 `android/app/libs/ysapi.jar` 中的 `selfStart` 和 `daemon` 方法替换了现有的开机自启和程序守护功能。

## 完成的工作

### 1. 创建YsApiHelper类
- **文件**: `android/app/src/main/kotlin/com/mingsign/esop_client/YsApiHelper.kt`
- **功能**:
  - 通过反射调用ysapi.jar中的MyManager类
  - 实现selfStart方法替换原有的BootCompleteReceiver开机自启
  - 实现daemon方法进行程序守护（com.mingsign.esop_client）
  - 自动在YsApi服务连接成功后设置开机自启和程序守护

### 2. 修改MainActivity
- **文件**: `android/app/src/main/kotlin/com/mingsign/esop_client/MainActivity.kt`
- **修改内容**:
  - 添加YsApiHelper实例
  - 在configureFlutterEngine中初始化YsApiHelper
  - 在onResume中重新启用程序守护
  - 在onDestroy中清理YsApi资源

### 3. 更新AndroidManifest.xml
- **文件**: `android/app/src/main/AndroidManifest.xml`
- **修改内容**:
  - 移除了原有的BootCompleteReceiver配置
  - 保留了RECEIVE_BOOT_COMPLETED权限（ysapi可能需要）

### 4. 备份原有文件
- **备份文件**: `android/app/src/main/kotlin/com/mingsign/esop_client/BootCompleteReceiver.kt.backup`
- **说明**: 原有的开机自启接收器已备份，不再使用

### 5. 依赖配置
- **文件**: `android/app/build.gradle.kts`
- **配置**: 已包含 `implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))`
- **说明**: ysapi.jar已正确加载到项目中

## YsApi功能说明

### selfStart方法
- **用途**: 替换原有的BootCompleteReceiver开机自启功能
- **包名**: com.mingsign.esop_client
- **调用时机**: YsApi服务连接成功后自动调用

### daemon方法
- **用途**: 进行程序守护，防止应用被系统杀死
- **包名**: com.mingsign.esop_client
- **守护时间**: 60秒（可配置：0=30秒，1=60秒，2=180秒）
- **调用时机**:
  - YsApi服务连接成功后自动调用
  - 应用恢复（onResume）时重新调用

## 技术实现细节

### 反射调用
```kotlin
// 获取MyManager实例
val myManagerClass = Class.forName("com.ys.rkapi.MyManager")
val getInstanceMethod = myManagerClass.getMethod("getInstance", Context::class.java)
myManager = getInstanceMethod.invoke(null, context)

// 调用selfStart方法
val selfStartMethod = myManagerClass.getMethod("selfStart", String::class.java)
val result = selfStartMethod.invoke(myManager, PACKAGE_NAME)

// 调用daemon方法
val daemonMethod = myManagerClass.getMethod("daemon", String::class.java, Int::class.javaPrimitiveType)
val result = daemonMethod.invoke(myManager, PACKAGE_NAME, guardTime)
```

### 服务连接回调
- 使用动态代理创建ServiceConnectedInterface回调
- 在服务连接成功后自动设置开机自启和程序守护
- 包含完整的错误处理和日志记录

## 编译结果
✅ **编译成功**: BUILD SUCCESSFUL in 21s
✅ **无编译错误**: 所有方法调用正确，依赖加载成功
✅ **功能集成**: ysapi.jar已成功集成到项目中

## 使用说明

### 应用启动时
1. MainActivity初始化时会自动调用`initializeYsApiHelper()`
2. YsApiHelper会初始化ysapi并绑定AIDL服务
3. 服务连接成功后自动设置开机自启和程序守护

### 运行时
- 应用恢复时会重新启用程序守护
- 应用销毁时会清理YsApi资源
- 所有操作都有完整的日志记录

### 日志标签
- `YsApiHelper`: YsApi相关操作日志
- `MainActivity`: 主活动中的YsApi初始化日志

## 注意事项
1. ysapi.jar需要在支持的设备上运行
2. 某些功能可能需要系统级权限
3. 建议在实际设备上测试开机自启和程序守护功能
4. 原有的BootCompleteReceiver已备份，如需回滚可恢复使用

## 后续建议
1. 在目标设备上测试开机自启功能
2. 验证程序守护是否正常工作
3. 根据实际需求调整守护时间间隔
4. 监控YsApi的性能影响