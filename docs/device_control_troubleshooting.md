# 设备控制故障排除指南

## 重启设备错误解决方案

### 错误描述
```
Failed to reboot device: PlatformException(REBOOT_FAILED, Failed to reboot, java.io.IOException: Error running exec(). Command: [su, -c, reboot] Working Directory: null Environment: null, null)
```

### 问题原因
这个错误通常由以下原因引起：

1. **设备没有root权限**
2. **su命令不可用或被阻止**
3. **SELinux策略限制**
4. **系统权限不足**

### 解决方案

#### 方案1：确保设备已获取root权限
1. 检查设备是否已经root
2. 确认SuperSU或Magisk等root管理工具正常工作
3. 测试su命令是否可用：
   ```bash
   adb shell
   su
   reboot
   ```

#### 方案2：使用系统应用签名
1. 将应用编译为系统应用
2. 使用系统签名证书签名APK
3. 将APK安装到 `/system/app/` 目录

#### 方案3：修改SELinux策略（需要root）
1. 临时禁用SELinux：
   ```bash
   su -c "setenforce 0"
   ```
2. 或者添加SELinux规则允许应用执行重启命令

#### 方案4：使用替代重启方法
应用现在支持多种重启方式：
1. 系统PowerManager API（需要系统权限）
2. Shell命令执行
3. Root权限下的su命令执行

### 权限配置

确保在 `AndroidManifest.xml` 中添加了必要权限：

```xml
<!-- 设备控制相关权限 -->
<uses-permission android:name="android.permission.REBOOT"
    android:protectionLevel="signature|privileged" />
<uses-permission android:name="android.permission.SHUTDOWN"
    android:protectionLevel="signature|privileged" />
<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
<uses-permission android:name="android.permission.ACCESS_SUPERUSER"
    android:protectionLevel="signature|privileged" />
```

### 测试步骤

1. **检查权限**：
   ```dart
   // 在Flutter中测试
   try {
     await DeviceControlService().reboot();
   } catch (e) {
     print('重启失败: $e');
   }
   ```

2. **检查root状态**：
   ```bash
   adb shell
   su -c "id"
   ```

3. **检查SELinux状态**：
   ```bash
   adb shell
   getenforce
   ```

### 常见错误码

- `REBOOT_FAILED`: 重启命令执行失败
- `PERMISSION_DENIED`: 权限不足
- `UNSUPPORTED_OPERATION`: 设备不支持该操作

### 建议

1. **开发环境**：使用模拟器或已root的测试设备
2. **生产环境**：确保应用具有系统级权限或设备已获取root权限
3. **用户提示**：在UI中提供清晰的错误信息和解决建议

## 音量控制

音量控制功能相对简单，只需要 `MODIFY_AUDIO_SETTINGS` 权限：

```xml
<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
```

## 截图功能

截图功能使用Flutter的screenshot插件，不需要特殊权限。

## 联系支持

如果问题仍然存在，请提供：
1. 设备型号和Android版本
2. 是否已root
3. 完整的错误日志
4. SELinux状态