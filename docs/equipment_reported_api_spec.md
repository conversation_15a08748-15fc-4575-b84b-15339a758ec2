# Equipment Reported API 接口规范

## 概述

本文档定义了 `equipment/reported` 接口的数据格式规范，用于终端设备向服务器上报状态和操作信息。

## 接口信息

- **接口路径**: `/v1/equipment/reported`
- **请求方法**: `POST`
- **内容类型**: `application/json`

## 数据结构

### 基础数据结构

所有上报数据都包含以下基础字段：

```json
{
  "mac_address": "设备MAC地址",
  "registration_code": "注册码",
  "device_alias": "设备别名",
  "group_name": "组名",
  "timestamp": "上报时间戳(ISO 8601格式)",
  "report_type": "上报类型",
  "operation_id": "操作唯一标识",
  "data": {
    // 根据report_type不同，包含不同的数据结构
  }
}
```

### 上报类型 (report_type)

| 类型 | 值 | 描述 |
|------|-----|------|
| MQTT指令接收 | `mqtt_command_received` | 接收到MQTT指令 |
| MQTT指令处理完成 | `mqtt_command_processed` | MQTT指令处理完成 |
| 文件下载开始 | `file_download_started` | 文件下载开始 |
| 文件下载完成 | `file_download_completed` | 文件下载完成 |
| 文件下载失败 | `file_download_failed` | 文件下载失败 |
| 文件解压开始 | `file_extraction_started` | 文件解压开始 |
| 文件解压完成 | `file_extraction_completed` | 文件解压完成 |
| 文件解压失败 | `file_extraction_failed` | 文件解压失败 |
| 文档预览开始 | `document_preview_started` | 文档预览开始 |
| 文档预览完成 | `document_preview_completed` | 文档预览完成 |
| 文档预览失败 | `document_preview_failed` | 文档预览失败 |
| WebView加载完成 | `webview_loaded` | WebView加载完成 |
| WebView加载错误 | `webview_error` | WebView加载错误 |
| 系统错误 | `system_error` | 系统错误 |
| 应用日志 | `application_log` | 应用日志 |
| 截屏上报 | `screenshot_taken` | 截屏并上报 |
| 程序当前正在打开的文件 | `current_open_file` | 程序当前正在打开的文件 |
| 程序当前正在打开的文件 | `current_open_file` | 程序当前正在打开的文件 |

## 具体数据格式示例

### 1. MQTT指令接收上报

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:30:45.123Z",
  "report_type": "mqtt_command_received",
  "operation_id": "mqtt_cmd_recv_1737282645123_456",
  "data": {
    "mqtt_topic": "esopChannel",
    "message_type": 1,
    "message_group_name": "Production-Line-A",
    "file_count": 2,
    "command_content": "{\"type\":1,\"group_name\":\"Production-Line-A\",\"list\":[...]}",
    "processing_status": "received"
  }
}
```

### 2. 文件下载完成上报

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:32:15.456Z",
  "report_type": "file_download_completed",
  "operation_id": "file_dl_comp_1737282735456_789",
  "data": {
    "file_url": "http://server.com/files/manual.zip",
    "file_name": "manual.zip",
    "file_size": 2048000,
    "file_type": "zip",
    "download_progress": 1.0,
    "operation_duration": 8.5,
    "status": "success",
    "local_path": "/storage/emulated/0/Android/data/com.mingsign.esop_client/files/zippak/manual.zip"
  }
}
```

### 3. 文档预览完成上报

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:35:22.789Z",
  "report_type": "document_preview_completed",
  "operation_id": "doc_prev_comp_1737282922789_012",
  "data": {
    "file_path": "/storage/emulated/0/Android/data/com.mingsign.esop_client/files/documents/manual.pdf",
    "file_type": "pdf",
    "file_size": 1024000,
    "preview_method": "in_app",
    "load_duration": 2.1,
    "status": "success"
  }
}
```

### 4. WebView加载完成上报

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:38:45.123Z",
  "report_type": "webview_loaded",
  "operation_id": "webview_load_1737283125123_345",
  "data": {
    "url": "file:///storage/emulated/0/Android/data/com.mingsign.esop_client/files/extracted/index.html",
    "html_file_path": "/storage/emulated/0/Android/data/com.mingsign.esop_client/files/extracted/index.html",
    "load_duration": 3.5,
    "status": "success",
    "user_agent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36"
  }
}
```

### 5. 系统错误上报

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:40:15.678Z",
  "report_type": "system_error",
  "operation_id": "sys_err_1737283215678_901",
  "data": {
    "log_level": "error",
    "module": "mqtt",
    "message": "MQTT connection lost",
    "error_code": "MQTT_CONN_LOST",
    "additional_info": {
      "server_address": "*************",
      "port": 1883,
      "reconnect_attempts": 3
    }
  }
}
```

### 6. 应用日志上报

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:42:30.234Z",
  "report_type": "application_log",
  "operation_id": "app_log_1737283350234_567",
  "data": {
    "log_level": "info",
    "module": "user_action",
    "message": "User action: double_tap on main_screen",
    "additional_info": {
      "action": "double_tap",
      "screen": "main_screen",
      "timestamp": "2025-01-19T10:42:30.234Z"
    }
  }
}
```

### 7. 截屏上报

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:45:00.000Z",
  "report_type": "screenshot_taken",
  "operation_id": "screenshot_1737283500000_123",
  "data": {
    "status": "success",
    "message": "Screenshot taken and uploaded successfully.",
    "image_url": "http://server.com/screenshots/screenshot_1737283500000_123.png"
  }
}
```

### 8. 程序当前正在打开的文件上报

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:47:30.456Z",
  "report_type": "current_open_file",
  "operation_id": "current_open_file_1737283650456_789",
  "data": {
    "file_path": "/storage/emulated/0/Android/data/com.mingsign.esop_client/files/videos/training_video.mp4",
    "file_name": "training_video.mp4",
    "file_type": "mp4",
    "file_size": 15728640,
    "status": "opened",
    "viewer_type": "video_player",
    "open_method": "in_app",
    "additional_info": {
      "source": "mqtt_command",
      "action": "auto_download_and_open"
    }
  }
}
```

#### 不同文件类型的上报示例

**视频文件上报：**
```json
{
  "report_type": "current_open_file",
  "data": {
    "file_path": "/storage/files/video.mp4",
    "file_name": "video.mp4",
    "file_type": "mp4",
    "file_size": 15728640,
    "status": "opened",
    "viewer_type": "video_player",
    "open_method": "in_app",
    "additional_info": {
      "source": "file_directory_sidebar",
      "action": "user_click"
    }
  }
}
```

**图片文件上报：**
```json
{
  "report_type": "current_open_file",
  "data": {
    "file_path": "/storage/files/image.jpg",
    "file_name": "image.jpg",
    "file_type": "jpg",
    "file_size": 2048000,
    "status": "opened",
    "viewer_type": "image_viewer",
    "open_method": "in_app",
    "additional_info": {
      "source": "download_queue",
      "action": "user_click"
    }
  }
}
```

**PDF文件上报：**
```json
{
  "report_type": "current_open_file",
  "data": {
    "file_path": "/storage/files/manual.pdf",
    "file_name": "manual.pdf",
    "file_type": "pdf",
    "file_size": 5242880,
    "status": "opened",
    "viewer_type": "pdf_viewer",
    "open_method": "in_app",
    "additional_info": {
      "source": "mqtt_command",
      "action": "auto_download_and_open"
    }
  }
}
```

**HTML文件（ZIP包）上报：**
```json
{
  "report_type": "current_open_file",
  "data": {
    "file_path": "/storage/extracted/index.html",
    "file_name": "content.zip",
    "file_type": "zip",
    "file_size": 10485760,
    "status": "opened",
    "viewer_type": "webview",
    "open_method": "in_app",
    "additional_info": {
      "source": "mqtt_command",
      "action": "auto_download_and_open",
      "extracted_path": "/storage/extracted/",
      "html_file": "/storage/extracted/index.html"
    }
  }
}
```

**系统应用打开文档上报：**
```json
{
  "report_type": "current_open_file",
  "data": {
    "file_path": "/storage/files/document.docx",
    "file_name": "document.docx",
    "file_type": "docx",
    "file_size": 1048576,
    "status": "opened",
    "viewer_type": "system_app",
    "open_method": "external",
    "additional_info": {
      "source": "mqtt_command",
      "action": "auto_download_and_open"
    }
  }
}
```

### 8. 程序当前正在打开的文件上报

#### 8.1 视频文件打开

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:47:30.456Z",
  "report_type": "current_open_file",
  "operation_id": "open_file_1737283650456_789",
  "data": {
    "file_path": "/storage/emulated/0/Android/data/com.mingsign.esop_client/files/videos/training.mp4",
    "file_name": "training.mp4",
    "file_type": "mp4",
    "file_size": 15728640,
    "status": "opened",
    "viewer_type": "video_player",
    "open_method": "in_app"
  }
}
```

#### 8.2 图片文件打开

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:48:15.123Z",
  "report_type": "current_open_file",
  "operation_id": "open_file_1737283695123_012",
  "data": {
    "file_path": "/storage/emulated/0/Android/data/com.mingsign.esop_client/files/images/diagram.png",
    "file_name": "diagram.png",
    "file_type": "png",
    "file_size": 2048000,
    "status": "opened",
    "viewer_type": "image_viewer",
    "open_method": "in_app"
  }
}
```

#### 8.3 PDF文件打开

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:49:00.789Z",
  "report_type": "current_open_file",
  "operation_id": "open_file_1737283740789_345",
  "data": {
    "file_path": "/storage/emulated/0/Android/data/com.mingsign.esop_client/files/documents/manual.pdf",
    "file_name": "manual.pdf",
    "file_type": "pdf",
    "file_size": 5242880,
    "status": "opened",
    "viewer_type": "pdf_viewer",
    "open_method": "in_app"
  }
}
```

#### 8.4 HTML文件（ZIP包解压后）打开

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:50:30.456Z",
  "report_type": "current_open_file",
  "operation_id": "open_file_1737283830456_678",
  "data": {
    "file_path": "/storage/emulated/0/Android/data/com.mingsign.esop_client/files/extracted/presentation/index.html",
    "file_name": "presentation.zip",
    "file_type": "zip",
    "file_size": 10485760,
    "status": "opened",
    "viewer_type": "webview",
    "open_method": "in_app",
    "additional_info": {
      "extracted_path": "/storage/emulated/0/Android/data/com.mingsign.esop_client/files/extracted/presentation",
      "index_file": "/storage/emulated/0/Android/data/com.mingsign.esop_client/files/extracted/presentation/index.html"
    }
  }
}
```

#### 8.5 其他文档文件（通过系统应用打开）

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:51:45.123Z",
  "report_type": "current_open_file",
  "operation_id": "open_file_1737283905123_901",
  "data": {
    "file_path": "/storage/emulated/0/Android/data/com.mingsign.esop_client/files/documents/report.xlsx",
    "file_name": "report.xlsx",
    "file_type": "xlsx",
    "file_size": 3145728,
    "status": "opened",
    "viewer_type": "system_app",
    "open_method": "external"
  }
}
```

## 批量上报接口

### 接口信息

- **接口路径**: `/v1/equipment/reported/batch`
- **请求方法**: `POST`
- **内容类型**: `application/json`

### 请求格式

```json
{
  "reports": [
    {
      // 单个上报数据，格式同上
    },
    {
      // 另一个上报数据
    }
    // ... 更多上报数据
  ]
}
```

## MQTT 指令格式

### 截屏指令 (type: 6)

```json
{
  "type": 6,
  "group_name": "Production-Line-A",
  "equipment_alias_name": "Terminal-001",
  "upload_url": "http://your-backend.com/api/v1/screenshot/upload"
}
```

## 响应格式

### 成功响应

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "received_count": 1,
    "processed_count": 1
  }
}
```

### 错误响应

```json
{
  "code": 1,
  "message": "error message",
  "data": null
}
```

## 注意事项

1. **时间戳格式**: 所有时间戳都使用 ISO 8601 格式的 UTC 时间
2. **操作ID**: 每个操作都有唯一的操作ID，格式为 `{prefix}_{timestamp}_{random}`
3. **文件大小**: 以字节为单位
4. **持续时间**: 以秒为单位，支持小数
5. **状态值**: 通常为 `success`、`failed`、`started` 等
6. **批量上报**: 建议每次批量上报不超过50条记录
7. **重试机制**: 客户端应实现重试机制，失败的上报数据会重新加入队列

## 数据字段说明

### 公共字段

- `mac_address`: 设备的MAC地址，用于唯一标识设备
- `registration_code`: 设备注册码
- `device_alias`: 设备别名，用户可配置
- `group_name`: 设备所属组名
- `timestamp`: 上报时间戳
- `report_type`: 上报类型，见上表
- `operation_id`: 操作唯一标识，用于追踪和去重
- `data`: 具体的上报数据，根据类型不同而不同

### data字段详细说明
### data字段详细说明

根据不同的 `report_type`，`data` 字段包含不同的内容。具体字段说明请参考上述示例。

#### current_open_file 字段说明

当 `report_type` 为 `current_open_file` 时，`data` 字段包含以下内容：

- `file_path`: 文件在设备上的完整路径
- `file_name`: 文件名（包含扩展名）
- `file_type`: 文件类型扩展名（如：mp4, jpg, pdf, zip等）
- `file_size`: 文件大小（字节）
- `status`: 文件状态，固定为 "opened"
- `viewer_type`: 查看器类型
  - `video_player`: 视频播放器
  - `image_viewer`: 图片查看器
  - `pdf_viewer`: PDF查看器
  - `webview`: WebView查看器（用于HTML内容）
  - `system_app`: 系统应用
- `open_method`: 打开方式
  - `in_app`: 应用内打开
  - `external`: 外部应用打开
- `additional_info`: 附加信息（可选），包含以下字段：
  - `source`: 触发来源
    - `mqtt_command`: MQTT指令触发
    - `file_directory_sidebar`: 文件目录侧边栏
    - `download_queue`: 下载队列
    - `auto_open_last_file`: 自动打开上次文件
  - `action`: 触发动作
    - `auto_download_and_open`: 自动下载并打开
    - `user_click`: 用户点击
    - `system_auto`: 系统自动
  - `extracted_path`: 解压路径（仅ZIP文件）
  - `html_file`: HTML文件路径（仅ZIP文件）

#### 上报时机说明

`current_open_file` 上报会在以下时机触发：

1. **MQTT指令处理完成后**：接收到MQTT指令，文件下载完成并成功打开时
2. **文件目录侧边栏**：用户从文件目录中点击文件打开时
3. **下载队列**：用户从下载队列中点击已完成的文件打开时
4. **自动打开上次文件**：应用启动时自动打开上次查看的文件时
5. **各查看器屏幕**：文件在各个查看器中成功加载完成时
   - 视频播放器初始化完成
   - 图片查看器加载完成
   - PDF查看器加载完成
   - WebView加载完成

#### 支持的文件类型

- **视频文件**: mp4, avi, mkv, mov, wmv 等
- **图片文件**: jpg, jpeg, png, gif, bmp 等
- **PDF文件**: pdf
- **HTML内容**: zip（包含HTML文件的压缩包）
- **办公文档**: doc, docx, ppt, pptx, xls, xlsx 等（通过系统应用打开）

#### current_open_file 数据字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file_path | string | 是 | 文件的本地路径 |
| file_name | string | 是 | 文件名（包含扩展名） |
| file_type | string | 是 | 文件类型扩展名（如：mp4, pdf, png等） |
| file_size | number | 是 | 文件大小（字节） |
| status | string | 是 | 固定值："opened" |
| viewer_type | string | 是 | 查看器类型，可选值：<br/>- video_player: 视频播放器<br/>- image_viewer: 图片查看器<br/>- pdf_viewer: PDF查看器<br/>- webview: WebView查看器<br/>- system_app: 系统应用 |
| open_method | string | 是 | 打开方式，可选值：<br/>- in_app: 应用内打开<br/>- external: 外部应用打开 |
| additional_info | object | 否 | 附加信息，根据文件类型可能包含不同字段 |

#### additional_info 可能包含的字段

**对于ZIP文件（HTML内容）：**
- `extracted_path`: 解压路径
- `index_file`: index.html文件路径

**对于URL来源的视频：**
- `source_type`: "url"
- `url`: 原始URL地址

## 上报时机说明

### current_open_file 上报时机

1. **视频文件**: 视频引擎初始化完成并开始播放时
2. **图片文件**: 图片查看器屏幕初始化时
3. **PDF文件**: PDF文档加载完成时
4. **HTML文件（ZIP包）**: ZIP解压完成并找到index.html文件时
5. **WebView内容**: WebView加载完成时
6. **其他文档文件**: 通过系统应用成功打开时

### 上报流程

```
MQTT消息接收 → 文件下载 → 文件处理 → 文件打开 → 上报current_open_file
```

每个文件类型在成功打开后都会触发 `current_open_file` 上报，确保服务器能够准确了解终端设备当前正在查看的内容。
