# 设备控制 YsApi 集成说明

## 概述

本文档描述了在设备控制功能中集成 ysapi.jar 作为保底操作方法的实现。当常规的重启/关机方法失败时，系统会自动尝试使用 ysapi.jar 提供的 API 方法。

## 功能特性

### 重启功能保底机制

重启操作按以下优先级顺序执行：

1. **方法1**: 使用 su 权限执行 `reboot` 命令
2. **方法2**: 直接执行 `reboot` 命令
3. **方法3**: 使用 su 权限执行 `sync && reboot` 命令
4. **方法4**: **保底方法** - 使用 ysapi.jar 的 API

### 关机功能保底机制

关机操作按以下优先级顺序执行：

1. **方法1**: 使用 su 权限执行 `shutdown -p` 命令
2. **方法2**: 使用 su 权限执行 `reboot -p` 命令
3. **方法3**: 直接执行 `shutdown -h now` 命令
4. **方法4**: **保底方法** - 使用 ysapi.jar 的 API

## 技术实现

### Android 端实现

在 `DeviceControlHelper.kt` 中添加了以下保底方法：

#### 重启保底方法
```kotlin
private fun executeYsApiReboot(): Boolean {
    return try {
        Log.d(TAG, "尝试使用ysapi.jar执行重启")

        // 通过反射获取ysapi类和方法
        val ysApiClass = Class.forName("com.ys.api.YsApi")
        val rebootMethod = ysApiClass.getMethod("reboot")

        // 创建实例并调用重启方法
        val ysApiInstance = ysApiClass.newInstance()
        rebootMethod.invoke(ysApiInstance)

        Log.d(TAG, "ysapi.jar重启方法调用成功")
        true
    } catch (e: Exception) {
        Log.e(TAG, "使用ysapi.jar重启失败", e)
        false
    }
}
```

#### 关机保底方法
```kotlin
private fun executeYsApiShutdown(): Boolean {
    return try {
        Log.d(TAG, "尝试使用ysapi.jar执行关机")

        // 通过反射获取ysapi类和方法
        val ysApiClass = Class.forName("com.ys.api.YsApi")
        val shutdownMethod = ysApiClass.getMethod("shutdown")

        // 创建实例并调用关机方法
        val ysApiInstance = ysApiClass.newInstance()
        shutdownMethod.invoke(ysApiInstance)

        Log.d(TAG, "ysapi.jar关机方法调用成功")
        true
    } catch (e: Exception) {
        Log.e(TAG, "使用ysapi.jar关机失败", e)
        false
    }
}
```

### Flutter 端实现

在 `DeviceControlService.dart` 中更新了错误处理逻辑，提供更详细的错误信息：

- 当检测到 ysapi 相关错误时，会提示用户常规方法和保底方法都失败了
- 为其他错误情况添加了保底方法尝试的说明

## 依赖配置

### build.gradle.kts 配置

确保 ysapi.jar 已正确引入：

```kotlin
dependencies {
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))
}
```

### 文件结构

```
android/app/libs/
├── ysapi.jar                 # YsApi 库文件
└── ysapi开发文档.pdf         # YsApi 技术文档
```

## 错误处理

### 可能的异常情况

1. **ClassNotFoundException**: ysapi.jar 未正确加载
2. **NoSuchMethodException**: ysapi 中未找到对应的方法
3. **其他反射异常**: 方法调用过程中的异常

### 错误日志

系统会记录详细的错误日志，包括：
- 每个方法的尝试结果
- ysapi 调用的成功/失败状态
- 具体的异常信息

## 使用方式

### 在代码中调用

```dart
// 重启设备
try {
  await deviceControlService.reboot();
  print('设备重启成功');
} catch (e) {
  print('设备重启失败: $e');
}

// 关机设备
try {
  await deviceControlService.shutdown();
  print('设备关机成功');
} catch (e) {
  print('设备关机失败: $e');
}
```

### 错误信息示例

- `重启失败：常规方法和ysapi.jar保底方法都失败了。请检查设备权限或联系技术支持。`
- `关机失败：设备可能没有root权限或su命令不可用。已尝试使用ysapi.jar保底方法。`

## 注意事项

1. **权限要求**: ysapi.jar 可能需要特定的系统权限
2. **设备兼容性**: 不同设备对 ysapi 的支持可能不同
3. **错误处理**: 建议在UI层面提供友好的错误提示
4. **日志监控**: 建议监控 ysapi 保底方法的使用频率，以评估常规方法的可靠性

## 测试建议

1. **功能测试**: 在不同权限级别下测试重启/关机功能
2. **异常测试**: 模拟 ysapi.jar 缺失或损坏的情况
3. **性能测试**: 测试保底方法的响应时间
4. **兼容性测试**: 在不同Android版本和设备上测试

## 维护说明

- 定期检查 ysapi.jar 的版本更新
- 监控错误日志，及时发现问题
- 根据实际使用情况调整重试策略