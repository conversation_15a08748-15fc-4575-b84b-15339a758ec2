# 视频播放监控使用指南

## 概述
本指南介绍如何使用修复后的视频播放器监控功能，以及如何通过日志了解播放器的运行状况。

## 日志监控

### 启用日志
确保在应用中正确配置了logger服务：

```dart
import '../services/logger_service.dart';
```

### 关键日志信息

#### 正常播放日志
```
D/MediaKitVideoPlayer: 播放状态变化: true
D/MediaKitVideoCarousel: 播放状态变化: true
```

#### 错误和重启日志
```
E/MediaKitVideoPlayer: 播放错误: [错误详情]
W/MediaKitVideoPlayer: 播放位置停在0:00，尝试重启播放器
I/MediaKitVideoPlayer: 重启播放器
```

#### 健康检查日志
```
W/MediaKitVideoPlayer: 播放位置长时间未更新，尝试重启播放器
W/MediaKitVideoCarousel: 播放器多次停止，尝试重启
```

## 监控指标

### 播放器健康状况指标
1. **播放位置更新频率** - 正常情况下应该持续更新
2. **重启频率** - 偶尔重启是正常的，频繁重启需要关注
3. **错误发生率** - 错误应该很少发生
4. **播放状态稳定性** - 播放状态应该保持稳定

### 异常情况识别

#### 高频重启
如果日志中频繁出现重启信息（每分钟多次），可能表明：
- 视频文件损坏
- 硬件解码问题
- 内存不足
- 网络连接不稳定（对于网络视频）

#### 播放位置卡住
如果经常出现"播放位置停在0:00"的警告：
- 检查视频文件格式兼容性
- 验证硬件解码支持
- 检查设备性能

## 性能优化建议

### 1. 视频文件优化
- 使用设备支持的视频格式
- 控制视频分辨率和码率
- 确保视频文件完整性

### 2. 硬件配置
- 确保足够的内存
- 检查硬件解码支持
- 优化设备散热

### 3. 应用配置
- 合理设置监控间隔
- 根据设备性能调整重启阈值
- 定期清理缓存

## 故障排除

### 问题：视频频繁重启
**可能原因：**
- 视频文件问题
- 硬件兼容性问题
- 内存不足

**解决方案：**
1. 检查视频文件格式和编码
2. 尝试降低视频质量
3. 重启设备释放内存
4. 检查硬件解码设置

### 问题：播放位置始终为0:00
**可能原因：**
- 视频解码失败
- 文件路径错误
- 权限问题

**解决方案：**
1. 验证文件路径正确性
2. 检查文件访问权限
3. 尝试不同的视频文件
4. 检查media_kit配置

### 问题：播放器无响应
**可能原因：**
- 主线程阻塞
- 资源耗尽
- 系统级问题

**解决方案：**
1. 检查应用性能
2. 重启应用
3. 检查系统资源使用情况

## 自定义监控参数

如果需要调整监控参数，可以修改以下常量：

### MediaKitVideoPlayer
```dart
// 位置检查间隔
Timer.periodic(const Duration(seconds: 5), ...)

// 健康检查间隔  
Timer.periodic(const Duration(seconds: 10), ...)

// 位置卡住阈值
if (timeSinceLastUpdate.inSeconds > 10)

// 健康检查阈值
if (timeSinceLastUpdate.inSeconds > 15)
```

### MediaKitVideoCarousel
```dart
// 位置检查间隔
Timer.periodic(const Duration(seconds: 8), ...)

// 健康检查间隔
Timer.periodic(const Duration(seconds: 15), ...)

// 位置卡住阈值
if (timeSinceLastUpdate.inSeconds > 15)

// 健康检查阈值
if (timeSinceLastUpdate.inSeconds > 20)
```

## 最佳实践

1. **定期监控日志** - 建立日志监控机制
2. **性能测试** - 定期进行长时间播放测试
3. **版本控制** - 记录配置变更和效果
4. **用户反馈** - 收集实际使用中的问题反馈
5. **持续优化** - 根据监控数据持续优化参数
