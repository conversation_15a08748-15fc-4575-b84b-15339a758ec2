import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/download_queue_manager.dart';
import '../services/report_service.dart';
import '../services/service_locator.dart';
import '../utils/file_utils.dart';

class DownloadQueueWidget extends StatelessWidget {
  const DownloadQueueWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: DownloadQueueManager(),
      child: Consumer<DownloadQueueManager>(
        builder: (context, downloadManager, child) {
          final summary = downloadManager.getDownloadSummary();
          final hasActiveTasks =
              summary['downloading'] > 0 || summary['pending'] > 0;

          if (!hasActiveTasks && summary['total'] == 0) {
            return const SizedBox(width: 0, height: 0);
          }

          return Container(
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(context, summary),
                if (hasActiveTasks)
                  _buildActiveDownloads(context, downloadManager),
                if (summary['completed'] > 0)
                  _buildCompletedDownloads(context, downloadManager),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader(BuildContext context, Map<String, dynamic> summary) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.blue,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.download, color: Colors.white),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '下载队列 (${summary['downloading']}/${summary['total']})',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          if (summary['completed'] > 0)
            TextButton(
              onPressed: () {
                DownloadQueueManager().clearCompletedTasks();
              },
              child: const Text('清除已完成', style: TextStyle(color: Colors.white)),
            ),
        ],
      ),
    );
  }

  Widget _buildActiveDownloads(
    BuildContext context,
    DownloadQueueManager manager,
  ) {
    final activeTasks = [
      ...manager.activeDownloads,
      ...manager.queue.where((task) => task.status == DownloadStatus.pending),
    ];

    if (activeTasks.isEmpty) {
      return const SizedBox(width: 0, height: 0);
    }

    return Column(
      children: activeTasks
          .map((task) => _buildDownloadItem(context, task, manager))
          .toList(),
    );
  }

  Widget _buildCompletedDownloads(
    BuildContext context,
    DownloadQueueManager manager,
  ) {
    final completedTasks = manager.completedDownloads;

    if (completedTasks.isEmpty) {
      return const SizedBox(width: 0, height: 0);
    }

    return ExpansionTile(
      title: Text('已完成 (${completedTasks.length})'),
      children: completedTasks
          .map((task) => _buildDownloadItem(context, task, manager))
          .toList(),
    );
  }

  Widget _buildDownloadItem(
    BuildContext context,
    DownloadTask task,
    DownloadQueueManager manager,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey.withOpacity(0.2))),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FileUtils.getFileIcon(task.file.name),
                size: 24,
                color: _getStatusColor(task.status),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      task.file.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${FileUtils.formatFileSize(task.file.size)} • ${_getStatusText(task.status)}',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
              _buildActionButton(context, task, manager),
            ],
          ),
          if (task.status == DownloadStatus.downloading) ...[
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: task.progress,
              backgroundColor: Colors.grey[300],
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
            const SizedBox(height: 4),
            Text(
              '${(task.progress * 100).toStringAsFixed(1)}%',
              style: TextStyle(fontSize: 11, color: Colors.grey[600]),
            ),
          ],
          if (task.error != null) ...[
            const SizedBox(height: 8),
            Text(
              '错误: ${task.error}',
              style: const TextStyle(fontSize: 12, color: Colors.red),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    DownloadTask task,
    DownloadQueueManager manager,
  ) {
    switch (task.status) {
      case DownloadStatus.downloading:
        return IconButton(
          icon: const Icon(Icons.cancel, color: Colors.red),
          onPressed: () => manager.cancelDownload(task.id),
          tooltip: '取消下载',
        );
      case DownloadStatus.failed:
        return IconButton(
          icon: const Icon(Icons.refresh, color: Colors.orange),
          onPressed: () => manager.retryDownload(task.id),
          tooltip: '重试下载',
        );
      case DownloadStatus.completed:
        return IconButton(
          icon: const Icon(Icons.open_in_new, color: Colors.green),
          onPressed: () => _openFile(context, task),
          tooltip: '打开文件',
        );
      default:
        return const SizedBox(width: 48);
    }
  }

  Color _getStatusColor(DownloadStatus status) {
    switch (status) {
      case DownloadStatus.pending:
        return Colors.grey;
      case DownloadStatus.downloading:
        return Colors.blue;
      case DownloadStatus.completed:
        return Colors.green;
      case DownloadStatus.failed:
        return Colors.red;
      case DownloadStatus.cancelled:
        return Colors.orange;
    }
  }

  String _getStatusText(DownloadStatus status) {
    switch (status) {
      case DownloadStatus.pending:
        return '等待中';
      case DownloadStatus.downloading:
        return '下载中';
      case DownloadStatus.completed:
        return '已完成';
      case DownloadStatus.failed:
        return '失败';
      case DownloadStatus.cancelled:
        return '已取消';
    }
  }

  void _openFile(BuildContext context, DownloadTask task) {
    // 这里需要调用现有的文件打开逻辑
    // 可以通过回调或事件总线来通知主界面打开文件
    debugPrint('打开文件: ${task.file.name}');

    // 上报程序当前正在打开的文件
    _reportCurrentOpenFile(task);

    // 可以发送一个事件或调用回调来通知主界面
    // 例如使用 Provider 或 EventBus
  }

  void _reportCurrentOpenFile(DownloadTask task) {
    try {
      final reportService = getIt<ReportService>();
      final fileType = FileUtils.getFileType(task.file.name);

      String viewerType;
      String openMethod;

      // 根据文件类型确定查看器类型和打开方式
      switch (fileType.toLowerCase()) {
        case 'mp4':
        case 'avi':
        case 'mkv':
        case 'mov':
        case 'wmv':
          viewerType = 'video_player';
          openMethod = 'in_app';
          break;
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
          viewerType = 'image_viewer';
          openMethod = 'in_app';
          break;
        case 'pdf':
          viewerType = 'pdf_viewer';
          openMethod = 'in_app';
          break;
        case 'zip':
          viewerType = 'webview';
          openMethod = 'in_app';
          break;
        default:
          viewerType = 'system_app';
          openMethod = 'external';
      }

      reportService.reportCurrentOpenFile(
        filePath: task.localPath ?? '/storage/downloads/${task.file.name}',
        fileName: task.file.name,
        fileType: fileType,
        fileSize: task.file.size,
        viewerType: viewerType,
        openMethod: openMethod,
        additionalInfo: {'source': 'download_queue', 'action': 'user_click'},
      );
    } catch (e) {
      debugPrint('上报文件打开信息失败: $e');
    }
  }
}

/// 简化的下载进度指示器，用于显示在状态栏或其他位置
class DownloadProgressIndicator extends StatelessWidget {
  const DownloadProgressIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: DownloadQueueManager(),
      child: Consumer<DownloadQueueManager>(
        builder: (context, downloadManager, child) {
          final summary = downloadManager.getDownloadSummary();
          final hasActiveDownloads = summary['downloading'] > 0;

          if (!hasActiveDownloads) {
            return const SizedBox(width: 0, height: 0);
          }

          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Colors.blue,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '下载中 ${summary['downloading']}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.blue,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
