import 'package:flutter/material.dart';
import 'file_directory_sidebar.dart';

class FloatingFileDirectoryButton extends StatefulWidget {
  final String equipmentId;

  const FloatingFileDirectoryButton({super.key, required this.equipmentId});

  @override
  State<FloatingFileDirectoryButton> createState() =>
      _FloatingFileDirectoryButtonState();
}

class _FloatingFileDirectoryButtonState
    extends State<FloatingFileDirectoryButton>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleSidebar() {
    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 侧边栏
        Positioned(
          top: 0,
          right: 0,
          bottom: 0,
          child: AnimatedBuilder(
            animation: _slideAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(_slideAnimation.value * 300, 0),
                child: _isExpanded
                    ? FileDirectorySidebar(
                        equipmentId: widget.equipmentId,
                        onClose: _toggleSidebar,
                      )
                    : const SizedBox.shrink(),
              );
            },
          ),
        ),
        // 浮动按钮
        Positioned(
          top: MediaQuery.of(context).padding.top + 60,
          right: _isExpanded ? 310 : 16,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            child: FloatingActionButton(
              onPressed: _toggleSidebar,
              backgroundColor: Colors.blue,
              child: AnimatedRotation(
                turns: _isExpanded ? 0.5 : 0,
                duration: const Duration(milliseconds: 300),
                child: Icon(
                  _isExpanded ? Icons.close : Icons.folder,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ),
        // 遮罩层
        if (_isExpanded)
          Positioned.fill(
            child: GestureDetector(
              onTap: _toggleSidebar,
              child: Container(
                color: Colors.black.withOpacity(0.3),
                margin: const EdgeInsets.only(right: 300),
              ),
            ),
          ),
      ],
    );
  }
}
