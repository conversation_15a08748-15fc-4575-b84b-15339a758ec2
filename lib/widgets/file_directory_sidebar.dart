import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/material_provider.dart';
import '../providers/settings_provider.dart';
import '../models/material_file_model.dart';
import '../providers/file_provider.dart';
import '../services/download_queue_manager.dart';
import '../services/report_service.dart';
import '../services/service_locator.dart';
import '../utils/file_utils.dart';

class FileDirectorySidebar extends StatefulWidget {
  final String equipmentId;
  final double width;
  final VoidCallback? onClose;

  const FileDirectorySidebar({
    super.key,
    required this.equipmentId,
    this.width = 300,
    this.onClose,
  });

  @override
  State<FileDirectorySidebar> createState() => _FileDirectorySidebarState();
}

class _FileDirectorySidebarState extends State<FileDirectorySidebar> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedCategory = 'all';
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MaterialProvider>().loadMaterials(widget.equipmentId);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(-2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildSearchBar(),
          _buildCategoryTabs(),
          Expanded(child: _buildFileList()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
      ),
      child: Row(
        children: [
          const Icon(Icons.folder, color: Colors.blue),
          const SizedBox(width: 8),
          const Expanded(
            child: Text(
              '文件目录',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          ),
          if (widget.onClose != null)
            IconButton(
              icon: const Icon(Icons.close, size: 20),
              onPressed: widget.onClose,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(12),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: '搜索文件...',
          prefixIcon: const Icon(Icons.search, size: 20),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear, size: 20),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
          isDense: true,
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildCategoryTabs() {
    return Consumer<MaterialProvider>(
      builder: (context, provider, child) {
        final categories = [
          {'key': 'all', 'label': '全部', 'count': provider.materials.length},
          {'key': 'video', 'label': '视频', 'count': provider.videoFiles.length},
          {'key': 'image', 'label': '图片', 'count': provider.imageFiles.length},
          {'key': 'audio', 'label': '音频', 'count': provider.audioFiles.length},
          {
            'key': 'document',
            'label': '文档',
            'count': provider.documentFiles.length,
          },
        ];

        return Container(
          height: 40,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              final isSelected = _selectedCategory == category['key'];

              return Padding(
                padding: const EdgeInsets.only(right: 4),
                child: FilterChip(
                  label: Text(
                    '${category['label']} (${category['count']})',
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected ? Colors.white : Colors.grey.shade700,
                    ),
                  ),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      _selectedCategory = category['key'] as String;
                    });
                  },
                  selectedColor: Colors.blue,
                  backgroundColor: Colors.grey.shade100,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildFileList() {
    return Consumer<MaterialProvider>(
      builder: (context, provider, child) {
        if (provider.state == MaterialLoadingState.loading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('加载文件列表...'),
              ],
            ),
          );
        }

        if (provider.state == MaterialLoadingState.error) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, color: Colors.red, size: 48),
                const SizedBox(height: 16),
                Text(
                  provider.error,
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.red),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => provider.refreshMaterials(),
                  child: const Text('重试'),
                ),
              ],
            ),
          );
        }

        List<MaterialFileModel> filteredFiles = _getFilteredFiles(provider);

        if (filteredFiles.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.folder_open, color: Colors.grey, size: 48),
                SizedBox(height: 16),
                Text('没有找到文件', style: TextStyle(color: Colors.grey)),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => provider.refreshMaterials(),
          child: ListView.builder(
            padding: const EdgeInsets.all(8),
            itemCount: filteredFiles.length,
            itemBuilder: (context, index) {
              final file = filteredFiles[index];
              return _buildFileItem(file);
            },
          ),
        );
      },
    );
  }

  List<MaterialFileModel> _getFilteredFiles(MaterialProvider provider) {
    List<MaterialFileModel> files;

    switch (_selectedCategory) {
      case 'video':
        files = provider.videoFiles;
        break;
      case 'image':
        files = provider.imageFiles;
        break;
      case 'audio':
        files = provider.audioFiles;
        break;
      case 'document':
        files = provider.documentFiles;
        break;
      default:
        files = provider.materials;
    }

    if (_searchQuery.isNotEmpty) {
      files = files
          .where(
            (file) =>
                file.name.toLowerCase().contains(_searchQuery.toLowerCase()),
          )
          .toList();
    }

    return files;
  }

  Widget _buildFileItem(MaterialFileModel file) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.blue.shade50,
          child: Text(file.fileTypeIcon, style: const TextStyle(fontSize: 20)),
        ),
        title: Text(
          file.name,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              file.formattedSize,
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
            ),
            Text(
              _formatDate(file.createdAt),
              style: TextStyle(fontSize: 11, color: Colors.grey.shade500),
            ),
          ],
        ),
        trailing: const Icon(Icons.play_arrow, color: Colors.blue),
        onTap: () => _openFile(file),
        dense: true,
      ),
    );
  }

  String _formatDate(int timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  void _openFile(MaterialFileModel file) {
    // 构建文件URL
    final serverUrl = _buildServerUrl();

    if (serverUrl.isEmpty) {
      _showErrorDialog('服务器地址未配置');
      return;
    }

    // 使用下载队列管理器处理文件下载
    final downloadManager = DownloadQueueManager();

    downloadManager.addDownloadTask(
      file: file,
      serverUrl: serverUrl,
      onCompleted: () {
        // 小文件下载完成后的回调
        debugPrint('文件下载完成: ${file.name}');

        // 立即上报程序当前正在打开的文件
        debugPrint('FileDirSidebar: 立即上报文件打开');
        _reportCurrentOpenFile(file);
      },
      onError: (error) {
        // 下载失败的回调
        _showErrorDialog('下载失败: $error');
      },
    );

    // 如果是大文件，显示提示信息
    if (file.size > 10 * 1024 * 1024) {
      _showLargeFileDialog(file);
    }
  }

  void _reportCurrentOpenFile(MaterialFileModel file) {
    try {
      final reportService = getIt<ReportService>();
      final fileType = FileUtils.getFileType(file.name);

      String viewerType;
      String openMethod;

      // 根据文件类型确定查看器类型和打开方式
      switch (fileType.toLowerCase()) {
        case 'mp4':
        case 'avi':
        case 'mkv':
        case 'mov':
        case 'wmv':
          viewerType = 'video_player';
          openMethod = 'in_app';
          break;
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
          viewerType = 'image_viewer';
          openMethod = 'in_app';
          break;
        case 'pdf':
          viewerType = 'pdf_viewer';
          openMethod = 'in_app';
          break;
        case 'zip':
          viewerType = 'webview';
          openMethod = 'in_app';
          break;
        default:
          viewerType = 'system_app';
          openMethod = 'external';
      }

      reportService.reportCurrentOpenFile(
        filePath: '/storage/files/${file.name}', // 假设的本地路径
        fileName: file.name,
        fileType: fileType,
        fileSize: file.size,
        viewerType: viewerType,
        openMethod: openMethod,
        additionalInfo: {
          'source': 'file_directory_sidebar',
          'action': 'user_click',
        },
      );
    } catch (e) {
      debugPrint('上报文件打开信息失败: $e');
    }
  }

  String _buildServerUrl() {
    // 从设置中获取服务器地址和端口来构建服务器URL
    final settingsProvider = context.read<SettingsProvider>();
    final serverAddress = settingsProvider.settings.mqttServerAddress ?? '';
    final serverPort = settingsProvider.settings.serverPort ?? '8567';

    if (serverAddress.isEmpty) {
      return '';
    }

    // 构建基础URL
    String baseUrl = serverAddress.toLowerCase().startsWith('http')
        ? serverAddress
        : 'http://$serverAddress:$serverPort';

    return baseUrl.endsWith('/')
        ? baseUrl.substring(0, baseUrl.length - 1)
        : baseUrl;
  }

  String _buildFileUrl(String filePath) {
    final serverUrl = _buildServerUrl();

    if (serverUrl.isEmpty) {
      return filePath;
    }

    // 确保路径格式正确
    if (!filePath.startsWith('/')) {
      return '$serverUrl/files/$filePath';
    } else {
      return '$serverUrl/files$filePath';
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('错误'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showLargeFileDialog(MaterialFileModel file) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('大文件下载'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.info, color: Colors.blue, size: 48),
            const SizedBox(height: 16),
            Text('文件 "${file.name}" 较大 (${file.formattedSize})'),
            const SizedBox(height: 8),
            const Text('已加入下载队列，将在后台下载。\n下载完成后会自动通知您。'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showFileProcessingDialog(MaterialFileModel file) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Consumer<FileProvider>(
        builder: (context, provider, child) {
          return AlertDialog(
            title: Text('打开文件: ${file.name}'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (provider.state == FileOperationState.downloading)
                  Column(
                    children: [
                      LinearProgressIndicator(value: provider.downloadProgress),
                      const SizedBox(height: 8),
                      Text(
                        '下载中... ${(provider.downloadProgress * 100).toInt()}%',
                      ),
                      if (provider.downloadSpeed.isNotEmpty)
                        Text('速度: ${provider.downloadSpeed}'),
                    ],
                  )
                else if (provider.state == FileOperationState.extracting)
                  const Column(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 8),
                      Text('解压中...'),
                    ],
                  )
                else if (provider.state == FileOperationState.completed)
                  const Column(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green, size: 48),
                      SizedBox(height: 8),
                      Text('文件已打开'),
                    ],
                  )
                else if (provider.state == FileOperationState.error)
                  Column(
                    children: [
                      const Icon(Icons.error, color: Colors.red, size: 48),
                      const SizedBox(height: 8),
                      Text('错误: ${provider.error}'),
                    ],
                  )
                else
                  const Column(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 8),
                      Text('处理中...'),
                    ],
                  ),
              ],
            ),
            actions: [
              if (provider.state == FileOperationState.completed ||
                  provider.state == FileOperationState.error)
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('确定'),
                ),
            ],
          );
        },
      ),
    );
  }
}
