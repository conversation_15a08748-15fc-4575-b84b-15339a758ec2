import 'package:flutter/material.dart';
import '../services/volume_test_service.dart';
import '../services/device_control_service.dart';

class VolumeTestScreen extends StatefulWidget {
  const VolumeTestScreen({super.key});

  @override
  State<VolumeTestScreen> createState() => _VolumeTestScreenState();
}

class _VolumeTestScreenState extends State<VolumeTestScreen> {
  final VolumeTestService _volumeTestService = VolumeTestService();
  final DeviceControlService _deviceControlService = DeviceControlService();

  double _currentVolume = 50.0;
  String _testResult = '';
  bool _isTesting = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('音量控制测试'), backgroundColor: Colors.blue),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      '当前音量: ${_currentVolume.round()}%',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    Slider(
                      value: _currentVolume,
                      min: 0,
                      max: 100,
                      divisions: 20,
                      label: '${_currentVolume.round()}%',
                      onChanged: (value) {
                        setState(() {
                          _currentVolume = value;
                        });
                      },
                      onChangeEnd: (value) {
                        _setVolume(value.round());
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isTesting ? null : () => _setVolume(0),
                    child: const Text('静音'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isTesting ? null : () => _setVolume(50),
                    child: const Text('50%'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isTesting ? null : () => _setVolume(100),
                    child: const Text('最大'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isTesting ? null : _runVolumeTest,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: _isTesting
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text('测试中...'),
                      ],
                    )
                  : const Text('运行完整测试'),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '测试结果:',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _testResult.isEmpty ? '点击按钮开始测试' : _testResult,
                            style: const TextStyle(fontFamily: 'monospace'),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _setVolume(int volume) async {
    try {
      await _deviceControlService.setVolume(volume);
      setState(() {
        _currentVolume = volume.toDouble();
        _testResult += '设置音量到 $volume% - 成功\n';
      });
    } catch (e) {
      setState(() {
        _testResult += '设置音量到 $volume% - 失败: $e\n';
      });
    }
  }

  Future<void> _runVolumeTest() async {
    setState(() {
      _isTesting = true;
      _testResult = '开始音量控制测试...\n';
    });

    try {
      final success = await _volumeTestService.testVolumeControl();
      setState(() {
        _testResult += success ? '音量控制测试 - 通过\n' : '音量控制测试 - 失败\n';
      });

      final muteSuccess = await _volumeTestService.testMuteFunction();
      setState(() {
        _testResult += muteSuccess ? '静音功能测试 - 通过\n' : '静音功能测试 - 失败\n';
      });

      setState(() {
        _testResult += '\n测试完成！\n';
      });
    } catch (e) {
      setState(() {
        _testResult += '测试过程中发生错误: $e\n';
      });
    } finally {
      setState(() {
        _isTesting = false;
      });
    }
  }
}
