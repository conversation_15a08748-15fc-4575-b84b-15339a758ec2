import 'dart:io';
import 'package:flutter/material.dart';
import 'package:pdfx/pdfx.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../services/orientation_service.dart';
import '../services/report_service.dart';
import '../services/file_viewer_manager.dart';

class PdfViewerScreen extends StatefulWidget {
  final File pdfFile;

  const PdfViewerScreen({super.key, required this.pdfFile});

  @override
  State<PdfViewerScreen> createState() => _PdfViewerScreenState();
}

class _PdfViewerScreenState extends State<PdfViewerScreen> {
  late PdfController _pdfController;
  DateTime? _loadStartTime;

  final ReportService _reportService = ReportService();
  final FileViewerManager _viewerManager = FileViewerManager();

  @override
  void initState() {
    super.initState();
    _loadStartTime = DateTime.now();
    _initializePdfController();
    _applyOrientationSetting();
  }

  Future<void> _initializePdfController() async {
    try {
      // 上报PDF预览开始
      _reportService.reportDocumentPreviewStarted(
        filePath: widget.pdfFile.path,
        fileType: 'pdf',
        fileSize: widget.pdfFile.lengthSync(),
        previewMethod: 'in_app',
      );

      _pdfController = PdfController(
        document: PdfDocument.openFile(widget.pdfFile.path),
      );

      // 上报PDF加载完成
      // 上报PDF加载完成
      if (_loadStartTime != null) {
        final loadDuration =
            DateTime.now().difference(_loadStartTime!).inMilliseconds / 1000.0;
        _reportService.reportDocumentPreviewCompleted(
          filePath: widget.pdfFile.path,
          fileType: 'pdf',
          fileSize: widget.pdfFile.lengthSync(),
          previewMethod: 'in_app',
          loadDuration: loadDuration,
        );

        // 上报程序当前正在打开的文件
        debugPrint('PdfViewerScreen: 立即上报PDF文件打开');
        _reportService.reportCurrentOpenFile(
          filePath: widget.pdfFile.path,
          fileName: widget.pdfFile.path.split('/').last,
          fileType: 'pdf',
          fileSize: widget.pdfFile.lengthSync(),
          viewerType: 'pdf_viewer',
          openMethod: 'in_app',
        );
      }
    } catch (error) {
      debugPrint('Error loading PDF: $error');
      // 上报PDF加载失败
      _reportService.reportDocumentPreviewFailed(
        filePath: widget.pdfFile.path,
        fileType: 'pdf',
        fileSize: widget.pdfFile.lengthSync(),
        previewMethod: 'in_app',
        errorMessage: error.toString(),
      );
    }
  }

  // Apply orientation setting from settings
  Future<void> _applyOrientationSetting() async {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final orientation =
        settingsProvider.settings.screenOrientation ?? 'landscape';
    await OrientationService.applyOrientation(orientation);
  }

  @override
  void dispose() {
    _pdfController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PdfView(
        controller: _pdfController,
        onPageChanged: (page) {
          // page is 1-based
        },
        onDocumentError: (error) {
          debugPrint('Error while rendering document: $error');
        },
      ),
    );
  }
}
