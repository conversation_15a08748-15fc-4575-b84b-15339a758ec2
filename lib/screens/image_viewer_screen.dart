import 'dart:io';

import 'package:flutter/material.dart';
import '../services/report_service.dart';

class ImageViewerScreen extends StatefulWidget {
  final File imageFile;

  const ImageViewerScreen({super.key, required this.imageFile});

  @override
  State<ImageViewerScreen> createState() => _ImageViewerScreenState();
}

class _ImageViewerScreenState extends State<ImageViewerScreen> {
  final ReportService _reportService = ReportService();

  @override
  void initState() {
    super.initState();
    _reportCurrentOpenFile();
  }

  void _reportCurrentOpenFile() async {
    try {
      debugPrint('ImageViewerScreen: 立即上报图片文件打开');
      await _reportService.reportCurrentOpenFile(
        filePath: widget.imageFile.path,
        fileName: widget.imageFile.path.split('/').last,
        fileType: widget.imageFile.path.split('.').last.toLowerCase(),
        fileSize: widget.imageFile.lengthSync(),
        viewerType: 'image_viewer',
        openMethod: 'in_app',
      );
    } catch (e) {
      debugPrint('Error reporting current open file: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Image.file(
          widget.imageFile,
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
        ),
      ),
    );
  }
}
