import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:marquee/marquee.dart';
import 'package:path/path.dart' as p;

import '../models/template_model.dart';
import '../services/logger_service.dart';

class NativeRenderScreen extends StatefulWidget {
  final String? dataFilePath; // Path to the data.json file

  const NativeRenderScreen({super.key, this.dataFilePath});

  @override
  State<NativeRenderScreen> createState() => _NativeRenderScreenState();
}

class _NativeRenderScreenState extends State<NativeRenderScreen> {
  TemplateData? _templateData;
  Size? _canvasSize;
  String _currentTime = '';
  Timer? _timer;
  String? _assetBaseDir;

  @override
  void initState() {
    super.initState();
    _loadTemplateData();
    _startTimer();
  }

  @override
  void didUpdateWidget(NativeRenderScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当数据文件路径发生变化时，重新加载数据
    logger.i(
      'NativeRenderScreen: didUpdateWidget 触发, 旧路径: ${oldWidget.dataFilePath}, 新路径: ${widget.dataFilePath}',
    );

    if (oldWidget.dataFilePath != widget.dataFilePath) {
      logger.i('NativeRenderScreen: 数据文件路径发生变化，重新加载数据');
      _loadTemplateData();
    } else {
      logger.d('NativeRenderScreen: 数据文件路径未变化，跳过重新加载');
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _currentTime = DateFormat('HH:mm:ss').format(DateTime.now());
        });
      }
    });
  }

  Future<void> _loadTemplateData() async {
    final filePath = widget.dataFilePath;
    logger.i('NativeRenderScreen: 开始加载模板数据: $filePath');

    // 设置加载状态
    if (mounted) {
      setState(() {
        _templateData = null;
        _canvasSize = null;
        _assetBaseDir = null;
      });
    }

    try {
      String response;
      if (filePath != null) {
        // Load from file system
        final file = File(filePath);
        if (!await file.exists()) {
          throw Exception('数据文件不存在: $filePath');
        }

        logger.i('NativeRenderScreen: 从文件系统加载: $filePath');
        response = await file.readAsString();
        // The asset directory is relative to the data.json file
        _assetBaseDir = p.join(p.dirname(filePath), 'assets');
        logger.i('NativeRenderScreen: 资源目录: $_assetBaseDir');
      } else {
        // Fallback to bundled demo data
        logger.i('NativeRenderScreen: 加载示例数据');
        response = await rootBundle.loadString('demo/data.json');
        _assetBaseDir = 'demo/assets';
      }

      final data = await json.decode(response);
      if (mounted) {
        setState(() {
          _templateData = TemplateData.fromJson(data);
          if (_templateData != null) {
            final canvasParts = _templateData!.canvasRatio.split('x');
            _canvasSize = Size(
              double.parse(canvasParts[0]),
              double.parse(canvasParts[1]),
            );
            logger.i(
              'NativeRenderScreen: 模板数据加载成功, Canvas尺寸: $_canvasSize, 组件数量: ${_templateData!.templateSm.length}',
            );
          }
        });
      }
    } catch (e) {
      // 处理加载错误
      logger.e('NativeRenderScreen: 加载模板数据失败: $e');
      if (mounted) {
        setState(() {
          _templateData = null;
          _canvasSize = null;
          _assetBaseDir = null;
        });
      }
    }
  }

  String _getAssetPath(String relativePath) {
    if (_assetBaseDir == null) return '';
    return p.join(_assetBaseDir!, relativePath);
  }

  // Helper to build image widget, deciding between file and asset
  Widget _buildImage(String relativePath, {BoxFit fit = BoxFit.fill}) {
    if (widget.dataFilePath != null) {
      return Image.file(File(_getAssetPath(relativePath)), fit: fit);
    }
    return Image.asset(_getAssetPath(relativePath), fit: fit);
  }

  // Helper to build media object for media_kit
  Media _buildMedia(String relativePath) {
    if (widget.dataFilePath != null) {
      return Media(_getAssetPath(relativePath));
    }
    return Media('asset://${_getAssetPath(relativePath)}');
  }

  @override
  Widget build(BuildContext context) {
    if (_templateData == null || _canvasSize == null || _assetBaseDir == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    final screenSize = MediaQuery.of(context).size;
    final widthScale = screenSize.width / _canvasSize!.width;
    final heightScale = screenSize.height / _canvasSize!.height;

    _templateData!.templateSm.sort(
      (a, b) => a.templateIndex.compareTo(b.templateIndex),
    );

    return Scaffold(
      body: Stack(
        children: _templateData!.templateSm.map((item) {
          if (item.templateSmType == 3) {
            return Positioned.fill(child: _buildBackground(item));
          } else {
            return Positioned(
              left: item.xAxis * widthScale,
              top: item.yAxis * heightScale,
              width: item.width == 0 ? null : item.width * widthScale,
              height: item.height == 0 ? null : item.height * heightScale,
              child: _buildWidget(item, widthScale, heightScale),
            );
          }
        }).toList(),
      ),
    );
  }

  Widget _buildWidget(TemplateSm item, double widthScale, double heightScale) {
    switch (item.templateSmType) {
      case 1: // Material
        if (item.multiFiles.isNotEmpty) {
          return _buildCarousel(item, widthScale, heightScale);
        }
        return _buildSimpleMaterial(item.path);
      case 2: // Time
        return _buildTimeWidget(item, widthScale, heightScale);
      case 6: // Marquee
        return _buildMarqueeWidget(item, widthScale, heightScale);
      default:
        return const SizedBox.shrink();
    }
  }

  BoxFit _getBoxFit(String? displayMode) {
    switch (displayMode) {
      case 'cover':
        return BoxFit.cover;
      case 'fill':
        return BoxFit.fill;
      case 'contain':
        return BoxFit.contain;
      case 'fitWidth':
        return BoxFit.fitWidth;
      case 'fitHeight':
        return BoxFit.fitHeight;
      case 'none':
        return BoxFit.none;
      case 'scaleDown':
        return BoxFit.scaleDown;
      default:
        return BoxFit.cover;
    }
  }

  Widget _buildBackground(TemplateSm item) {
    return _buildImage(item.path, fit: _getBoxFit(item.backgroundDisplay));
  }

  Widget _buildSimpleMaterial(String path) {
    final isVideo = path.toLowerCase().endsWith('.mp4');
    if (isVideo) {
      return MediaKitVideoPlayer(media: _buildMedia(path));
    }
    return _buildImage(path, fit: BoxFit.fill);
  }

  Widget _buildCarousel(
    TemplateSm item,
    double widthScale,
    double heightScale,
  ) {
    final isVideo = item.multiFiles.first.type == 2;
    if (isVideo) {
      return MediaKitVideoCarousel(
        files: item.multiFiles,
        buildMedia: _buildMedia,
      );
    }
    return ImageCarouselWidget(files: item.multiFiles, buildImage: _buildImage);
  }

  Color _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) {
      return Colors.white;
    }
    try {
      if (colorString.startsWith('#')) {
        return Color(
          int.parse(colorString.substring(1), radix: 16) + 0xFF000000,
        );
      }
    } catch (e) {
      // Ignore and return default
    }
    return Colors.white;
  }

  Color _parseBackgroundColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) {
      return Colors.transparent;
    }
    try {
      // 处理 rgba 格式
      if (colorString.startsWith('rgba(')) {
        final rgbaMatch = RegExp(
          r'rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)',
        ).firstMatch(colorString);
        if (rgbaMatch != null) {
          final r = int.parse(rgbaMatch.group(1)!);
          final g = int.parse(rgbaMatch.group(2)!);
          final b = int.parse(rgbaMatch.group(3)!);
          final a = double.parse(rgbaMatch.group(4)!);
          return Color.fromRGBO(r, g, b, a);
        }
      }
      // 处理 # 格式
      if (colorString.startsWith('#')) {
        return Color(
          int.parse(colorString.substring(1), radix: 16) + 0xFF000000,
        );
      }
    } catch (e) {
      // Ignore and return default
    }
    return Colors.transparent;
  }

  Widget _buildTimeWidget(
    TemplateSm item,
    double widthScale,
    double heightScale,
  ) {
    final color = _parseColor(item.fontColor);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(5),
      ),
      alignment: Alignment.center,
      child: Text(
        _currentTime,
        style: TextStyle(
          fontSize: item.height * heightScale * 0.6,
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildMarqueeWidget(
    TemplateSm item,
    double widthScale,
    double heightScale,
  ) {
    final textColor = _parseColor(item.fontColor);
    final backgroundColor = _parseBackgroundColor(item.backgroundColor);
    final fontSize = (item.fontSize ?? 20).toDouble() * heightScale;
    final scrollSpeed = (item.scrollSpeed ?? 50).toDouble();
    final marqueeText = item.marqueeText ?? '';

    // 如果文本为空，返回空容器
    if (marqueeText.isEmpty) {
      return Container(decoration: BoxDecoration(color: backgroundColor));
    }

    // 根据滚动速度计算每像素的时间
    // scrollSpeed 范围是 1-100，转换为像素/秒的速度
    // 速度1 = 30像素/秒，速度100 = 300像素/秒（优化性能）
    final pixelsPerSecond = scrollSpeed; // 线性映射到 30-300 范围
    debugPrint('pixelsPerSecond: $pixelsPerSecond|| scrollSpeed: $scrollSpeed');
    // Marquee插件使用velocity参数控制速度，单位是像素/秒
    final velocity = pixelsPerSecond;

    return Container(
      decoration: BoxDecoration(color: backgroundColor),
      child: marqueeText.length < 20
          ? Text(
              marqueeText,
              style: TextStyle(
                fontSize: fontSize,
                color: textColor,
                fontWeight: FontWeight.normal,
              ),
              maxLines: 1,
            )
          : Marquee(
              text: marqueeText,
              style: TextStyle(
                fontSize: fontSize,
                color: textColor,
                fontWeight: FontWeight.normal,
              ),
              scrollAxis: Axis.horizontal,
              crossAxisAlignment: CrossAxisAlignment.start,
              blankSpace: 20.0,
              velocity: velocity,
              pauseAfterRound: const Duration(seconds: 1),
              startPadding: 10.0,
              accelerationDuration: const Duration(milliseconds: 800),
              accelerationCurve: Curves.linear,
              decelerationDuration: const Duration(milliseconds: 400),
              decelerationCurve: Curves.easeOut,
            ),
    );
  }
}

// --- Carousel and Video Player Widgets ---

class ImageCarouselWidget extends StatefulWidget {
  final List<MultiFile> files;
  final Widget Function(String, {BoxFit fit}) buildImage;
  const ImageCarouselWidget({
    super.key,
    required this.files,
    required this.buildImage,
  });

  @override
  State<ImageCarouselWidget> createState() => _ImageCarouselWidgetState();
}

class _ImageCarouselWidgetState extends State<ImageCarouselWidget> {
  late PageController _pageController;
  Timer? _carouselTimer;
  int _currentPage = 5000;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _currentPage);
    if (widget.files.isNotEmpty) {
      _startCarousel();
    }
  }

  void _startCarousel() {
    _carouselTimer?.cancel();
    if (widget.files.isEmpty) return;

    final realIndex = _currentPage % widget.files.length;
    _carouselTimer = Timer.periodic(
      Duration(seconds: widget.files[realIndex].intervalTime),
      (timer) {
        if (!mounted) return;
        _currentPage++;
        _pageController.jumpToPage(_currentPage);
      },
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _carouselTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.files.isEmpty) {
      return const SizedBox.shrink();
    }
    return PageView.builder(
      controller: _pageController,
      itemCount: 100000,
      itemBuilder: (context, index) {
        final realIndex = index % widget.files.length;
        return widget.buildImage(
          widget.files[realIndex].path,
          fit: BoxFit.fill,
        );
      },
      onPageChanged: (index) {
        if (mounted) {
          _currentPage = index;
          _startCarousel();
        }
      },
    );
  }
}

class MediaKitVideoCarousel extends StatefulWidget {
  final List<MultiFile> files;
  final Media Function(String) buildMedia;
  const MediaKitVideoCarousel({
    super.key,
    required this.files,
    required this.buildMedia,
  });

  @override
  State<MediaKitVideoCarousel> createState() => _MediaKitVideoCarouselState();
}

class _MediaKitVideoCarouselState extends State<MediaKitVideoCarousel> {
  Player? _player;
  VideoController? _controller;
  Timer? _healthCheckTimer;
  Timer? _positionCheckTimer;
  Duration _lastPosition = Duration.zero;
  DateTime _lastPositionUpdate = DateTime.now();
  int _stuckCount = 0;
  bool _isDisposed = false;
  bool _isRestarting = false;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() {
    if (_isDisposed || _isRestarting) return;

    // 清理旧的播放器资源
    _cleanupPlayer();

    _player = Player();
    _controller = VideoController(
      _player!,
      configuration: const VideoControllerConfiguration(
        hwdec: 'no', // 强制使用软件解码，避免绿色花屏
        vo: 'gpu',
        enableHardwareAcceleration: false,
      ),
    );

    _setupPlayerListeners();

    if (widget.files.isNotEmpty) {
      final playlist = Playlist(
        widget.files.map((file) => widget.buildMedia(file.path)).toList(),
      );
      _player!.setPlaylistMode(PlaylistMode.loop);
      _player!.setVolume(0.0);
      _player!.open(playlist, play: true);
    }

    _startHealthCheck();
  }

  void _setupPlayerListeners() {
    if (_player == null || _isDisposed) return;

    // 监听播放错误
    _player!.stream.error.listen((error) {
      if (!_isDisposed && !_isRestarting) {
        logger.e('MediaKitVideoCarousel: 播放错误: $error');
        _handlePlaybackError();
      }
    });

    // 监听播放状态
    _player!.stream.playing.listen((isPlaying) {
      if (!_isDisposed && !_isRestarting) {
        logger.d('MediaKitVideoCarousel: 播放状态变化: $isPlaying');
        if (!isPlaying) {
          _stuckCount++;
          if (_stuckCount > 8) {
            // 轮播视频允许更多次停止，减少过度敏感的重启
            logger.w('MediaKitVideoCarousel: 播放器多次停止，尝试重启');
            _restartPlayer();
          }
        } else {
          _stuckCount = 0;
        }
      }
    });

    // 监听播放位置
    _player!.stream.position.listen((position) {
      if (!_isDisposed && !_isRestarting) {
        _lastPosition = position;
        _lastPositionUpdate = DateTime.now();
      }
    });
  }

  void _cleanupPlayer() {
    try {
      _player?.stop();
    } catch (e) {
      logger.d('MediaKitVideoCarousel: 停止播放器时出现异常: $e');
    }

    try {
      _player?.dispose();
    } catch (e) {
      logger.d('MediaKitVideoCarousel: 销毁播放器时出现异常: $e');
    }

    _player = null;
    _controller = null;
  }

  void _startHealthCheck() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
      if (_isDisposed) {
        timer.cancel();
        return;
      }
      _checkPlaybackHealth();
    });

    _positionCheckTimer?.cancel();
    _positionCheckTimer = Timer.periodic(const Duration(seconds: 8), (timer) {
      if (_isDisposed) {
        timer.cancel();
        return;
      }
      _checkPositionStuck();
    });
  }

  void _checkPlaybackHealth() {
    final timeSinceLastUpdate = DateTime.now().difference(_lastPositionUpdate);
    // 轮播视频允许更长的无响应时间，优化健康检查间隔
    if (timeSinceLastUpdate.inSeconds > 35) {
      logger.w('MediaKitVideoCarousel: 播放位置长时间未更新，尝试重启播放器');
      _restartPlayer();
    }
  }

  void _checkPositionStuck() {
    if (_lastPosition == Duration.zero) {
      final timeSinceLastUpdate = DateTime.now().difference(
        _lastPositionUpdate,
      );
      // 轮播视频允许更多初始化时间，减少误判重启
      if (timeSinceLastUpdate.inSeconds > 30) {
        logger.w('MediaKitVideoCarousel: 播放位置停在0:00，尝试重启播放器');
        _restartPlayer();
      }
    }
  }

  void _handlePlaybackError() {
    logger.w('MediaKitVideoCarousel: 处理播放错误，尝试重启播放器');
    _restartPlayer();
  }

  Future<void> _restartPlayer() async {
    if (_isDisposed || _isRestarting) return;

    _isRestarting = true;

    try {
      logger.i('MediaKitVideoCarousel: 重启播放器');

      // 停止健康检查
      _healthCheckTimer?.cancel();
      _positionCheckTimer?.cancel();

      // 清理旧的播放器资源
      _cleanupPlayer();

      // 等待更长时间再重新初始化，避免频繁重启
      await Future.delayed(const Duration(milliseconds: 500));

      // 重新初始化播放器
      if (mounted && !_isDisposed) {
        _initializePlayer();
      }
    } catch (e) {
      logger.e('MediaKitVideoCarousel: 重启播放器失败: $e');
    } finally {
      _isRestarting = false;
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _healthCheckTimer?.cancel();
    _positionCheckTimer?.cancel();
    _cleanupPlayer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Video(
      controller: _controller ?? VideoController(Player()),
      fit: BoxFit.cover, // 改为 cover 以铺满屏幕并居中内容
    );
  }
}

class MediaKitVideoPlayer extends StatefulWidget {
  final Media media;
  const MediaKitVideoPlayer({super.key, required this.media});

  @override
  State<MediaKitVideoPlayer> createState() => _MediaKitVideoPlayerState();
}

class _MediaKitVideoPlayerState extends State<MediaKitVideoPlayer> {
  Player? _player;
  VideoController? _controller;
  Timer? _healthCheckTimer;
  Timer? _positionCheckTimer;
  Duration _lastPosition = Duration.zero;
  DateTime _lastPositionUpdate = DateTime.now();
  int _stuckCount = 0;
  bool _isDisposed = false;
  bool _isRestarting = false;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() {
    if (_isDisposed || _isRestarting) return;

    // 清理旧的播放器资源
    _cleanupPlayer();

    _player = Player();
    _controller = VideoController(
      _player!,
      configuration: const VideoControllerConfiguration(
        hwdec: 'no', // 强制使用软件解码，避免绿色花屏
        vo: 'gpu',
        enableHardwareAcceleration: false,
      ),
    );

    _setupPlayerListeners();
    _player!.setPlaylistMode(PlaylistMode.single);
    _player!.setVolume(0.0);
    _player!.open(widget.media, play: true);

    _startHealthCheck();
  }

  void _setupPlayerListeners() {
    if (_player == null || _isDisposed) return;

    // 监听播放错误
    _player!.stream.error.listen((error) {
      if (!_isDisposed && !_isRestarting) {
        logger.e('MediaKitVideoPlayer: 播放错误: $error');
        _handlePlaybackError();
      }
    });

    // 监听播放状态
    _player!.stream.playing.listen((isPlaying) {
      if (!_isDisposed && !_isRestarting) {
        logger.d('MediaKitVideoPlayer: 播放状态变化: $isPlaying');
        if (!isPlaying) {
          _stuckCount++;
          if (_stuckCount > 6) {
            // 提高停止阈值，减少过度敏感的重启
            logger.w('MediaKitVideoPlayer: 播放器多次停止，尝试重启');
            _restartPlayer();
          }
        } else {
          _stuckCount = 0;
        }
      }
    });

    // 监听播放位置
    _player!.stream.position.listen((position) {
      if (!_isDisposed && !_isRestarting) {
        _lastPosition = position;
        _lastPositionUpdate = DateTime.now();
      }
    });
  }

  void _cleanupPlayer() {
    try {
      _player?.stop();
    } catch (e) {
      logger.d('MediaKitVideoPlayer: 停止播放器时出现异常: $e');
    }

    try {
      _player?.dispose();
    } catch (e) {
      logger.d('MediaKitVideoPlayer: 销毁播放器时出现异常: $e');
    }

    _player = null;
    _controller = null;
  }

  void _startHealthCheck() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (_isDisposed) {
        timer.cancel();
        return;
      }
      _checkPlaybackHealth();
    });

    _positionCheckTimer?.cancel();
    _positionCheckTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_isDisposed) {
        timer.cancel();
        return;
      }
      _checkPositionStuck();
    });
  }

  void _checkPlaybackHealth() {
    final timeSinceLastUpdate = DateTime.now().difference(_lastPositionUpdate);
    // 单个视频播放优化健康检查间隔，减少误判
    if (timeSinceLastUpdate.inSeconds > 20) {
      logger.w('MediaKitVideoPlayer: 播放位置长时间未更新，尝试重启播放器');
      _restartPlayer();
    }
  }

  void _checkPositionStuck() {
    if (_lastPosition == Duration.zero) {
      final timeSinceLastUpdate = DateTime.now().difference(
        _lastPositionUpdate,
      );
      // 单个视频播放允许更多初始化时间
      if (timeSinceLastUpdate.inSeconds > 15) {
        logger.w('MediaKitVideoPlayer: 播放位置停在0:00，尝试重启播放器');
        _restartPlayer();
      }
    }
  }

  void _handlePlaybackError() {
    logger.w('MediaKitVideoPlayer: 处理播放错误，尝试重启播放器');
    _restartPlayer();
  }

  Future<void> _restartPlayer() async {
    if (_isDisposed || _isRestarting) return;

    _isRestarting = true;

    try {
      logger.i('MediaKitVideoPlayer: 重启播放器');

      // 停止健康检查
      _healthCheckTimer?.cancel();
      _positionCheckTimer?.cancel();

      // 清理旧的播放器资源
      _cleanupPlayer();

      // 等待更长时间再重新初始化，避免频繁重启
      await Future.delayed(const Duration(milliseconds: 500));

      // 重新初始化播放器
      if (mounted && !_isDisposed) {
        _initializePlayer();
      }
    } catch (e) {
      logger.e('MediaKitVideoPlayer: 重启播放器失败: $e');
    } finally {
      _isRestarting = false;
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _healthCheckTimer?.cancel();
    _positionCheckTimer?.cancel();
    _cleanupPlayer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Video(
      controller: _controller ?? VideoController(Player()),
      fit: BoxFit.cover, // 改为 cover 以铺满屏幕并居中内容
    );
  }
}
