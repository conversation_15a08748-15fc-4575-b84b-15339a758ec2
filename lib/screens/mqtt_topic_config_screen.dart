import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../utils/mqtt_topic_helper.dart';

class MqttTopicConfigScreen extends StatefulWidget {
  const MqttTopicConfigScreen({super.key});

  @override
  State<MqttTopicConfigScreen> createState() => _MqttTopicConfigScreenState();
}

class _MqttTopicConfigScreenState extends State<MqttTopicConfigScreen> {
  final TextEditingController _groupNameController = TextEditingController();
  final TextEditingController _deviceAliasController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadCurrentSettings();
  }

  void _loadCurrentSettings() {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    _groupNameController.text = settingsProvider.settings.groupName ?? '';
    _deviceAliasController.text = settingsProvider.settings.deviceAlias ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('MQTT Topic 配置')),
      body: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          final currentTopic = MqttTopicHelper.getCurrentTopic(
            settingsProvider,
          );

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '当前 MQTT Topic:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            currentTopic,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                TextField(
                  controller: _groupNameController,
                  decoration: const InputDecoration(
                    labelText: '组名 (Group Name)',
                    hintText: '请输入组名',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: _deviceAliasController,
                  decoration: const InputDecoration(
                    labelText: '设备别名 (Device Alias)',
                    hintText: '请输入设备别名',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: settingsProvider.isLoading
                        ? null
                        : () => _updateTopicSettings(),
                    child: settingsProvider.isLoading
                        ? const CircularProgressIndicator()
                        : const Text('更新并重新连接 MQTT'),
                  ),
                ),
                const SizedBox(height: 16),
                const Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '说明:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          '• 新的 MQTT Topic 格式: esop/\${group_name}/\${alias_name}',
                        ),
                        Text('• 如果组名或设备别名为空，将使用默认 topic'),
                        Text('• 更新后会自动重新连接 MQTT 服务器'),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Future<void> _updateTopicSettings() async {
    final groupName = _groupNameController.text.trim();
    final deviceAlias = _deviceAliasController.text.trim();

    if (groupName.isEmpty || deviceAlias.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请填写组名和设备别名'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      await MqttTopicHelper.updateTopicAndReconnect(
        context: context,
        groupName: groupName,
        deviceAlias: deviceAlias,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('MQTT Topic 更新成功'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('更新失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  void dispose() {
    _groupNameController.dispose();
    _deviceAliasController.dispose();
    super.dispose();
  }
}
