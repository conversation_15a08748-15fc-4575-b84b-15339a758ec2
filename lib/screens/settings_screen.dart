import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../providers/localization_provider.dart';
import '../l10n/app_localizations_extension.dart';
import '../services/broadcast_service.dart';
import '../services/equipment_api_service.dart';
import '../services/orientation_service.dart';
import '../utils/ui_utils.dart';
import '../utils/app_version_utils.dart';
import 'dart:io';
import 'package:flutter/services.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _mqttServerController = TextEditingController();
  final _apiServerController = TextEditingController();
  final _mqttPortController = TextEditingController();
  final _groupNameController = TextEditingController();
  final _deviceAliasController = TextEditingController();
  final _registrationCodeController = TextEditingController();
  final BroadcastService _broadcastService = BroadcastService();
  late final SettingsProvider _settingsProvider;

  String _selectedLanguage = 'zh';
  String _selectedOrientation = 'landscape';
  bool _isDiscovering = false;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    UiUtils.setFullscreenMode();

    _settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    _loadSettings();

    _settingsProvider.addListener(_onSettingsChanged);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 总是尝试发现服务器，不管当前是否有服务器地址
      _discoverServer();
    });
  }

  void _loadSettings() {
    final settings = _settingsProvider.settings;
    _mqttServerController.text = settings.mqttServerAddress ?? '';
    _apiServerController.text = settings.serverPort ?? '8567';
    _mqttPortController.text = settings.mqttPort ?? '1883';
    _groupNameController.text = settings.groupName ?? '';
    _deviceAliasController.text = settings.deviceAlias ?? '';
    _registrationCodeController.text = settings.registrationCode ?? '';

    setState(() {
      _selectedLanguage = settings.languageCode ?? 'zh';
      _selectedOrientation = settings.screenOrientation ?? 'landscape';
    });
  }

  void _onSettingsChanged() {
    // 在保存或发现过程中跳过自动更新，避免冲突
    if (_isSaving || _isDiscovering) return;

    final settings = _settingsProvider.settings;
    bool changed = false;

    // 只在值真正不同时才更新UI控件
    if (_mqttServerController.text != (settings.mqttServerAddress ?? '')) {
      _mqttServerController.text = settings.mqttServerAddress ?? '';
      changed = true;
      debugPrint('Updated server address in UI: ${settings.mqttServerAddress}');
    }

    if (_mqttPortController.text != (settings.mqttPort ?? '')) {
      _mqttPortController.text = settings.mqttPort ?? '';
      changed = true;
      debugPrint('Updated MQTT port in UI: ${settings.mqttPort}');
    }

    if (_registrationCodeController.text != (settings.registrationCode ?? '')) {
      _registrationCodeController.text = settings.registrationCode ?? '';
      changed = true;
      debugPrint(
        'Updated registration code in UI: ${settings.registrationCode}',
      );
    }

    if (_groupNameController.text != (settings.groupName ?? '')) {
      _groupNameController.text = settings.groupName ?? '';
      changed = true;
    }

    if (_deviceAliasController.text != (settings.deviceAlias ?? '')) {
      _deviceAliasController.text = settings.deviceAlias ?? '';
      changed = true;
    }

    if (changed && mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _settingsProvider.removeListener(_onSettingsChanged);
    _mqttServerController.dispose();
    _apiServerController.dispose();
    _mqttPortController.dispose();
    _groupNameController.dispose();
    _deviceAliasController.dispose();
    _registrationCodeController.dispose();
    _broadcastService.close();
    super.dispose();
  }

  Future<void> _discoverServer() async {
    if (_isDiscovering) return;

    debugPrint('Settings screen: Starting server discovery...');
    setState(() {
      _isDiscovering = true;
    });

    try {
      final discoveryResult = await _broadcastService.listenForBroadcast(
        timeout: const Duration(seconds: 15),
      );

      debugPrint(
        'Settings screen: Discovery completed with result: $discoveryResult',
      );

      if (!mounted) return;

      setState(() {
        _isDiscovering = false;
      });

      if (discoveryResult != null) {
        debugPrint(
          'Settings screen: Processing discovery result: $discoveryResult',
        );

        // 直接更新UI控件，不依赖provider的监听器
        final serverAddress = discoveryResult['serverAddress'] ?? '';
        final mqttPort = discoveryResult['mqttPort'] ?? '';

        // 立即更新UI控件
        setState(() {
          _mqttServerController.text = serverAddress;
          _mqttPortController.text = mqttPort;
        });

        debugPrint(
          'Settings screen: UI controllers updated - Server: $serverAddress, Port: $mqttPort',
        );

        // 然后更新provider中的设置（异步进行，不阻塞UI）
        _settingsProvider
            .updateSettingsFromBroadcast(discoveryResult)
            .then((_) {
              debugPrint('Settings screen: Settings provider updated');
            })
            .catchError((error) {
              debugPrint(
                'Settings screen: Error updating settings provider: $error',
              );
            });

        // 显示成功消息
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('服务器发现成功！已自动填充配置信息。'),
              duration: Duration(seconds: 3),
              backgroundColor: Colors.green,
            ),
          );
        }

        // 延迟后自动提交设置
        await Future.delayed(const Duration(seconds: 2));
        if (mounted && serverAddress.isNotEmpty) {
          debugPrint('Settings screen: Auto-submitting settings...');
          _submit(isAutoDiscovered: true);
        }
      } else {
        debugPrint('Settings screen: Discovery failed - no result received');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.l10n.discoveryFailed),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Settings screen: Discovery error: $e');
      if (mounted) {
        setState(() {
          _isDiscovering = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('服务器发现失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<bool> _saveSettings() async {
    final localizationProvider = Provider.of<LocalizationProvider>(
      context,
      listen: false,
    );

    await _settingsProvider.updateMqttServerAddress(_mqttServerController.text);
    await _settingsProvider.updateServerPort(_apiServerController.text);
    await _settingsProvider.updateMqttPort(_mqttPortController.text);
    await _settingsProvider.updateGroupName(_groupNameController.text);
    await _settingsProvider.updateDeviceAlias(_deviceAliasController.text);
    await _settingsProvider.updateRegistrationCode(
      _registrationCodeController.text,
    );

    if (_selectedLanguage != _settingsProvider.settings.languageCode) {
      await _settingsProvider.updateLanguageCode(_selectedLanguage);
      await localizationProvider.changeLocale(Locale(_selectedLanguage));
    }

    if (_selectedOrientation != _settingsProvider.settings.screenOrientation) {
      await _settingsProvider.updateScreenOrientation(_selectedOrientation);
      await OrientationService.applyOrientation(_selectedOrientation);
    }
    return true;
  }

  Future<void> _submit({bool isAutoDiscovered = false}) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    final apiService = EquipmentApiService();
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final settings = _settingsProvider.settings;

    final response = await apiService.addEquipment(
      serverAddress: _mqttServerController.text,
      serverPort: _apiServerController.text,
      deviceAlias: _deviceAliasController.text,
      macAddress: settings.macAddress ?? '',
      groupName: _groupNameController.text,
      aliasName: _deviceAliasController.text,
      registrationCode: _registrationCodeController.text,
    );

    if (!mounted) return;

    if (response['code'] == 0) {
      final saved = await _saveSettings();
      if (saved && mounted) {
        if (!isAutoDiscovered) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(context.l10n.settingsSaved)));
        }
        apiService.sendHeartbeat(settingsProvider: settingsProvider);
        Navigator.of(context).pop();
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '${context.l10n.registrationFailed}: ${response['message']}',
          ),
        ),
      );
    }

    if (mounted) {
      setState(() {
        _isSaving = false;
      });
    }
  }

  Future<void> _confirmExitApp() async {
    final shouldExit = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('确定要退出应用吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('退出', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (shouldExit == true) {
      _exitApp();
    }
  }

  void _exitApp() {
    try {
      if (Platform.isAndroid) {
        SystemNavigator.pop();
      } else {
        // 其他平台（如Windows、macOS）直接退出进程
        exit(0);
      }
    } catch (e) {
      // 兜底处理
      exit(0);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            title: Text(context.l10n.settings),
            toolbarHeight: 48, // 减少AppBar高度
          ),
          body: Consumer<SettingsProvider>(
            builder: (context, settingsProvider, child) {
              return Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 8), // 减少左右边距
                child: Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 将语言和方向选择合并到一行
                        _buildCompactSelections(),
                        const SizedBox(height: 8),

                        // 服务器配置区域
                        _buildSectionTitle('服务器配置'),
                        const SizedBox(height: 4),
                        _buildCompactTextFormField(
                          controller: _mqttServerController,
                          labelText: context.l10n.mqttServerAddress,
                          hintText: _isDiscovering
                              ? context.l10n.discovering
                              : context.l10n.mqttServerAddressHint,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return context.l10n.pleaseEnterMqttServerAddress;
                            }
                            return null;
                          },
                          suffixIcon: IconButton(
                            icon: _isDiscovering
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  )
                                : const Icon(Icons.search, size: 20),
                            onPressed: _discoverServer,
                          ),
                        ),

                        // 端口配置使用两列布局
                        Row(
                          children: [
                            Expanded(
                              child: _buildCompactTextFormField(
                                controller: _apiServerController,
                                labelText: context.l10n.apiServerPort,
                                hintText: context.l10n.apiServerPortHint,
                                keyboardType: TextInputType.number,
                                validator: _validatePort,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildCompactTextFormField(
                                controller: _mqttPortController,
                                labelText: context.l10n.mqttPort,
                                hintText: context.l10n.mqttPortHint,
                                keyboardType: TextInputType.number,
                                validator: _validatePort,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 8),
                        _buildSectionTitle('设备配置'),
                        const SizedBox(height: 4),

                        _buildCompactTextFormField(
                          controller: _groupNameController,
                          labelText: context.l10n.groupName,
                          hintText: context.l10n.groupNameHint,
                        ),
                        _buildCompactTextFormField(
                          controller: _deviceAliasController,
                          labelText: context.l10n.deviceAlias,
                          hintText: context.l10n.deviceAliasHint,
                        ),

                        // MAC地址显示
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4.0),
                          child: TextFormField(
                            initialValue:
                                settingsProvider.settings.macAddress ??
                                context.l10n.loading,
                            decoration: InputDecoration(
                              labelText: context.l10n.macAddress,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                              isDense: true,
                            ),
                            readOnly: true,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),

                        const SizedBox(height: 12),
                        // 保存按钮
                        SizedBox(
                          width: double.infinity,
                          height: 44,
                          child: ElevatedButton(
                            onPressed: settingsProvider.isLoading
                                ? null
                                : _submit,
                            style: ElevatedButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: settingsProvider.isLoading
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  )
                                : Text(
                                    context.l10n.saveSettings,
                                    style: const TextStyle(fontSize: 16),
                                  ),
                          ),
                        ),

                        const SizedBox(height: 8),
                        // 退出应用按钮
                        SizedBox(
                          width: double.infinity,
                          height: 44,
                          child: ElevatedButton.icon(
                            onPressed: _confirmExitApp,
                            icon: const Icon(
                              Icons.exit_to_app,
                              color: Colors.white,
                            ),
                            label: const Text(
                              '退出应用',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color.fromARGB(
                                255,
                                255,
                                163,
                                156,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),

                        if (settingsProvider.error.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: Text(
                              settingsProvider.error,
                              style: const TextStyle(
                                color: Colors.red,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        const SizedBox(height: 8), // 底部留白
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        // 浮动版本号显示在屏幕右下角
        Positioned(bottom: 16, right: 16, child: _buildFloatingVersionNumber()),
      ],
    );
  }

  // 浮动版本号组件
  Widget _buildFloatingVersionNumber() {
    return FutureBuilder<String>(
      future: AppVersionUtils.getFullVersion(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            decoration: BoxDecoration(
              color: Colors.black54,
              // borderRadius: BorderRadius.circular(12),
            ),
          );
        }

        if (snapshot.hasError) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: const Color(0xCCFF0000),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              '版本获取失败',
              style: TextStyle(color: Colors.white, fontSize: 18),
            ),
          );
        }

        final version = snapshot.data ?? '0.0.0';
        return Text(
          'version: v$version',
          style: const TextStyle(
            decoration: TextDecoration.none,
            color: Colors.black54,
            fontSize: 14,
          ),
        );
      },
    );
  }

  // 紧凑的选择区域，将语言和方向选择合并
  Widget _buildCompactSelections() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 语言选择
          Text(
            context.l10n.language,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () => setState(() => _selectedLanguage = 'zh'),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 12,
                    ),
                    decoration: BoxDecoration(
                      color: _selectedLanguage == 'zh'
                          ? Colors.blue.shade50
                          : null,
                      border: Border.all(
                        color: _selectedLanguage == 'zh'
                            ? Colors.blue
                            : Colors.grey.shade300,
                      ),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _selectedLanguage == 'zh'
                              ? Icons.radio_button_checked
                              : Icons.radio_button_unchecked,
                          size: 16,
                          color: _selectedLanguage == 'zh'
                              ? Colors.blue
                              : Colors.grey,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          context.l10n.chinese,
                          style: const TextStyle(fontSize: 13),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: InkWell(
                  onTap: () => setState(() => _selectedLanguage = 'en'),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 12,
                    ),
                    decoration: BoxDecoration(
                      color: _selectedLanguage == 'en'
                          ? Colors.blue.shade50
                          : null,
                      border: Border.all(
                        color: _selectedLanguage == 'en'
                            ? Colors.blue
                            : Colors.grey.shade300,
                      ),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _selectedLanguage == 'en'
                              ? Icons.radio_button_checked
                              : Icons.radio_button_unchecked,
                          size: 16,
                          color: _selectedLanguage == 'en'
                              ? Colors.blue
                              : Colors.grey,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          context.l10n.english,
                          style: const TextStyle(fontSize: 13),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // 屏幕方向选择
          Text(
            context.l10n.screenOrientation,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () =>
                      setState(() => _selectedOrientation = 'landscape'),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 12,
                    ),
                    decoration: BoxDecoration(
                      color: _selectedOrientation == 'landscape'
                          ? Colors.blue.shade50
                          : null,
                      border: Border.all(
                        color: _selectedOrientation == 'landscape'
                            ? Colors.blue
                            : Colors.grey.shade300,
                      ),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _selectedOrientation == 'landscape'
                              ? Icons.radio_button_checked
                              : Icons.radio_button_unchecked,
                          size: 16,
                          color: _selectedOrientation == 'landscape'
                              ? Colors.blue
                              : Colors.grey,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          context.l10n.landscapeDisplay,
                          style: const TextStyle(fontSize: 13),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: InkWell(
                  onTap: () =>
                      setState(() => _selectedOrientation = 'portrait'),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 12,
                    ),
                    decoration: BoxDecoration(
                      color: _selectedOrientation == 'portrait'
                          ? Colors.blue.shade50
                          : null,
                      border: Border.all(
                        color: _selectedOrientation == 'portrait'
                            ? Colors.blue
                            : Colors.grey.shade300,
                      ),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _selectedOrientation == 'portrait'
                              ? Icons.radio_button_checked
                              : Icons.radio_button_unchecked,
                          size: 16,
                          color: _selectedOrientation == 'portrait'
                              ? Colors.blue
                              : Colors.grey,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          context.l10n.portraitDisplay,
                          style: const TextStyle(fontSize: 13),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),
        ],
      ),
    );
  }

  // 区域标题
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 15,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
    );
  }

  // 紧凑的文本输入框
  Widget _buildCompactTextFormField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    Widget? suffixIcon,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.0)),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
          isDense: true,
          suffixIcon: suffixIcon,
        ),
        validator: validator,
        style: const TextStyle(fontSize: 14),
      ),
    );
  }

  String? _validatePort(String? value) {
    if (value == null || value.isEmpty) {
      return context.l10n.pleaseEnterMqttPort;
    }
    try {
      final port = int.parse(value);
      if (port <= 0 || port > 65535) {
        return context.l10n.portNumberMustBeBetween;
      }
    } catch (e) {
      return context.l10n.pleaseEnterValidPort;
    }
    return null;
  }
}
