import 'package:flutter/material.dart';
import '../services/settings_service.dart';

class LocalizationProvider with ChangeNotifier {
  final SettingsService _settingsService = SettingsService();
  Locale _locale = const Locale('zh'); // Default to Chinese
  bool _isLoading = false;

  // Getters
  Locale get locale => _locale;
  bool get isLoading => _isLoading;

  // Initialize locale from settings
  Future<void> initLocale() async {
    _isLoading = true;
    notifyListeners();

    try {
      final settings = await _settingsService.loadSettings();
      if (settings.languageCode != null) {
        print(
          'Loading language from settings: ${settings.languageCode}',
        ); // Debug log
        _locale = Locale(settings.languageCode!);
      } else {
        print('No language setting found, defaulting to Chinese'); // Debug log
        _locale = const Locale('zh');
      }
    } catch (e) {
      print('Error loading language settings: $e'); // Debug log
      // Default to Chinese on error
      _locale = const Locale('zh');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Change locale
  Future<void> changeLocale(Locale locale) async {
    print(
      'Changing locale to: ${locale.languageCode}, current: ${_locale.languageCode}',
    ); // Debug log
    if (_locale.languageCode == locale.languageCode) {
      print(
        'Locale already set to ${locale.languageCode}, skipping',
      ); // Debug log
      return;
    }

    _isLoading = true;
    notifyListeners();

    try {
      // This is not ideal, but for now we will keep it to avoid major refactoring
      // In a better architecture, this should be handled by the UI calling both providers.
      final settings = await _settingsService.loadSettings();
      settings.languageCode = locale.languageCode;
      final success = await _settingsService.saveSettings(settings);
      if (success) {
        print(
          'Successfully saved language code: ${locale.languageCode}',
        ); // Debug log
        _locale = locale;
      } else {
        print('Failed to save language code'); // Debug log
      }
    } catch (e) {
      print('Error changing locale: $e'); // Debug log
      // Keep current locale on error
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
