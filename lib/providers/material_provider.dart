import 'package:flutter/foundation.dart';
import '../models/material_file_model.dart';
import '../services/material_api_service.dart';

enum MaterialLoadingState { idle, loading, loaded, error }

class MaterialProvider with ChangeNotifier {
  final MaterialApiService _materialApiService = MaterialApiService();

  MaterialLoadingState _state = MaterialLoadingState.idle;
  List<MaterialFileModel> _materials = [];
  String _error = '';
  String? _currentEquipmentId;

  // Getters
  MaterialLoadingState get state => _state;
  List<MaterialFileModel> get materials => _materials;
  String get error => _error;
  String? get currentEquipmentId => _currentEquipmentId;

  /// 加载材料文件列表
  Future<void> loadMaterials(String equipmentId) async {
    if (_state == MaterialLoadingState.loading) {
      return; // 防止重复加载
    }

    _state = MaterialLoadingState.loading;
    _error = '';
    _currentEquipmentId = equipmentId;
    notifyListeners();

    try {
      debugPrint('Loading materials for equipment ID: $equipmentId');
      final response = await _materialApiService.getMaterial(equipmentId);

      if (response.isSuccess) {
        _materials = response.data;
        _state = MaterialLoadingState.loaded;
        debugPrint(
          'Loaded ${_materials.length} materials for equipment $equipmentId',
        );
      } else {
        _error = response.message;
        _state = MaterialLoadingState.error;
        debugPrint('Failed to load materials: ${response.message}');
      }
    } catch (e) {
      _error = '加载材料时发生错误: $e';
      _state = MaterialLoadingState.error;
      debugPrint('Error loading materials: $e');
    }

    notifyListeners();
  }

  /// 刷新材料列表
  Future<void> refreshMaterials() async {
    if (_currentEquipmentId != null) {
      await loadMaterials(_currentEquipmentId!);
    }
  }

  /// 根据文件类型筛选材料
  List<MaterialFileModel> getMaterialsByType(String contentType) {
    return _materials
        .where((material) => material.contentType.startsWith(contentType))
        .toList();
  }

  /// 获取视频文件
  List<MaterialFileModel> get videoFiles => getMaterialsByType('video/');

  /// 获取图片文件
  List<MaterialFileModel> get imageFiles => getMaterialsByType('image/');

  /// 获取音频文件
  List<MaterialFileModel> get audioFiles => getMaterialsByType('audio/');

  /// 获取文档文件
  List<MaterialFileModel> get documentFiles =>
      _materials.where((material) => material.isDocument).toList();

  /// 清空数据
  void clear() {
    _state = MaterialLoadingState.idle;
    _materials = [];
    _error = '';
    _currentEquipmentId = null;
    notifyListeners();
  }

  /// 根据名称搜索文件
  List<MaterialFileModel> searchFiles(String query) {
    if (query.isEmpty) {
      return _materials;
    }

    return _materials
        .where(
          (material) =>
              material.name.toLowerCase().contains(query.toLowerCase()),
        )
        .toList();
  }
}
