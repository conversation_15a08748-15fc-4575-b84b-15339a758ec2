import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/mqtt_message_model.dart';
import '../models/mqtt_connection_state.dart';
import '../services/mqtt_service.dart';
import '../services/settings_service.dart';

class MqttProvider with ChangeNotifier {
  final MqttService _mqttService = MqttService();

  MqttConnectionState _connectionState = MqttConnectionState.disconnected;
  MqttConnectionState _previousConnectionState =
      MqttConnectionState.disconnected;
  String _error = '';
  StreamSubscription? _connectionSubscription;
  StreamSubscription? _messageSubscription;

  // Getters
  MqttConnectionState get connectionState => _connectionState;
  String get error => _error;
  bool get isConnected => _connectionState == MqttConnectionState.connected;
  bool get wasReconnected =>
      _previousConnectionState == MqttConnectionState.disconnected &&
      _connectionState == MqttConnectionState.connected;

  // Initialize MQTT service
  void initialize({dynamic fileProvider}) {
    // Set the FileProvider instance in MQTT service
    if (fileProvider != null) {
      _mqttService.setFileProvider(fileProvider);
    }
    // Listen for connection state changes
    _connectionSubscription = _mqttService.connectionState.listen((state) {
      _previousConnectionState = _connectionState;
      _connectionState = state;
      if (state == MqttConnectionState.error) {
        _error = 'MQTT connection error. See logs for details.';
      } else if (state == MqttConnectionState.connected) {
        _error = ''; // Clear error on successful connection
        // 如果是从断开状态重新连接，记录日志
        if (_previousConnectionState == MqttConnectionState.disconnected) {
          debugPrint('MQTT重新连接成功，使用新的topic');
        }
      }
      notifyListeners();
    });

    // 检查是否应该连接MQTT
    _checkAndConnect();
  }

  // 检查组名和设备别名是否存在，决定是否连接MQTT
  Future<void> _checkAndConnect() async {
    try {
      final settingsService = SettingsService();
      final settings = await settingsService.loadSettings();

      // 检查组名和设备别名是否都存在
      if (settings.groupName != null &&
          settings.groupName!.isNotEmpty &&
          settings.deviceAlias != null &&
          settings.deviceAlias!.isNotEmpty) {
        // 组名和设备别名都存在，连接MQTT
        debugPrint('组名和设备别名都存在，连接MQTT');
        connect();
      } else {
        // 组名或设备别名为空，不连接MQTT
        debugPrint('组名或设备别名为空，不连接MQTT');
        _connectionState = MqttConnectionState.disconnected;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('检查MQTT连接条件时出错: $e');
    }
  }

  // Connect to MQTT server
  Future<void> connect() async {
    try {
      await _mqttService.connect();
    } catch (e) {
      _error = 'Error connecting to MQTT server: $e';
      debugPrint(_error);
      notifyListeners();
    }
  }

  // Disconnect from MQTT server
  void disconnect() {
    try {
      _mqttService.disconnect();
    } catch (e) {
      _error = 'Error disconnecting from MQTT server: $e';
      debugPrint(_error);
      notifyListeners();
    }
  }

  // Listen for MQTT messages
  void listenForMessages(Function(MqttMessageModel) onMessage) {
    // Cancel previous subscription if exists
    _messageSubscription?.cancel();

    // Listen for messages
    _messageSubscription = _mqttService.messages.listen((message) {
      onMessage(message);
    });
  }

  // Dispose resources
  @override
  void dispose() {
    _connectionSubscription?.cancel();
    _messageSubscription?.cancel();
    _mqttService.dispose();
    super.dispose();
  }
}
