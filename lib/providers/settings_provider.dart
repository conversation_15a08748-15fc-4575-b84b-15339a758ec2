import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/settings_model.dart';
import '../services/settings_service.dart';
import '../services/mac_address_service.dart';
import '../services/service_locator.dart';
import '../providers/mqtt_provider.dart';

class SettingsProvider with ChangeNotifier {
  final SettingsService _settingsService = SettingsService();
  final MacAddressService _macAddressService = MacAddressService();
  SettingsModel _settings = SettingsModel();
  bool _isLoading = false;
  String _error = '';

  // Getters
  SettingsModel get settings => _settings;
  bool get isLoading => _isLoading;
  String get error => _error;

  // Initialize settings
  Future<void> initSettings() async {
    _setLoading(true);
    try {
      _settings = await _settingsService.loadSettings();
      _error = '';
    } catch (e) {
      _error = 'Error initializing settings: $e';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // Generic method to update settings
  Future<void> _updateSettings(
    Future<void> Function(SettingsModel) updateFunction,
  ) async {
    _setLoading(true);
    try {
      await updateFunction(_settings);
      final success = await _settingsService.saveSettings(_settings);
      if (success) {
        _error = '';
      } else {
        _error = 'Failed to save settings';
      }
    } catch (e) {
      _error = 'Error updating settings: $e';
      debugPrint(_error);
    } finally {
      _setLoading(false);
    }
  }

  // Update MQTT server address
  Future<void> updateMqttServerAddress(String address) async {
    await _updateSettings((s) async => s.mqttServerAddress = address);
  }

  // Update device alias
  Future<void> updateDeviceAlias(String alias) async {
    final String? oldAlias = _settings.deviceAlias;
    await _updateSettings((s) async => s.deviceAlias = alias);

    // 如果别名发生变化，重新连接MQTT以更新topic
    if (oldAlias != alias) {
      _reconnectMqtt();
    }
  }

  // Update device alias
  Future<void> updateEquipmentId(int equipmentId) async {
    await _updateSettings((s) async => s.equipmentId = equipmentId);
  }

  // Update language code
  Future<void> updateLanguageCode(String languageCode) async {
    await _updateSettings((s) async => s.languageCode = languageCode);
  }

  // Update MQTT port
  Future<void> updateMqttPort(String port) async {
    await _updateSettings((s) async => s.mqttPort = port);
  }

  // Update Server port
  Future<void> updateServerPort(String port) async {
    await _updateSettings((s) async => s.serverPort = port);
  }

  // Update MQTT topic
  Future<void> updateMqttTopic(String topic) async {
    await _updateSettings((s) async => s.mqttTopic = topic);
  }

  /// 更新组名和设备别名
  Future<void> updateGroupAndAlias(
    String? groupName,
    String? deviceAlias,
  ) async {
    final String? oldGroupName = _settings.groupName;
    final String? oldAlias = _settings.deviceAlias;

    await _updateSettings((s) async {
      s.groupName = groupName;
      s.deviceAlias = deviceAlias;
    });

    // 如果组名或别名发生变化，重新连接MQTT以更新topic
    if (oldGroupName != groupName || oldAlias != deviceAlias) {
      _reconnectMqtt();
    }
  }

  // Update group name
  Future<void> updateGroupName(String groupName) async {
    final String? oldGroupName = _settings.groupName;
    await _updateSettings((s) async => s.groupName = groupName);

    // 如果组名发生变化，重新连接MQTT以更新topic
    if (oldGroupName != groupName) {
      _reconnectMqtt();
    }
  }

  // 防止重复重连的标志
  bool _isReconnecting = false;

  // 重新连接MQTT以使用新的topic
  Future<void> _reconnectMqtt() async {
    // 如果已经在重连过程中，直接返回
    if (_isReconnecting) {
      debugPrint('MQTT重连已在进行中，跳过此次重连请求');
      return;
    }

    _isReconnecting = true;

    try {
      // 检查组名和设备别名是否都存在
      if (_settings.groupName == null ||
          _settings.groupName!.isEmpty ||
          _settings.deviceAlias == null ||
          _settings.deviceAlias!.isEmpty) {
        // 如果组名或设备别名为空，断开MQTT连接
        final mqttProvider = locator<MqttProvider>();
        mqttProvider.disconnect();
        debugPrint('组名或设备别名为空，断开MQTT连接');
        return;
      }

      // 获取MQTT提供者
      final mqttProvider = locator<MqttProvider>();

      // 断开连接
      mqttProvider.disconnect();

      // 等待足够长的时间确保断开连接完成
      await Future.delayed(const Duration(seconds: 3));

      // 重新连接
      await mqttProvider.connect();

      debugPrint('MQTT重新连接成功，使用新的topic: ${_settings.generateMqttTopic()}');
    } catch (e) {
      debugPrint('MQTT重新连接失败: $e');
    } finally {
      _isReconnecting = false;
    }
  }

  // Update screen orientation
  Future<void> updateScreenOrientation(String orientation) async {
    await _updateSettings((s) async => s.screenOrientation = orientation);
  }

  // Update registration code
  Future<void> updateRegistrationCode(String registrationCode) async {
    await _updateSettings((s) async => s.registrationCode = registrationCode);
  }

  Future<void> updateSettingsFromBroadcast(
    Map<String, String> broadcastData,
  ) async {
    debugPrint(
      'SettingsProvider: Updating settings from broadcast: $broadcastData',
    );

    await _updateSettings((s) async {
      // 更新服务器地址
      if (broadcastData.containsKey('serverAddress') &&
          broadcastData['serverAddress']!.isNotEmpty) {
        s.mqttServerAddress = broadcastData['serverAddress'];
        debugPrint(
          'SettingsProvider: Updated server address to ${s.mqttServerAddress}',
        );
      }

      // 更新MQTT端口
      if (broadcastData.containsKey('mqttPort') &&
          broadcastData['mqttPort']!.isNotEmpty) {
        s.mqttPort = broadcastData['mqttPort'];
        debugPrint('SettingsProvider: Updated MQTT port to ${s.mqttPort}');
      }

      // 确保MAC地址存在
      if (s.macAddress == null || s.macAddress!.isEmpty) {
        s.macAddress = await _macAddressService.getMacAddress();
        debugPrint('SettingsProvider: Retrieved MAC address: ${s.macAddress}');
      }
    });

    debugPrint('SettingsProvider: Settings update completed');
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
