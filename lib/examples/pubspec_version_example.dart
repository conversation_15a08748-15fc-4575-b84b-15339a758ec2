import 'package:flutter/material.dart';
import '../utils/pubspec_version_utils.dart';

/// 基于 pubspec.yaml 的版本号使用示例
class PubspecVersionExample extends StatefulWidget {
  const PubspecVersionExample({super.key});

  @override
  State<PubspecVersionExample> createState() => _PubspecVersionExampleState();
}

class _PubspecVersionExampleState extends State<PubspecVersionExample> {
  String _appVersion = '加载中...';
  String _versionName = '加载中...';
  String _buildNumber = '加载中...';
  String _formattedVersion = '加载中...';
  Map<String, dynamic> _versionInfo = {};

  @override
  void initState() {
    super.initState();
    _loadVersionInfo();
  }

  /// 加载版本信息
  Future<void> _loadVersionInfo() async {
    try {
      final appVersion = await PubspecVersionUtils.getAppVersion();
      final versionName = await PubspecVersionUtils.getVersionName();
      final buildNumber = await PubspecVersionUtils.getBuildNumber();
      final formattedVersion = await PubspecVersionUtils.getFormattedVersion();
      final versionInfo = await PubspecVersionUtils.getVersionInfo();

      setState(() {
        _appVersion = appVersion;
        _versionName = versionName;
        _buildNumber = buildNumber;
        _formattedVersion = formattedVersion;
        _versionInfo = versionInfo;
      });
    } catch (e) {
      debugPrint('加载版本信息失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pubspec 版本号管理'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '版本信息',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    Text('完整版本号: $_appVersion'),
                    Text('版本名称: $_versionName'),
                    Text('构建号: $_buildNumber'),
                    Text('格式化显示: $_formattedVersion'),
                    if (_versionInfo.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text('平台: ${_versionInfo['platform'] ?? '未知'}'),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '操作',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadVersionInfo,
                      child: const Text('刷新版本信息'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '使用说明',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      '1. 版本号在 pubspec.yaml 中定义\n'
                      '2. 格式: major.minor.patch+buildNumber\n'
                      '3. 例如: 1.0.0+1 表示版本1.0.0，构建号1\n'
                      '4. 发布时通过 --build-name 和 --build-number 参数覆盖\n'
                      '5. 这是 Flutter 应用的标准版本管理方式',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
