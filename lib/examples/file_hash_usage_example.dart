import 'package:flutter/foundation.dart';
import '../services/file_hash_service.dart';
import '../services/file_service.dart';
import '../services/message_handler_service.dart';
import '../models/mqtt_message_model.dart';
import '../models/mqtt_file_item_model.dart';

/// 文件hash功能使用示例
/// 演示如何使用新的hash功能来优化文件下载和管理
class FileHashUsageExample {
  final FileHashService _fileHashService = FileHashService();
  final FileService _fileService = FileService();
  final MessageHandlerService _messageHandlerService = MessageHandlerService();

  /// 示例1：处理包含hash的MQTT消息
  Future<void> handleMqttMessageWithHash() async {
    debugPrint('=== 示例1：处理包含hash的MQTT消息 ===');

    // 模拟MQTT消息（包含hash字段）
    final mqttMessage = MqttMessageModel(
      type: 2, // 全部接收指令
      fileList: [
        MqttFileItemModel(
          downloadFile: 'assets/zippack/ww2.zip',
          fileType: 'pdf',
          equipmentAliasName: '1号',
          hash:
              '0603b2dfae928f2787e49db304c1d52aadc01415b06e4c111e3758ef99addc5e',
        ),
      ],
    );

    debugPrint('接收到MQTT消息，文件: ${mqttMessage.fileList![0].downloadFile}');
    debugPrint('文件hash: ${mqttMessage.fileList![0].hash}');

    // 这里会自动检查hash并决定是否重新下载
    debugPrint('消息将会被MessageHandlerService处理，自动进行hash比较');
  }

  /// 示例2：展示hash比较逻辑
  Future<void> demonstrateHashComparison() async {
    debugPrint('\n=== 示例2：演示hash比较逻辑 ===');

    const downloadUrl = 'http://example.com/assets/zippack/demo.zip';
    const oldHash = 'old_hash_12345';
    const newHash = 'new_hash_67890';

    // 第一次下载，保存hash
    await _fileHashService.saveFileHash(
      downloadUrl: downloadUrl,
      hash: oldHash,
      localFilePath: '/tmp/demo.zip',
      extractedDirPath: '/tmp/extracted/demo_old_hash_12345',
    );
    debugPrint('保存了文件hash记录: $oldHash');

    // 检查相同hash - 不需要重新下载
    final shouldRedownload1 = await _fileHashService.shouldRedownload(
      downloadUrl,
      oldHash,
    );
    debugPrint('相同hash检查 - 需要重新下载: $shouldRedownload1 (应该是false)');

    // 检查不同hash - 需要重新下载
    final shouldRedownload2 = await _fileHashService.shouldRedownload(
      downloadUrl,
      newHash,
    );
    debugPrint('不同hash检查 - 需要重新下载: $shouldRedownload2 (应该是true)');

    // 如果需要重新下载，解压目录将使用新的hash命名
    if (shouldRedownload2) {
      final newDirName = _fileHashService.generateExtractedDirName(
        downloadUrl,
        newHash,
      );
      debugPrint('新的解压目录名称: $newDirName');
    }
  }

  /// 示例3：展示解压目录命名规则
  Future<void> demonstrateDirectoryNaming() async {
    debugPrint('\n=== 示例3：演示解压目录命名规则 ===');

    const downloadUrl = 'http://example.com/assets/zippack/presentation.zip';
    const hash =
        '0603b2dfae928f2787e49db304c1d52aadc01415b06e4c111e3758ef99addc5e';

    // 生成解压目录名称（包含hash）
    final dirNameWithHash = _fileHashService.generateExtractedDirName(
      downloadUrl,
      hash,
    );
    debugPrint('包含hash的目录名: $dirNameWithHash');

    // 生成解压目录名称（不包含hash）
    final dirNameWithoutHash = _fileHashService.generateExtractedDirName(
      downloadUrl,
      null,
    );
    debugPrint('不包含hash的目录名: $dirNameWithoutHash');

    debugPrint('目录结构示例:');
    debugPrint('  /tmp/extracted/');
    debugPrint('    ├── presentation_$hash/  (新版本)');
    debugPrint('    └── presentation/        (旧版本或无hash)');
  }

  /// 示例4：展示完整的文件处理流程
  Future<void> demonstrateCompleteWorkflow() async {
    debugPrint('\n=== 示例4：完整的文件处理流程 ===');

    const downloadUrl = 'http://server.com/assets/zippack/training.zip';
    const hash =
        'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456';

    debugPrint('步骤1: 接收到文件下载请求');
    debugPrint('  URL: $downloadUrl');
    debugPrint('  Hash: $hash');

    debugPrint('步骤2: 检查是否需要重新下载');
    final shouldDownload = await _fileHashService.shouldRedownload(
      downloadUrl,
      hash,
    );
    debugPrint('  需要下载: $shouldDownload');

    if (shouldDownload) {
      debugPrint('步骤3: 开始下载文件');

      debugPrint('步骤4: 下载完成后解压');
      final extractedDirName = _fileHashService.generateExtractedDirName(
        downloadUrl,
        hash,
      );
      debugPrint('  解压目录: /tmp/extracted/$extractedDirName');

      debugPrint('步骤5: 保存hash记录');
      await _fileHashService.saveFileHash(
        downloadUrl: downloadUrl,
        hash: hash,
        localFilePath: '/tmp/training.zip',
        extractedDirPath: '/tmp/extracted/$extractedDirName',
      );

      debugPrint('步骤6: 文件处理完成');
    } else {
      debugPrint('步骤3: 使用现有文件，无需重新下载');
    }
  }

  /// 示例5：展示清理功能
  Future<void> demonstrateCleanupWithHash() async {
    debugPrint('\n=== 示例5：展示清理功能 ===');

    debugPrint('当系统清理旧文件时：');
    debugPrint('1. 删除文件本身');
    debugPrint('2. 同时删除对应的hash记录');
    debugPrint('3. 清理无效的hash记录（文件已不存在）');

    // 模拟清理过程
    debugPrint('正在清理无效的hash记录...');
    await _fileHashService.cleanupInvalidHashes();
    debugPrint('清理完成');
  }

  /// 运行所有示例
  Future<void> runAllExamples() async {
    debugPrint('🚀 开始演示文件hash功能\n');

    try {
      await handleMqttMessageWithHash();
      await demonstrateHashComparison();
      await demonstrateDirectoryNaming();
      await demonstrateCompleteWorkflow();
      await demonstrateCleanupWithHash();

      debugPrint('\n✅ 所有示例演示完成！');
      debugPrint('\n📋 功能总结：');
      debugPrint('- ✅ MQTT消息支持hash字段');
      debugPrint('- ✅ 文件hash存储和管理');
      debugPrint('- ✅ 智能判断是否需要重新下载');
      debugPrint('- ✅ 解压目录使用hash命名');
      debugPrint('- ✅ 清理时同时删除hash记录');
    } catch (e) {
      debugPrint('❌ 演示过程中出现错误: $e');
    }
  }
}
