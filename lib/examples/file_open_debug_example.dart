import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../services/file_open_diagnostics.dart';
import '../providers/file_provider.dart';
import '../services/file_service.dart';

/// 文件打开调试示例
/// 演示如何使用诊断工具来调试文件解压后无法正确打开的问题
class FileOpenDebugExample {
  /// 调试MQTT文件处理流程
  /// 这个方法可以在接收到MQTT消息后调用，用于诊断文件处理问题
  static Future<void> debugMqttFileProcessing({
    required String downloadUrl,
    String? hash,
    FileProvider? fileProvider,
  }) async {
    debugPrint('🔧 ===== 开始调试MQTT文件处理流程 =====');
    debugPrint('📥 下载URL: $downloadUrl');
    debugPrint('🔑 Hash: ${hash ?? '无'}');

    try {
      // 1. 诊断文件处理流程
      debugPrint('\n🔍 步骤1: 诊断文件处理流程');
      final fileResults = await FileOpenDiagnostics.diagnoseFileProcessing(
        downloadUrl: downloadUrl,
        hash: hash,
      );

      // 2. 诊断FileProvider状态
      if (fileProvider != null) {
        debugPrint('\n🔍 步骤2: 诊断FileProvider状态');
        final providerResults = FileOpenDiagnostics.diagnoseFileProvider(
          fileProvider,
        );
      }

      // 3. 打印诊断总结
      debugPrint('\n📋 步骤3: 诊断总结');
      FileOpenDiagnostics.printDiagnosticSummary(fileResults);

      // 4. 提供修复建议
      _provideTroubleshootingSuggestions(fileResults);
    } catch (e) {
      debugPrint('❌ 调试过程中出现错误: $e');
    }

    debugPrint('🔧 ===== 调试完成 =====\n');
  }

  /// 快速诊断当前FileProvider状态
  static void quickDiagnoseFileProvider(FileProvider fileProvider) {
    debugPrint('⚡ 快速诊断FileProvider状态:');

    final results = FileOpenDiagnostics.diagnoseFileProvider(fileProvider);

    // 检查关键状态
    if (results['state']?.toString().contains('completed') == true) {
      if (results['has_data_json_file'] == true) {
        debugPrint('✅ 状态正常: 文件处理完成，data.json已找到');
      } else {
        debugPrint('⚠️ 状态异常: 文件处理完成，但data.json未找到');
      }
    } else {
      debugPrint('📊 当前状态: ${results['state']}');
    }

    if (results['error'] != null && results['error'].toString().isNotEmpty) {
      debugPrint('❌ 错误信息: ${results['error']}');
    }
  }

  /// 检查文件是否能正确显示在NativeRenderScreen
  static Future<void> checkNativeRenderCompatibility(
    String dataJsonPath,
  ) async {
    debugPrint('🖥️ 检查NativeRenderScreen兼容性');
    debugPrint('📄 data.json路径: $dataJsonPath');

    try {
      final fileService = FileService();
      final dataJsonFile = await fileService.findDataJsonFile(
        dataJsonPath.contains('data.json')
            ? dataJsonPath.substring(0, dataJsonPath.lastIndexOf('/'))
            : dataJsonPath,
      );

      if (dataJsonFile != null) {
        debugPrint('✅ data.json文件找到');

        // 检查文件内容
        final content = await dataJsonFile.readAsString();
        debugPrint('📏 文件大小: ${content.length} 字符');

        // 简单验证JSON格式
        try {
          final jsonData = await compute(_parseJsonContent, content);
          if (jsonData != null) {
            debugPrint('✅ JSON格式有效');
            debugPrint('🎨 Canvas尺寸: ${jsonData['canvasRatio'] ?? '未定义'}');
            debugPrint('🧩 模板组件: ${jsonData['templateSm']?.length ?? 0} 个');
          } else {
            debugPrint('❌ JSON格式无效');
          }
        } catch (e) {
          debugPrint('❌ JSON解析失败: $e');
        }
      } else {
        debugPrint('❌ data.json文件未找到');
      }
    } catch (e) {
      debugPrint('❌ 兼容性检查失败: $e');
    }
  }

  /// 静态JSON解析方法
  static dynamic _parseJsonContent(String content) {
    try {
      return json.decode(content);
    } catch (e) {
      return null;
    }
  }

  /// 提供故障排除建议
  static void _provideTroubleshootingSuggestions(Map<String, dynamic> results) {
    debugPrint('\n💡 故障排除建议:');

    final existingCheck =
        results['existing_file_check'] as Map<String, dynamic>?;
    final fileVerification =
        results['file_verification'] as Map<String, dynamic>?;
    final dataJsonValidation =
        results['data_json_validation'] as Map<String, dynamic>?;
    final assetsCheck = results['assets_check'] as Map<String, dynamic>?;

    // 1. 检查文件是否找到
    if (existingCheck?['found'] != true) {
      debugPrint('1️⃣ 文件未找到 → 检查下载和hash处理是否正确');
    }

    // 2. 检查文件完整性
    if (fileVerification?['all_exist'] != true) {
      final missingFiles = fileVerification?['missing_files'] as List? ?? [];
      debugPrint('2️⃣ 文件缺失 → 缺少: ${missingFiles.join(', ')}');

      if (missingFiles.contains('data_json')) {
        debugPrint('   → 解压过程可能失败，检查ZIP文件完整性');
      }
      if (missingFiles.contains('extracted_dir')) {
        debugPrint('   → 解压目录被意外删除，重新下载文件');
      }
    }

    // 3. 检查data.json有效性
    if (dataJsonValidation?['valid'] != true) {
      if (dataJsonValidation?['readable'] != true) {
        debugPrint('3️⃣ data.json不可读 → 检查文件权限');
      } else if (dataJsonValidation?['parseable'] != true) {
        debugPrint('3️⃣ data.json格式错误 → 检查JSON语法');
      }
    }

    // 4. 检查资源文件
    if (assetsCheck?['assets_dir_exists'] != true) {
      debugPrint('4️⃣ assets目录缺失 → 检查ZIP文件结构');
      final extractedContents = assetsCheck?['extracted_contents'] as List?;
      if (extractedContents != null && extractedContents.isNotEmpty) {
        debugPrint('   → 解压内容: ${extractedContents.join(', ')}');
      }
    }

    // 5. 通用建议
    debugPrint('\n🔧 通用解决方案:');
    debugPrint('• 清理临时文件并重新下载');
    debugPrint('• 检查网络连接和服务器状态');
    debugPrint('• 验证ZIP文件的完整性');
    debugPrint('• 检查设备存储空间');
    debugPrint('• 重启应用程序');
  }

  /// 模拟MQTT消息处理调试
  static Future<void> simulateDebugFlow() async {
    debugPrint('🎭 模拟调试流程演示');

    const mockUrl = 'http://example.com/assets/zippack/demo.zip';
    const mockHash = 'abc123def456';

    await debugMqttFileProcessing(downloadUrl: mockUrl, hash: mockHash);
  }
}
