import 'package:flutter/material.dart';
import '../utils/version_utils.dart';

/// 版本号管理使用示例
/// 展示如何在应用中使用版本号管理功能
class VersionUsageExample extends StatefulWidget {
  const VersionUsageExample({super.key});

  @override
  State<VersionUsageExample> createState() => _VersionUsageExampleState();
}

class _VersionUsageExampleState extends State<VersionUsageExample> {
  String _currentVersion = '加载中...';
  int _buildNumber = 0;
  Map<String, dynamic> _versionInfo = {};

  @override
  void initState() {
    super.initState();
    _loadVersionInfo();
  }

  /// 加载版本信息
  Future<void> _loadVersionInfo() async {
    try {
      final version = await VersionUtils.getCurrentVersion();
      final buildNumber = await VersionUtils.getBuildNumber();
      final versionInfo = await VersionUtils.getVersionInfo();

      setState(() {
        _currentVersion = version;
        _buildNumber = buildNumber;
        _versionInfo = versionInfo;
      });
    } catch (e) {
      debugPrint('加载版本信息失败: $e');
    }
  }

  /// 递增版本号
  Future<void> _incrementVersion() async {
    try {
      final newVersion = await VersionUtils.incrementVersion();
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('版本号已更新为: $newVersion')));
      await _loadVersionInfo();
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('更新版本号失败: $e')));
    }
  }

  /// 重置版本号
  Future<void> _resetVersion() async {
    try {
      await VersionUtils.resetVersion();
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('版本号已重置')));
      await _loadVersionInfo();
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('重置版本号失败: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('版本号管理示例'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '当前版本信息',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    Text('版本号: $_currentVersion'),
                    Text('构建号: $_buildNumber'),
                    if (_versionInfo.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text('平台: ${_versionInfo['platform'] ?? '未知'}'),
                      Text('最后更新: ${_versionInfo['last_update_time'] ?? '未知'}'),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '版本号操作',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: _incrementVersion,
                          child: const Text('递增版本号'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _loadVersionInfo,
                          child: const Text('刷新信息'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _resetVersion,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                      ),
                      child: const Text('重置版本号'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '使用说明',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      '1. 版本号格式: YYYY.MM.DD+构建号\n'
                      '2. 每次递增会自动增加构建号\n'
                      '3. 版本号会自动保存到本地存储\n'
                      '4. 在设备注册时会自动包含版本号\n'
                      '5. 可以通过 VersionUtils 类方便地使用版本号功能',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 版本号管理的具体使用方法示例
class VersionUsageExamples {
  /// 示例1: 获取当前版本号
  static Future<void> example1() async {
    final version = await VersionUtils.getCurrentVersion();
    debugPrint('当前版本号: $version');
  }

  /// 示例2: 递增版本号
  static Future<void> example2() async {
    final newVersion = await VersionUtils.incrementVersion();
    debugPrint('新版本号: $newVersion');
  }

  /// 示例3: 获取格式化的版本信息
  static Future<void> example3() async {
    final formattedVersion = await VersionUtils.getFormattedVersion();
    debugPrint('格式化版本: $formattedVersion');
  }

  /// 示例4: 获取API提交用的版本信息
  static Future<void> example4() async {
    final apiVersionInfo = await VersionUtils.getApiVersionInfo();
    debugPrint('API版本信息: $apiVersionInfo');
  }

  /// 示例5: 检查是否需要更新版本号
  static Future<void> example5() async {
    final shouldUpdate = await VersionUtils.shouldUpdateVersion();
    if (shouldUpdate) {
      final newVersion = await VersionUtils.autoUpdateIfNeeded();
      debugPrint('自动更新版本号: $newVersion');
    } else {
      debugPrint('版本号无需更新');
    }
  }
}
