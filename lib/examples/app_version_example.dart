import 'package:flutter/material.dart';
import '../utils/app_version_utils.dart';

/// Flutter 官方版本号管理使用示例
class AppVersionExample extends StatefulWidget {
  const AppVersionExample({super.key});

  @override
  State<AppVersionExample> createState() => _AppVersionExampleState();
}

class _AppVersionExampleState extends State<AppVersionExample> {
  String _appName = '加载中...';
  String _packageName = '加载中...';
  String _fullVersion = '加载中...';
  String _versionName = '加载中...';
  String _buildNumber = '加载中...';
  String _formattedVersion = '加载中...';
  Map<String, dynamic> _versionInfo = {};

  @override
  void initState() {
    super.initState();
    _loadVersionInfo();
  }

  /// 加载版本信息
  Future<void> _loadVersionInfo() async {
    try {
      final appName = await AppVersionUtils.getAppName();
      final packageName = await AppVersionUtils.getPackageName();
      final fullVersion = await AppVersionUtils.getFullVersion();
      final versionName = await AppVersionUtils.getVersionName();
      final buildNumber = await AppVersionUtils.getBuildNumber();
      final formattedVersion = await AppVersionUtils.getFormattedVersion();
      final versionInfo = await AppVersionUtils.getVersionInfo();

      setState(() {
        _appName = appName;
        _packageName = packageName;
        _fullVersion = fullVersion;
        _versionName = versionName;
        _buildNumber = buildNumber;
        _formattedVersion = formattedVersion;
        _versionInfo = versionInfo;
      });
    } catch (e) {
      debugPrint('加载版本信息失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Flutter 官方版本管理'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '应用信息',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    Text('应用名称: $_appName'),
                    Text('包名: $_packageName'),
                    Text('完整版本号: $_fullVersion'),
                    Text('版本名称: $_versionName'),
                    Text('构建号: $_buildNumber'),
                    Text('格式化显示: $_formattedVersion'),
                    if (_versionInfo.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text('平台: ${_versionInfo['platform'] ?? '未知'}'),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '操作',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadVersionInfo,
                      child: const Text('刷新版本信息'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Flutter 官方版本管理说明',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      '1. 版本号在 pubspec.yaml 中定义\n'
                      '2. 格式: major.minor.patch+buildNumber\n'
                      '3. 例如: 1.0.0+1 表示版本1.0.0，构建号1\n'
                      '4. 使用 package_info_plus 插件获取版本信息\n'
                      '5. 构建时可通过参数覆盖版本号\n'
                      '6. 这是 Flutter 应用的标准版本管理方式',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 版本号管理的具体使用方法示例
class AppVersionUsageExamples {
  /// 示例1: 获取完整版本号
  static Future<void> example1() async {
    final version = await AppVersionUtils.getFullVersion();
    debugPrint('完整版本号: $version');
  }

  /// 示例2: 获取版本名称和构建号
  static Future<void> example2() async {
    final versionName = await AppVersionUtils.getVersionName();
    final buildNumber = await AppVersionUtils.getBuildNumber();
    debugPrint('版本名称: $versionName, 构建号: $buildNumber');
  }

  /// 示例3: 获取格式化的版本信息
  static Future<void> example3() async {
    final formattedVersion = await AppVersionUtils.getFormattedVersion();
    debugPrint('格式化版本: $formattedVersion');
  }

  /// 示例4: 获取API提交用的版本信息
  static Future<void> example4() async {
    final apiVersionInfo = await AppVersionUtils.getApiVersionInfo();
    debugPrint('API版本信息: $apiVersionInfo');
  }

  /// 示例5: 获取应用基本信息
  static Future<void> example5() async {
    final appName = await AppVersionUtils.getAppName();
    final packageName = await AppVersionUtils.getPackageName();
    debugPrint('应用名称: $appName, 包名: $packageName');
  }

  /// 示例6: 版本号比较
  static Future<void> example6() async {
    final result = AppVersionUtils.compareVersions('1.0.0+1', '1.0.1+1');
    debugPrint('版本比较结果: $result'); // -1: 第一个版本较低
  }
}
