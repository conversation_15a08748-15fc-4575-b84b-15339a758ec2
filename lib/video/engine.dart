import 'package:flutter/widgets.dart';

/// A minimal, pluggable video engine interface to decouple business from plugins.
abstract class VideoEngine {
  /// Initialize underlying controller(s) but do not start playback yet.
  Future<void> init();

  /// Open media from a local file path or URL.
  Future<void> open(String sourcePath);

  /// Start/resume playback.
  Future<void> play();

  /// Pause playback.
  Future<void> pause();

  /// Stop playback (and keep ready if possible).
  Future<void> stop();

  /// Dispose resources.
  Future<void> dispose();

  /// Build the video view widget to render frames.
  Widget buildView();
}

