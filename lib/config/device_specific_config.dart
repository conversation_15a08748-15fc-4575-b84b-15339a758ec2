import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import '../utils/h265_compatibility_detector.dart';

/// 设备特定配置管理器
/// 为不同设备提供优化的配置参数
class DeviceSpecificConfig {
  static DeviceSpecificConfig? _instance;
  static DeviceSpecificConfig get instance =>
      _instance ??= DeviceSpecificConfig._();

  DeviceSpecificConfig._();

  /// 获取设备特定配置
  Map<String, dynamic> getConfigForDevice(H265CompatibilityInfo info) {
    if (info.isRK3568) {
      return _getRK3568Config();
    } else if (info.manufacturer.toLowerCase().contains('samsung')) {
      return _getSamsungConfig();
    } else if (info.manufacturer.toLowerCase().contains('huawei')) {
      return _getHuaweiConfig();
    } else if (info.manufacturer.toLowerCase().contains('xiaomi')) {
      return _getXiaomiConfig();
    } else {
      return _getGenericConfig();
    }
  }

  /// 获取WebView设置
  InAppWebViewSettings getWebViewSettingsForDevice(H265CompatibilityInfo info) {
    var settings = InAppWebViewSettings(
      javaScriptEnabled: true,
      domStorageEnabled: true,
      databaseEnabled: true,
      allowsInlineMediaPlayback: true,
      mediaPlaybackRequiresUserGesture: false,
      allowsAirPlayForMediaPlayback: true,
      allowsPictureInPictureMediaPlayback: true,
      iframeAllow: "camera; microphone; geolocation",
      iframeAllowFullscreen: true,
    );

    if (info.isRK3568) {
      // RK3568特殊配置
      settings.hardwareAcceleration = false; // 禁用硬件加速避免花屏
      settings.useHybridComposition = true;
      settings.useShouldOverrideUrlLoading = true;
      settings.cacheEnabled = false;
      settings.clearCache = true;
    } else if (info.webViewSupportsH265) {
      // 支持H.265的设备
      settings.hardwareAcceleration = true;
      settings.useHybridComposition = false;
    } else {
      // 不支持H.265的设备
      settings.hardwareAcceleration = false;
      settings.useHybridComposition = true;
    }

    return settings;
  }

  /// RK3568配置
  Map<String, dynamic> _getRK3568Config() {
    return {
      'deviceName': 'RK3568',
      'description': 'Rockchip RK3568 优化配置',
      'webViewSettings': {
        'hardwareAcceleration': false,
        'useHybridComposition': true,
        'cacheEnabled': false,
      },
      'videoSettings': {
        'preferredEngine': 'video_player', // RK3568优先使用video_player
        'hardwareDecoding': false, // 禁用硬件解码
        'h265Support': false,
      },
      'optimizations': ['禁用硬件加速避免花屏', '使用软件解码确保兼容性', '优化内存使用'],
    };
  }

  /// Samsung设备配置
  Map<String, dynamic> _getSamsungConfig() {
    return {
      'deviceName': 'Samsung',
      'description': 'Samsung设备优化配置',
      'webViewSettings': {
        'hardwareAcceleration': true,
        'useHybridComposition': false,
        'cacheEnabled': true,
      },
      'videoSettings': {
        'preferredEngine': 'media_kit',
        'hardwareDecoding': true,
        'h265Support': true,
      },
      'optimizations': ['启用硬件加速', '优化视频播放性能', '支持H.265解码'],
    };
  }

  /// Huawei设备配置
  Map<String, dynamic> _getHuaweiConfig() {
    return {
      'deviceName': 'Huawei',
      'description': 'Huawei设备优化配置',
      'webViewSettings': {
        'hardwareAcceleration': true,
        'useHybridComposition': false,
        'cacheEnabled': true,
      },
      'videoSettings': {
        'preferredEngine': 'media_kit',
        'hardwareDecoding': true,
        'h265Support': true,
      },
      'optimizations': ['启用硬件加速', '优化EMUI兼容性', '支持H.265解码'],
    };
  }

  /// Xiaomi设备配置
  Map<String, dynamic> _getXiaomiConfig() {
    return {
      'deviceName': 'Xiaomi',
      'description': 'Xiaomi设备优化配置',
      'webViewSettings': {
        'hardwareAcceleration': true,
        'useHybridComposition': false,
        'cacheEnabled': true,
      },
      'videoSettings': {
        'preferredEngine': 'media_kit',
        'hardwareDecoding': true,
        'h265Support': true,
      },
      'optimizations': ['启用硬件加速', '优化MIUI兼容性', '支持H.265解码'],
    };
  }

  /// 通用设备配置
  Map<String, dynamic> _getGenericConfig() {
    return {
      'deviceName': 'Generic',
      'description': '通用设备配置',
      'webViewSettings': {
        'hardwareAcceleration': true,
        'useHybridComposition': false,
        'cacheEnabled': true,
      },
      'videoSettings': {
        'preferredEngine': 'media_kit',
        'hardwareDecoding': true,
        'h265Support': false, // 保守设置
      },
      'optimizations': ['平衡性能和兼容性', '自适应硬件能力', '保守的H.265支持'],
    };
  }

  /// 获取设备特定的性能优化建议
  List<String> getPerformanceOptimizations(H265CompatibilityInfo info) {
    final config = getConfigForDevice(info);
    return List<String>.from(config['optimizations'] ?? []);
  }

  /// 检查设备是否需要特殊处理
  bool requiresSpecialHandling(H265CompatibilityInfo info) {
    return info.isRK3568 ||
        info.recommendedStrategy == H265DecodingStrategy.forceSoftware;
  }

  /// 获取设备兼容性报告
  String getCompatibilityReport(H265CompatibilityInfo info) {
    final config = getConfigForDevice(info);
    final buffer = StringBuffer();

    buffer.writeln('=== 设备兼容性报告 ===');
    buffer.writeln('设备: ${info.manufacturer} ${info.deviceModel}');
    buffer.writeln('Android版本: API ${info.apiLevel}');
    buffer.writeln('配置类型: ${config['deviceName']}');
    buffer.writeln('描述: ${config['description']}');
    buffer.writeln();

    buffer.writeln('WebView设置:');
    final webViewSettings = config['webViewSettings'] as Map<String, dynamic>;
    webViewSettings.forEach((key, value) {
      buffer.writeln('  $key: $value');
    });
    buffer.writeln();

    buffer.writeln('视频设置:');
    final videoSettings = config['videoSettings'] as Map<String, dynamic>;
    videoSettings.forEach((key, value) {
      buffer.writeln('  $key: $value');
    });
    buffer.writeln();

    buffer.writeln('优化措施:');
    final optimizations = config['optimizations'] as List<String>;
    for (final optimization in optimizations) {
      buffer.writeln('  • $optimization');
    }

    return buffer.toString();
  }

  /// 调试信息
  void printDeviceConfig(H265CompatibilityInfo info) {
    if (kDebugMode) {
      debugPrint(getCompatibilityReport(info));
    }
  }
}
