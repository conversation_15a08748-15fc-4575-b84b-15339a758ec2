import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../models/reported_data_model.dart';
import '../providers/settings_provider.dart';
import 'device_info_service.dart';
import 'settings_service.dart';
import 'app_version_service.dart';

class EquipmentApiService {
  final Dio _dio = Dio();
  final DeviceInfoService _deviceInfoService = DeviceInfoService();
  final AppVersionService _versionService = AppVersionService();

  Future<Map<String, dynamic>> addEquipment({
    required String serverAddress,
    required String serverPort,
    required String deviceAlias,
    required String macAddress,
    required String groupName,
    required String aliasName,
    required String registrationCode,
  }) async {
    if (serverAddress.isEmpty) {
      debugPrint('Server address is empty, skipping device registration');
      return {'code': -1, 'message': '服务器地址为空', 'data': {}};
    }

    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult.contains(ConnectivityResult.none)) {
        debugPrint('No internet connection, skipping device registration');
        return {'code': -1, 'message': '没有网络连接', 'data': {}};
      }

      String ipAddress = await _getDeviceIpAddress() ?? '';
      String baseUrl = _buildBaseUrl(serverAddress, serverPort);
      final url = '$baseUrl/v1/equipment/addEquipment';

      final deviceInfo = await _deviceInfoService.getAndroidDetailedInfo();
      final appVersion = await _versionService.getFullVersion();

      final body = {
        'name': deviceAlias,
        'mac_address': macAddress,
        'group_name': groupName,
        'alias_name': aliasName,
        'ip_addr': ipAddress,
        'registration_code': registrationCode,
        'app_version': appVersion,
        ...deviceInfo,
      };

      debugPrint('Registering device with server: $url');
      debugPrint('Request body: $body');

      final response = await _dio.post(
        url,
        data: body,
        options: _getDioOptions(),
      );

      if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
        debugPrint('Device registration successful: ${response.data}');
        return response.data;
      } else {
        debugPrint(
          'Device registration failed: ${response.statusCode}, ${response.data}',
        );
        return {
          'code': response.statusCode,
          'message': '注册失败',
          'data': response.data,
        };
      }
    } catch (e) {
      debugPrint('Error registering device: $e');
      return {'code': -1, 'message': '注册时发生错误: $e', 'data': {}};
    }
  }

  Future<bool> updateDeviceInfo({
    required String serverAddress,
    required String serverPort,
    required String deviceAlias,
    required String macAddress,
    required String groupName,
    required String aliasName,
    required String registrationCode,
  }) async {
    if (serverAddress.isEmpty) {
      debugPrint('Server address is empty, skipping device update');
      return false;
    }

    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult.contains(ConnectivityResult.none)) {
        debugPrint('No internet connection, skipping device update');
        return false;
      }

      String ipAddress = await _getDeviceIpAddress() ?? '';
      String baseUrl = _buildBaseUrl(serverAddress, serverPort);
      final url = '$baseUrl/v1/equipment/updateEquipment';

      final body = {
        'name': deviceAlias,
        'mac_address': macAddress,
        'group_name': groupName,
        'alias_name': aliasName,
        'ip': ipAddress,
        'registration_code': registrationCode,
      };

      debugPrint('Updating device info with server: $url');
      debugPrint('Request body: $body');

      final response = await _dio.put(
        url,
        data: body,
        options: _getDioOptions(),
      );

      if (response.statusCode == 200) {
        debugPrint('Device update successful: ${response.data}');
        return true;
      } else {
        debugPrint(
          'Device update failed: ${response.statusCode}, ${response.data}',
        );
        return false;
      }
    } catch (e) {
      debugPrint('Error updating device info: $e');
      return false;
    }
  }

  Future<void> sendHeartbeat({
    required SettingsProvider settingsProvider,
  }) async {
    try {
      final settings = settingsProvider.settings;
      final serverAddress = settings.mqttServerAddress ?? '';
      final macAddress = settings.macAddress ?? '';
      final registrationCode = settings.registrationCode ?? '';

      if (serverAddress.isEmpty || macAddress.isEmpty) {
        debugPrint('Missing required parameters for heartbeat');
        return;
      }

      String baseUrl = _buildBaseUrl(serverAddress, settings.serverPort ?? '');
      final url = '$baseUrl/v1/equipment/lifeEquipment';
      final body = {
        'mac_address': macAddress,
        'registration_code': registrationCode,
      };

      debugPrint('Sending heartbeat to: $url');
      debugPrint('Request body: $body');

      final response = await _dio.put(
        url,
        data: body,
        options: _getDioOptions(),
      );

      if (response.statusCode == 200) {
        debugPrint('Heartbeat sent successfully: ${response.data}');
        _handleHeartbeatResponse(response.data, settingsProvider);
      } else {
        debugPrint('Failed to send heartbeat, status: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      debugPrint('Error sending heartbeat: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  /// 获取设备在数据库中的equipment_id
  Future<String?> getEquipmentId() async {
    try {
      final serverAddress = await _getServerAddressFromSettings();
      final serverPort = await _getServerPortFromSettings();
      final settingsService = SettingsService();
      final settings = await settingsService.loadSettings();
      final macAddress = settings.macAddress ?? '';

      if (serverAddress.isEmpty || macAddress.isEmpty) {
        debugPrint('Server address or MAC address is empty');
        return null;
      }

      final baseUrl = _buildBaseUrl(serverAddress, serverPort);
      final url = '$baseUrl/v1/equipment/getEquipmentId';

      final response = await _dio.post(
        url,
        data: {'mac_address': macAddress},
        options: _getDioOptions(),
      );

      if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
        final data = response.data;
        if (data['code'] == 0 && data['data'] != null) {
          return data['data']['equipment_id']?.toString();
        }
      }

      debugPrint('Failed to get equipment ID: ${response.data}');
      return null;
    } catch (e) {
      debugPrint('Error getting equipment ID: $e');
      return null;
    }
  }

  /// 上报设备状态和操作信息到 equipment/reported 接口
  Future<bool> reportStatus(ReportedDataModel reportData) async {
    try {
      // 检查网络连接
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult.contains(ConnectivityResult.none)) {
        debugPrint('No internet connection, skipping status report');
        return false;
      }

      // 构建请求URL
      final serverAddress = reportData.macAddress.isNotEmpty
          ? await _getServerAddressFromSettings()
          : '';
      if (serverAddress.isEmpty) {
        debugPrint('Server address is empty, skipping status report');
        return false;
      }

      final serverPort = await _getServerPortFromSettings();
      String baseUrl = _buildBaseUrl(serverAddress, serverPort);
      final url = '$baseUrl/v1/equipment/reported';

      debugPrint('Reporting status to: $url');
      debugPrint('Report data: ${reportData.toJson()}');

      final response = await _dio.post(
        url,
        data: reportData.toJson(),
        options: _getDioOptions(),
      );

      if (response.statusCode == 200) {
        debugPrint('Status report sent successfully: ${response.data}');
        return true;
      } else {
        debugPrint(
          'Failed to send status report, status: ${response.statusCode}',
        );
        return false;
      }
    } catch (e, stackTrace) {
      debugPrint('Error sending status report: $e');
      debugPrint('Stack trace: $stackTrace');
      return false;
    }
  }

  /// 批量上报状态信息
  Future<bool> reportStatusBatch(List<ReportedDataModel> reportDataList) async {
    if (reportDataList.isEmpty) {
      return true;
    }

    try {
      // 检查网络连接
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult.contains(ConnectivityResult.none)) {
        debugPrint('No internet connection, skipping batch status report');
        return false;
      }

      // 构建请求URL
      final serverAddress = reportDataList.first.macAddress.isNotEmpty
          ? await _getServerAddressFromSettings()
          : '';
      if (serverAddress.isEmpty) {
        debugPrint('Server address is empty, skipping batch status report');
        return false;
      }

      final serverPort = await _getServerPortFromSettings();
      String baseUrl = _buildBaseUrl(serverAddress, serverPort);
      final url = '$baseUrl/v1/equipment/reported/batch';

      final batchData = {
        'reports': reportDataList.map((report) => report.toJson()).toList(),
      };

      debugPrint('Reporting batch status to: $url');
      debugPrint('Batch report count: ${reportDataList.length}');

      final response = await _dio.post(
        url,
        data: batchData,
        options: _getDioOptions(),
      );

      if (response.statusCode == 200) {
        debugPrint('Batch status report sent successfully: ${response.data}');
        return true;
      } else {
        debugPrint(
          'Failed to send batch status report, status: ${response.statusCode}',
        );
        return false;
      }
    } catch (e, stackTrace) {
      debugPrint('Error sending batch status report: $e');
      debugPrint('Stack trace: $stackTrace');
      return false;
    }
  }

  Future<bool> uploadScreenshot({
    required File screenshotFile,
    required String groupName,
    required String aliasName,
    String? taskId,
  }) async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult.contains(ConnectivityResult.none)) {
        debugPrint('No internet connection, skipping screenshot upload');
        return false;
      }

      final serverAddress = await _getServerAddressFromSettings();
      if (serverAddress.isEmpty) {
        debugPrint('Server address is empty, skipping screenshot upload');
        return false;
      }

      final serverPort = await _getServerPortFromSettings();
      final baseUrl = _buildBaseUrl(serverAddress, serverPort);
      final url = '$baseUrl/v1/equipment/screenshot';

      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          screenshotFile.path,
          filename: 'screenshot.png',
        ),
        'group_name': groupName,
        'equipment_alias_name': aliasName,
        'task_id': taskId,
      });

      debugPrint('Uploading screenshot to: $url');

      final response = await _dio.post(
        url,
        data: formData,
        options: Options(
          headers: {'Content-Type': 'multipart/form-data'},
          receiveTimeout: const Duration(seconds: 30),
          sendTimeout: const Duration(seconds: 30),
        ),
      );

      if (response.statusCode == 200) {
        debugPrint('Screenshot uploaded successfully: ${response.data}');
        return true;
      } else {
        debugPrint(
          'Failed to upload screenshot, status: ${response.statusCode}',
        );
        return false;
      }
    } catch (e, stackTrace) {
      debugPrint('Error uploading screenshot: $e');
      debugPrint('Stack trace: $stackTrace');
      return false;
    }
  }

  void _handleHeartbeatResponse(
    dynamic responseData,
    SettingsProvider settingsProvider,
  ) {
    try {
      if (responseData is Map<String, dynamic> &&
          responseData['code'] == 0 &&
          responseData['data'] != null) {
        final data = responseData['data'];
        final remoteGroupName = data['group_name'] ?? '';
        final remoteAliasName = data['alias_name'] ?? '';
        final equipmentId = data['id'] ?? 0;
        final registrationCode = data['registration_code'] ?? '';
        debugPrint(
          "resonseData: $data ;groupName: $remoteGroupName; aliasName: $remoteAliasName ; equipmentId: $equipmentId",
        );
        if (remoteGroupName != "" &&
            remoteGroupName != settingsProvider.settings.groupName) {
          settingsProvider.updateGroupName(remoteGroupName);
        }

        if (remoteAliasName != "" &&
            remoteAliasName != settingsProvider.settings.deviceAlias) {
          settingsProvider.updateDeviceAlias(remoteAliasName);
        }
        if (equipmentId > 0) {
          debugPrint("heart beat responser success");
          settingsProvider.updateEquipmentId(equipmentId);
        }
        if (registrationCode != "") {
          debugPrint("heart beat responser success");
          settingsProvider.updateRegistrationCode(registrationCode);
        }
      }
    } catch (e) {
      debugPrint('Error parsing heartbeat response: $e');
    }
  }

  /// 从设置中获取服务器地址
  Future<String> _getServerAddressFromSettings() async {
    try {
      // 这里需要访问SettingsService来获取服务器地址
      // 由于当前类没有直接访问SettingsService的方式，我们需要通过参数传递
      // 或者创建一个SettingsService实例
      final settingsService = SettingsService();
      final settings = await settingsService.loadSettings();
      return settings.mqttServerAddress ?? '';
    } catch (e) {
      debugPrint('Error getting server address from settings: $e');
      return '';
    }
  }

  /// 从设置中获取服务器端口
  Future<String> _getServerPortFromSettings() async {
    try {
      final settingsService = SettingsService();
      final settings = await settingsService.loadSettings();
      return settings.serverPort ?? '8567';
    } catch (e) {
      debugPrint('Error getting server port from settings: $e');
      return '8567';
    }
  }

  String _buildBaseUrl(String serverAddress, String port) {
    if (!serverAddress.startsWith('http://') &&
        !serverAddress.startsWith('https://')) {
      return 'http://$serverAddress:$port';
    }
    return serverAddress;
  }

  Options _getDioOptions() {
    return Options(
      headers: {'Content-Type': 'application/json'},
      receiveTimeout: const Duration(seconds: 10),
      sendTimeout: const Duration(seconds: 10),
    );
  }

  Future<String?> _getDeviceIpAddress() async {
    try {
      final interfaces = await NetworkInterface.list(
        includeLoopback: false,
        type: InternetAddressType.IPv4,
      );
      for (var interface in interfaces) {
        for (var addr in interface.addresses) {
          if (addr.type == InternetAddressType.IPv4) {
            return addr.address;
          }
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error getting device IP address: $e');
      return null;
    }
  }
}
