import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

class OrientationService {
  // Apply screen orientation based on setting
  static Future<void> applyOrientation(String orientation) async {
    try {
      List<DeviceOrientation> orientations;
      
      switch (orientation) {
        case 'landscape':
          orientations = [
            DeviceOrientation.landscapeLeft,
            DeviceOrientation.landscapeRight,
          ];
          break;
        case 'portrait':
          orientations = [
            DeviceOrientation.portraitUp,
            DeviceOrientation.portraitDown,
          ];
          break;
        default:
          // Default to landscape if unknown orientation
          orientations = [
            DeviceOrientation.landscapeLeft,
            DeviceOrientation.landscapeRight,
          ];
          break;
      }
      
      await SystemChrome.setPreferredOrientations(orientations);
      debugPrint('Applied orientation: $orientation');
    } catch (e) {
      debugPrint('Error applying orientation: $e');
    }
  }
  
  // Get current orientation setting as string
  static String getOrientationString(DeviceOrientation orientation) {
    switch (orientation) {
      case DeviceOrientation.landscapeLeft:
      case DeviceOrientation.landscapeRight:
        return 'landscape';
      case DeviceOrientation.portraitUp:
      case DeviceOrientation.portraitDown:
        return 'portrait';
      default:
        return 'landscape';
    }
  }
  
  // Check if orientation is landscape
  static bool isLandscape(String orientation) {
    return orientation == 'landscape';
  }
  
  // Check if orientation is portrait
  static bool isPortrait(String orientation) {
    return orientation == 'portrait';
  }
}
