import 'package:flutter/foundation.dart';
import '../models/mqtt_message_model.dart';

enum CommandKind { file, operation, other }

class _QueueItem {
  final String topic;
  final String payload;
  final Map<String, dynamic> json;
  final MqttMessageModel model;
  final CommandKind kind;
  final Future<bool> Function() executor; // 执行处理，返回是否真正执行了动作
  final void Function(bool handled, String? error)
  onComplete; // 回调处理完成（用于上报/ACK）

  _QueueItem({
    required this.topic,
    required this.payload,
    required this.json,
    required this.model,
    required this.kind,
    required this.executor,
    required this.onComplete,
  });
}

class MqttCommandQueue {
  MqttCommandQueue({required this.cancelHeavyOps});

  final VoidCallback cancelHeavyOps;

  // 文件下载指令队列（串行执行）
  final List<_QueueItem> _fileQueue = <_QueueItem>[];
  bool _fileProcessing = false;

  // 运行中的并行操作任务计数
  int _runningOperations = 0;

  static CommandKind classify(MqttMessageModel m) {
    final t = m.type;
    if (t == 1 || t == 2 || t == 3) return CommandKind.file;
    if (t == 4 || t == 5 || t == 6 || t == 7 || t == 8) {
      return CommandKind.operation;
    }
    return CommandKind.other;
  }

  bool _isFileCommand(CommandKind k) => k == CommandKind.file;

  void enqueue({
    required String topic,
    required String payload,
    required Map<String, dynamic> json,
    required MqttMessageModel model,
    required Future<bool> Function() executor,
    required void Function(bool handled, String? error) onComplete,
  }) {
    final kind = classify(model);

    final item = _QueueItem(
      topic: topic,
      payload: payload,
      json: json,
      model: model,
      kind: kind,
      executor: executor,
      onComplete: onComplete,
    );

    if (_isFileCommand(kind)) {
      // 下载指令串行处理：清理队列中所有未处理的下载任务
      _fileQueue.removeWhere((q) => _isFileCommand(q.kind));
      // 请求取消当前执行中的下载
      cancelHeavyOps();

      _fileQueue.add(item);
      _pumpFileQueue();
    } else {
      // 操作指令并行执行：立即启动，不进入队列
      _executeOperationAsync(item);
    }
  }

  /// 处理文件下载队列（串行执行）
  Future<void> _pumpFileQueue() async {
    if (_fileProcessing) return;
    _fileProcessing = true;
    try {
      while (_fileQueue.isNotEmpty) {
        final item = _fileQueue.removeAt(0);
        try {
          debugPrint('[队列] 开始执行文件下载指令 type=${item.model.type}');
          final handled = await item.executor();
          item.onComplete(handled, null);
          debugPrint(
            '[队列] 文件下载指令执行完成 type=${item.model.type} handled=$handled',
          );
        } catch (e) {
          debugPrint('[队列] 文件下载指令执行失败 type=${item.model.type} error=$e');
          item.onComplete(false, e.toString());
        }
      }
    } finally {
      _fileProcessing = false;
    }
  }

  /// 并行执行操作指令
  void _executeOperationAsync(_QueueItem item) {
    _runningOperations++;
    debugPrint(
      '[队列] 并行启动操作指令 type=${item.model.type} (当前运行: $_runningOperations)',
    );

    // 异步执行，不阻塞其他指令
    () async {
      try {
        final handled = await item.executor();
        item.onComplete(handled, null);
        debugPrint('[队列] 操作指令执行完成 type=${item.model.type} handled=$handled');
      } catch (e) {
        debugPrint('[队列] 操作指令执行失败 type=${item.model.type} error=$e');
        item.onComplete(false, e.toString());
      } finally {
        _runningOperations--;
        debugPrint(
          '[队列] 操作指令结束 type=${item.model.type} (剩余运行: $_runningOperations)',
        );
      }
    }();
  }

  /// 获取当前运行状态信息
  Map<String, dynamic> getStatus() {
    return {
      'fileQueueLength': _fileQueue.length,
      'fileProcessing': _fileProcessing,
      'runningOperations': _runningOperations,
    };
  }
}
