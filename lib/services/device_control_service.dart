import 'dart:io';
// import 'package:device_reboot/device_reboot.dart'; // 插件不可用，使用原生方法替代
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'error_handler_service.dart';
import 'service_locator.dart';

class DeviceControlService {
  static const MethodChannel _deviceControlChannel = MethodChannel(
    'com.mingsign.esop_client/device_control',
  );
  final ErrorHandlerService _errorHandler = ErrorHandlerService();
  final ScreenshotController _screenshotController =
      locator<ScreenshotController>();

  Future<void> setVolume(int volume) async {
    if (volume < 0 || volume > 100) {
      debugPrint("Volume must be between 0 and 100.");
      return;
    }

    debugPrint("Setting volume to: $volume%");

    try {
      await _deviceControlChannel.invokeMethod('setVolume', {'volume': volume});
      debugPrint("Volume set successfully to: $volume%");
    } on PlatformException catch (e, s) {
      debugPrint("Failed to set volume: ${e.message}, code: ${e.code}");
      _errorHandler.reportError(
        module: 'DeviceControl',
        message: 'Failed to set volume: ${e.message}',
        stackTrace: s.toString(),
        additionalInfo: {
          'platform': 'Android',
          'error_code': e.code,
          'requested_volume': volume,
          'error_details': e.details?.toString() ?? 'No additional details',
        },
      );
      // 重新抛出异常以便上层处理
      rethrow;
    } catch (e, s) {
      debugPrint("Unexpected error setting volume: $e");
      _errorHandler.reportError(
        module: 'DeviceControl',
        message: 'Unexpected error setting volume: $e',
        stackTrace: s.toString(),
        additionalInfo: {
          'platform': 'Android',
          'requested_volume': volume,
          'error_type': e.runtimeType.toString(),
        },
      );
      rethrow;
    }
  }

  Future<String?> takeScreenshot() async {
    try {
      final Uint8List? image = await _screenshotController.capture();
      if (image == null) {
        throw Exception('Failed to capture screenshot');
      }

      final externalDir = await getExternalStorageDirectory();
      Directory screenshotDir;

      if (externalDir == null) {
        // Fallback to temporary directory if external storage is not available
        screenshotDir = await getTemporaryDirectory();
      } else {
        screenshotDir = Directory('${externalDir.path}/screenshots');
        if (!await screenshotDir.exists()) {
          await screenshotDir.create(recursive: true);
        }
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final file = File('${screenshotDir.path}/screenshot_$timestamp.png');
      await file.writeAsBytes(image);
      return file.path;
    } catch (e, s) {
      _errorHandler.reportError(
        module: 'DeviceControl',
        message: 'Failed to take screenshot: $e',
        stackTrace: s.toString(),
        additionalInfo: {'platform': 'Flutter'},
      );
      return null;
    }
  }

  Future<String> testConnection() async {
    try {
      final result = await _deviceControlChannel.invokeMethod('test');
      debugPrint('测试连接成功: $result');
      return result;
    } catch (e) {
      debugPrint('测试连接失败: $e');
      rethrow;
    }
  }

  Future<void> clearPowerOnOffTime() async {
    if (!Platform.isAndroid) {
      debugPrint('clearPowerOnOffTime ignored: not Android');
      return;
    }
    try {
      await _deviceControlChannel.invokeMethod('clearPowerOnOffTime');
      debugPrint('clearPowerOnOffTime succeeded');
    } on PlatformException catch (e, s) {
      debugPrint('clearPowerOnOffTime failed: ${e.message}, code: ${e.code}');
      _errorHandler.reportError(
        module: 'DeviceControl',
        message: 'clearPowerOnOffTime failed: ${e.message}',
        stackTrace: s.toString(),
        additionalInfo: {
          'platform': 'Android',
          'error_code': e.code,
          'error_details': e.details?.toString() ?? 'No additional details',
        },
      );
      rethrow;
    } catch (e, s) {
      debugPrint('Unexpected error in clearPowerOnOffTime: $e');
      _errorHandler.reportError(
        module: 'DeviceControl',
        message: 'Unexpected error in clearPowerOnOffTime: $e',
        stackTrace: s.toString(),
        additionalInfo: {
          'platform': Platform.operatingSystem,
          'error_type': e.runtimeType.toString(),
        },
      );
      rethrow;
    }
  }

  Future<void> setPowerOnOffWithWeekly({
    required int powerOnHour,
    required int powerOnMinute,
    required int powerOffHour,
    required int powerOffMinute,
    required List<int> weekdays, // 周一到周日，共7个 0/1
  }) async {
    if (!Platform.isAndroid) {
      debugPrint('setPowerOnOffWithWeekly ignored: not Android');
      return;
    }
    if (weekdays.length != 7) {
      throw ArgumentError('weekdays must have 7 elements (Mon..Sun)');
    }
    try {
      await _deviceControlChannel.invokeMethod('setPowerOnOffWithWeekly', {
        'powerOnHour': powerOnHour,
        'powerOnMinute': powerOnMinute,
        'powerOffHour': powerOffHour,
        'powerOffMinute': powerOffMinute,
        'weekdays': weekdays,
      });
      debugPrint(
        'setPowerOnOffWithWeekly succeeded: on=$powerOnHour:$powerOnMinute off=$powerOffHour:$powerOffMinute weekdays=$weekdays',
      );
    } on PlatformException catch (e, s) {
      debugPrint(
        'setPowerOnOffWithWeekly failed: ${e.message}, code: ${e.code}',
      );
      _errorHandler.reportError(
        module: 'DeviceControl',
        message: 'setPowerOnOffWithWeekly failed: ${e.message}',
        stackTrace: s.toString(),
        additionalInfo: {
          'platform': 'Android',
          'error_code': e.code,
          'powerOn': '$powerOnHour:$powerOnMinute',
          'powerOff': '$powerOffHour:$powerOffMinute',
          'weekdays': weekdays.join(','),
          'error_details': e.details?.toString() ?? 'No additional details',
        },
      );
      rethrow;
    } catch (e, s) {
      debugPrint('Unexpected error in setPowerOnOffWithWeekly: $e');
      _errorHandler.reportError(
        module: 'DeviceControl',
        message: 'Unexpected error in setPowerOnOffWithWeekly: $e',
        stackTrace: s.toString(),
        additionalInfo: {
          'platform': Platform.operatingSystem,
          'powerOn': '$powerOnHour:$powerOnMinute',
          'powerOff': '$powerOffHour:$powerOffMinute',
          'weekdays': weekdays.join(','),
          'error_type': e.runtimeType.toString(),
        },
      );
      rethrow;
    }
  }

  Future<void> reboot() async {
    debugPrint('正在尝试重启设备...');

    // 首先测试连接
    try {
      await testConnection();
      debugPrint('MethodChannel连接正常');
    } catch (e) {
      debugPrint('MethodChannel连接失败: $e');
      rethrow;
    }

    if (Platform.isAndroid) {
      // 1. 尝试Root方法
      try {
        debugPrint('步骤1: 尝试Root重启');
        await _deviceControlChannel.invokeMethod('rebootWithRoot');
        debugPrint('Root重启成功');
        return;
      } on PlatformException catch (e, s) {
        debugPrint('Root重启失败: ${e.message}');
        _errorHandler.reportError(
          module: 'DeviceControl',
          message: 'Root reboot failed',
          stackTrace: s.toString(),
          additionalInfo: {'step': 1, 'error': e.toString()},
        );
      }

      // 2. 尝试原生系统方法（替代插件方法）
      try {
        debugPrint('步骤2: 尝试原生系统重启');
        await _deviceControlChannel.invokeMethod('rebootWithSystem');
        debugPrint('原生系统重启成功');
        return;
      } on PlatformException catch (e, s) {
        debugPrint('原生系统重启失败: ${e.message}');
        _errorHandler.reportError(
          module: 'DeviceControl',
          message: 'System reboot failed',
          stackTrace: s.toString(),
          additionalInfo: {'step': 2, 'error': e.toString()},
        );
      }

      // 3. 使用YsApi保底方法
      try {
        debugPrint('步骤3: 尝试YsApi重启');
        await _deviceControlChannel.invokeMethod('rebootWithYsApi');
        debugPrint('YsApi重启成功');
        return;
      } on PlatformException catch (e, s) {
        debugPrint('YsApi重启失败: ${e.message}');
        _errorHandler.reportError(
          module: 'DeviceControl',
          message: 'YsApi reboot failed',
          stackTrace: s.toString(),
          additionalInfo: {'step': 3, 'error': e.toString()},
        );
      }

      // 所有方法都失败，抛出异常
      throw PlatformException(code: 'REBOOT_FAILED', message: '所有重启方法都失败了');
    } else {
      // 其他平台的重启逻辑
      await _runProcess('reboot', []);
    }
  }

  Future<void> shutdown() async {
    debugPrint('正在尝试关闭设备...');
    if (Platform.isAndroid) {
      // 1. 尝试Root方法
      try {
        debugPrint('步骤1: 尝试Root关机');
        await _deviceControlChannel.invokeMethod('shutdownWithRoot');
        debugPrint('Root关机成功');
        return;
      } on PlatformException catch (e, s) {
        debugPrint('Root关机失败: ${e.message}');
        _errorHandler.reportError(
          module: 'DeviceControl',
          message: 'Root shutdown failed',
          stackTrace: s.toString(),
          additionalInfo: {'step': 1, 'error': e.toString()},
        );
      }

      // 2. 尝试原生系统方法（替代插件方法）
      try {
        debugPrint('步骤2: 尝试原生系统关机');
        await _deviceControlChannel.invokeMethod('shutdownWithSystem');
        debugPrint('原生系统关机成功');
        return;
      } on PlatformException catch (e, s) {
        debugPrint('原生系统关机失败: ${e.message}');
        _errorHandler.reportError(
          module: 'DeviceControl',
          message: 'System shutdown failed',
          stackTrace: s.toString(),
          additionalInfo: {'step': 2, 'error': e.toString()},
        );
      }

      // 3. 使用YsApi保底方法
      try {
        debugPrint('步骤3: 尝试YsApi关机');
        await _deviceControlChannel.invokeMethod('shutdownWithYsApi');
        debugPrint('YsApi关机成功');
        return;
      } on PlatformException catch (e, s) {
        debugPrint('YsApi关机失败: ${e.message}');
        _errorHandler.reportError(
          module: 'DeviceControl',
          message: 'YsApi shutdown failed',
          stackTrace: s.toString(),
          additionalInfo: {'step': 3, 'error': e.toString()},
        );
      }

      // 所有方法都失败，抛出异常
      throw PlatformException(code: 'SHUTDOWN_FAILED', message: '所有关机方法都失败了');
    } else {
      // 其他平台的关机逻辑
      await _runProcess('shutdown', ['-h', 'now']);
    }
  }

  Future<void> _runProcess(String executable, List<String> arguments) async {
    debugPrint('Executing command: $executable ${arguments.join(' ')}');
    final result = await Process.run(executable, arguments);
    if (result.exitCode != 0) {
      throw ProcessException(
        executable,
        arguments,
        'Exit code: ${result.exitCode}\nSTDOUT: ${result.stdout}\nSTDERR: ${result.stderr}',
      );
    }
    debugPrint('Command executed with exit code ${result.exitCode}');
  }
}
