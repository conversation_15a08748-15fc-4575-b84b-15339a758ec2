import 'dart:io';
import 'package:flutter/foundation.dart';

import 'report_service.dart';

/// 全局错误处理服务
/// 负责捕获和上报应用运行过程中的错误和异常
class ErrorHandlerService {
  static final ErrorHandlerService _instance = ErrorHandlerService._internal();
  factory ErrorHandlerService() => _instance;
  ErrorHandlerService._internal();

  final ReportService _reportService = ReportService();
  bool _isInitialized = false;

  /// 初始化错误处理服务
  void initialize() {
    if (_isInitialized) return;

    // 捕获Flutter框架错误
    FlutterError.onError = (FlutterErrorDetails details) {
      _handleFlutterError(details);
    };

    // 捕获异步错误
    PlatformDispatcher.instance.onError = (error, stack) {
      _handleAsyncError(error, stack);
      return true;
    };

    // 捕获平台错误
    if (!kIsWeb) {
      _setupPlatformErrorHandling();
    }

    _isInitialized = true;
    debugPrint('ErrorHandlerService initialized');
  }

  /// 处理Flutter框架错误
  void _handleFlutterError(FlutterErrorDetails details) {
    // 在调试模式下仍然显示错误
    if (kDebugMode) {
      FlutterError.presentError(details);
    }

    // 上报错误
    _reportService.reportSystemError(
      module: 'flutter_framework',
      message: details.exception.toString(),
      errorCode: 'FLUTTER_ERROR',
      stackTrace: details.stack?.toString(),
      additionalInfo: {
        'library': details.library ?? 'unknown',
        'context': details.context?.toString() ?? 'unknown',
        'informationCollector': details.informationCollector?.toString(),
      },
    );
  }

  /// 处理异步错误
  void _handleAsyncError(Object error, StackTrace stack) {
    debugPrint('Async error caught: $error');
    debugPrint('Stack trace: $stack');

    // 上报错误
    _reportService.reportSystemError(
      module: 'async',
      message: error.toString(),
      errorCode: 'ASYNC_ERROR',
      stackTrace: stack.toString(),
      additionalInfo: {'errorType': error.runtimeType.toString()},
    );
  }

  /// 设置平台特定的错误处理
  void _setupPlatformErrorHandling() {
    if (Platform.isAndroid || Platform.isIOS) {
      // 移动平台错误处理
      _setupMobileErrorHandling();
    } else if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
      // 桌面平台错误处理
      _setupDesktopErrorHandling();
    }
  }

  /// 设置移动平台错误处理
  void _setupMobileErrorHandling() {
    // 可以在这里添加移动平台特定的错误处理逻辑
    debugPrint('Mobile platform error handling setup');
  }

  /// 设置桌面平台错误处理
  void _setupDesktopErrorHandling() {
    // 可以在这里添加桌面平台特定的错误处理逻辑
    debugPrint('Desktop platform error handling setup');
  }

  /// 手动上报错误
  void reportError({
    required String module,
    required String message,
    String? errorCode,
    String? stackTrace,
    Map<String, dynamic>? additionalInfo,
  }) {
    _reportService.reportSystemError(
      module: module,
      message: message,
      errorCode: errorCode,
      stackTrace: stackTrace,
      additionalInfo: additionalInfo,
    );
  }

  /// 上报应用日志
  void reportLog({
    required String level,
    required String module,
    required String message,
    Map<String, dynamic>? additionalInfo,
  }) {
    _reportService.reportApplicationLog(
      logLevel: level,
      module: module,
      message: message,
      additionalInfo: additionalInfo,
    );
  }

  /// 上报网络错误
  void reportNetworkError({
    required String url,
    required String method,
    required String errorMessage,
    int? statusCode,
    Map<String, dynamic>? additionalInfo,
  }) {
    final info = <String, dynamic>{
      'url': url,
      'method': method,
      'statusCode': statusCode,
      ...?additionalInfo,
    };

    _reportService.reportSystemError(
      module: 'network',
      message: errorMessage,
      errorCode: 'NETWORK_ERROR',
      additionalInfo: info,
    );
  }

  /// 上报文件操作错误
  void reportFileError({
    required String operation,
    required String filePath,
    required String errorMessage,
    Map<String, dynamic>? additionalInfo,
  }) {
    final info = <String, dynamic>{
      'operation': operation,
      'filePath': filePath,
      ...?additionalInfo,
    };

    _reportService.reportSystemError(
      module: 'file',
      message: errorMessage,
      errorCode: 'FILE_ERROR',
      additionalInfo: info,
    );
  }

  /// 上报MQTT连接错误
  void reportMqttError({
    required String operation,
    required String errorMessage,
    String? serverAddress,
    int? port,
    Map<String, dynamic>? additionalInfo,
  }) {
    final info = <String, dynamic>{
      'operation': operation,
      'serverAddress': serverAddress,
      'port': port,
      ...?additionalInfo,
    };

    _reportService.reportSystemError(
      module: 'mqtt',
      message: errorMessage,
      errorCode: 'MQTT_ERROR',
      additionalInfo: info,
    );
  }

  /// 上报设备信息错误
  void reportDeviceError({
    required String operation,
    required String errorMessage,
    Map<String, dynamic>? additionalInfo,
  }) {
    _reportService.reportSystemError(
      module: 'device',
      message: errorMessage,
      errorCode: 'DEVICE_ERROR',
      additionalInfo: {'operation': operation, ...?additionalInfo},
    );
  }

  /// 上报性能问题
  void reportPerformanceIssue({
    required String operation,
    required double duration,
    double? threshold,
    Map<String, dynamic>? additionalInfo,
  }) {
    final info = <String, dynamic>{
      'operation': operation,
      'duration': duration,
      'threshold': threshold,
      ...?additionalInfo,
    };

    _reportService.reportApplicationLog(
      logLevel: 'warning',
      module: 'performance',
      message: 'Performance issue detected: $operation took ${duration}s',
      additionalInfo: info,
    );
  }

  /// 上报用户操作日志
  void reportUserAction({
    required String action,
    required String screen,
    Map<String, dynamic>? additionalInfo,
  }) {
    final info = <String, dynamic>{
      'action': action,
      'screen': screen,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalInfo,
    };

    _reportService.reportApplicationLog(
      logLevel: 'info',
      module: 'user_action',
      message: 'User action: $action on $screen',
      additionalInfo: info,
    );
  }

  /// 上报应用生命周期事件
  void reportAppLifecycle({
    required String event,
    Map<String, dynamic>? additionalInfo,
  }) {
    _reportService.reportApplicationLog(
      logLevel: 'info',
      module: 'app_lifecycle',
      message: 'App lifecycle event: $event',
      additionalInfo: {
        'event': event,
        'timestamp': DateTime.now().toIso8601String(),
        ...?additionalInfo,
      },
    );
  }
}
