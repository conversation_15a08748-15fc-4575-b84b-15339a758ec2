import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import '../models/material_file_model.dart';
import '../utils/file_utils.dart';

enum DownloadStatus { pending, downloading, completed, failed, cancelled }

class DownloadTask {
  final String id;
  final MaterialFileModel file;
  final String url;
  final String localPath;
  DownloadStatus status;
  double progress;
  String? error;
  CancelToken? cancelToken;
  DateTime createdAt;
  DateTime? completedAt;

  DownloadTask({
    required this.id,
    required this.file,
    required this.url,
    required this.localPath,
    this.status = DownloadStatus.pending,
    this.progress = 0.0,
    this.error,
    this.cancelToken,
    DateTime? createdAt,
    this.completedAt,
  }) : createdAt = createdAt ?? DateTime.now();

  bool get isLargeFile => file.size > 10 * 1024 * 1024; // 10MB
}

class DownloadQueueManager with ChangeNotifier {
  static final DownloadQueueManager _instance =
      DownloadQueueManager._internal();
  factory DownloadQueueManager() => _instance;
  DownloadQueueManager._internal();

  final List<DownloadTask> _queue = [];
  final List<DownloadTask> _activeDownloads = [];
  final int _maxConcurrentDownloads = 2;
  final Dio _dio = Dio();

  List<DownloadTask> get queue => List.unmodifiable(_queue);
  List<DownloadTask> get activeDownloads => List.unmodifiable(_activeDownloads);
  List<DownloadTask> get completedDownloads =>
      _queue.where((task) => task.status == DownloadStatus.completed).toList();

  /// 添加下载任务
  Future<String> addDownloadTask({
    required MaterialFileModel file,
    required String serverUrl,
    VoidCallback? onCompleted,
    Function(String error)? onError,
  }) async {
    final taskId =
        'download_${DateTime.now().millisecondsSinceEpoch}_${file.id}';

    // 构建完整的文件URL
    String fileUrl = file.path;
    if (!fileUrl.startsWith('http')) {
      fileUrl = serverUrl.endsWith('/')
          ? '$serverUrl${file.path}'
          : '$serverUrl/${file.path}';
    }

    // 获取本地存储路径
    final directory = await getApplicationDocumentsDirectory();
    final localPath = '${directory.path}/downloads/${file.name}';

    // 确保下载目录存在
    final downloadDir = Directory('${directory.path}/downloads');
    if (!await downloadDir.exists()) {
      await downloadDir.create(recursive: true);
    }

    final task = DownloadTask(
      id: taskId,
      file: file,
      url: fileUrl,
      localPath: localPath,
      cancelToken: CancelToken(),
    );

    _queue.add(task);
    notifyListeners();

    // 如果是小文件，直接下载并等待完成
    if (!task.isLargeFile) {
      await _downloadFile(task);
      if (task.status == DownloadStatus.completed) {
        onCompleted?.call();
        // 自动打开小文件
        await _openFile(task);
      } else if (task.status == DownloadStatus.failed) {
        onError?.call(task.error ?? 'Download failed');
      }
    } else {
      // 大文件加入队列异步下载
      _processQueue();
    }

    return taskId;
  }

  /// 处理下载队列
  void _processQueue() {
    if (_activeDownloads.length >= _maxConcurrentDownloads) {
      return;
    }

    final pendingTasks = _queue
        .where((task) => task.status == DownloadStatus.pending)
        .toList();

    for (final task in pendingTasks) {
      if (_activeDownloads.length >= _maxConcurrentDownloads) {
        break;
      }

      _activeDownloads.add(task);
      _downloadFile(task).then((_) {
        _activeDownloads.remove(task);
        if (task.status == DownloadStatus.completed) {
          _showDownloadCompletedNotification(task);
        }
        _processQueue(); // 继续处理队列中的下一个任务
      });
    }
  }

  /// 下载文件
  Future<void> _downloadFile(DownloadTask task) async {
    try {
      task.status = DownloadStatus.downloading;
      notifyListeners();

      debugPrint('开始下载文件: ${task.file.name}');

      await _dio.download(
        task.url,
        task.localPath,
        cancelToken: task.cancelToken,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            task.progress = received / total;
            notifyListeners();
          }
        },
      );

      task.status = DownloadStatus.completed;
      task.completedAt = DateTime.now();
      task.progress = 1.0;

      debugPrint('文件下载完成: ${task.file.name}');
    } catch (e) {
      if (e is DioException && e.type == DioExceptionType.cancel) {
        task.status = DownloadStatus.cancelled;
        debugPrint('文件下载被取消: ${task.file.name}');
      } else {
        task.status = DownloadStatus.failed;
        task.error = e.toString();
        debugPrint('文件下载失败: ${task.file.name}, 错误: $e');
      }
    }

    notifyListeners();
  }

  /// 取消下载任务
  void cancelDownload(String taskId) {
    final task = _queue.firstWhere(
      (t) => t.id == taskId,
      orElse: () => throw ArgumentError('Task not found: $taskId'),
    );

    if (task.status == DownloadStatus.downloading) {
      task.cancelToken?.cancel();
      task.status = DownloadStatus.cancelled;
      _activeDownloads.remove(task);
      notifyListeners();
      _processQueue();
    }
  }

  /// 重试下载任务
  void retryDownload(String taskId) {
    final task = _queue.firstWhere(
      (t) => t.id == taskId,
      orElse: () => throw ArgumentError('Task not found: $taskId'),
    );

    if (task.status == DownloadStatus.failed) {
      task.status = DownloadStatus.pending;
      task.progress = 0.0;
      task.error = null;
      task.cancelToken = CancelToken();
      notifyListeners();
      _processQueue();
    }
  }

  /// 清除已完成的下载任务
  void clearCompletedTasks() {
    _queue.removeWhere((task) => task.status == DownloadStatus.completed);
    notifyListeners();
  }

  /// 获取任务状态
  DownloadTask? getTask(String taskId) {
    try {
      return _queue.firstWhere((t) => t.id == taskId);
    } catch (e) {
      return null;
    }
  }

  /// 显示下载完成通知
  void _showDownloadCompletedNotification(DownloadTask task) {
    // 这里可以显示系统通知或应用内通知
    debugPrint('下载完成通知: ${task.file.name}');
  }

  /// 打开文件
  Future<void> _openFile(DownloadTask task) async {
    try {
      final file = File(task.localPath);
      if (await file.exists()) {
        // 使用现有的文件处理系统打开文件
        final fileType = FileUtils.getFileType(task.localPath);
        debugPrint('打开文件: ${task.file.name}, 类型: $fileType');

        // 这里需要调用现有的文件打开逻辑
        // 可以通过回调或事件总线来通知主界面打开文件
      }
    } catch (e) {
      debugPrint('打开文件失败: ${task.file.name}, 错误: $e');
    }
  }

  /// 获取下载进度摘要
  Map<String, dynamic> getDownloadSummary() {
    final pending = _queue
        .where((t) => t.status == DownloadStatus.pending)
        .length;
    final downloading = _queue
        .where((t) => t.status == DownloadStatus.downloading)
        .length;
    final completed = _queue
        .where((t) => t.status == DownloadStatus.completed)
        .length;
    final failed = _queue
        .where((t) => t.status == DownloadStatus.failed)
        .length;

    return {
      'pending': pending,
      'downloading': downloading,
      'completed': completed,
      'failed': failed,
      'total': _queue.length,
    };
  }

  /// 清理所有任务
  void clearAllTasks() {
    // 取消所有正在下载的任务
    for (final task in _activeDownloads) {
      task.cancelToken?.cancel();
    }

    _queue.clear();
    _activeDownloads.clear();
    notifyListeners();
  }
}
