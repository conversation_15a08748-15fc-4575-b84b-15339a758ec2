import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import '../utils/h265_compatibility_detector.dart';
import '../config/device_specific_config.dart';
import '../video/policy.dart';

/// 视频解码策略管理器
/// 根据设备能力和视频格式自动选择最佳的解码方案
class VideoDecodingStrategyManager {
  static VideoDecodingStrategyManager? _instance;
  static VideoDecodingStrategyManager get instance =>
      _instance ??= VideoDecodingStrategyManager._();

  VideoDecodingStrategyManager._();

  H265CompatibilityInfo? _compatibilityInfo;

  /// 初始化策略管理器
  Future<void> initialize() async {
    try {
      _compatibilityInfo =
          await H265CompatibilityDetector.getCompatibilityInfo();
      debugPrint(
        'VideoDecodingStrategyManager: Initialized with compatibility info: $_compatibilityInfo',
      );
    } catch (e) {
      debugPrint('VideoDecodingStrategyManager: Error initializing: $e');
      _compatibilityInfo = H265CompatibilityInfo.createFallback();
    }
  }

  /// 获取兼容性信息
  H265CompatibilityInfo get compatibilityInfo {
    return _compatibilityInfo ?? H265CompatibilityInfo.createFallback();
  }

  /// 生成通用视频策略（用于引擎选择与硬解策略）
  VideoPolicy getPolicy(String videoPath) {
    final info = compatibilityInfo;
    final isH265 = _isH265Video(videoPath);

    // 引擎选择：默认 media_kit
    VideoEngineKind engine = VideoEngineKind.mediaKit;

    // 硬解策略映射 - 优先尝试硬解码策略
    HwDecPolicy hw;
    if (isH265 && info.isRK3568) {
      hw = HwDecPolicy.forceSoftware; // 关键：RK3568 + H.265 禁用硬解避免花屏
    } else if (info.hasHardwareDecoder) {
      // 只要有硬件解码器就优先尝试硬解，让MediaKitEngine处理失败回退
      hw = HwDecPolicy.auto; // 使用auto让引擎层决定是否回退
    } else {
      hw = HwDecPolicy.forceSoftware;
    }

    debugPrint('VideoDecodingStrategyManager: 视频路径: $videoPath');
    debugPrint('VideoDecodingStrategyManager: H.265视频: $isH265');
    debugPrint('VideoDecodingStrategyManager: 硬解码策略: $hw');

    return VideoPolicy(
      sourcePath: videoPath,
      hwDec: hw,
      engine: engine,
      codecHint: isH265 ? 'h265' : 'other',
    );
  }

  /// 为WebView获取最佳设置
  InAppWebViewSettings getOptimalWebViewSettings() {
    final info = compatibilityInfo;

    // 使用设备特定配置
    return DeviceSpecificConfig.instance.getWebViewSettingsForDevice(info);
  }

  /// 获取WebView视频优化JavaScript代码
  String getWebViewVideoOptimizationScript() {
    final info = compatibilityInfo;

    return '''
      (function() {
        console.log('H.265 Video Optimization Script Loading...');

        // 设备兼容性信息
        const DEVICE_INFO = {
          isRK3568: ${info.isRK3568},
          supportsH265: ${info.webViewSupportsH265},
          recommendedStrategy: '${info.recommendedStrategy.name}',
          softwarePerformance: '${info.softwareDecoderPerformance.name}'
        };

        // H.265视频检测和处理
        function detectAndOptimizeH265Videos() {
          const videos = document.querySelectorAll('video');

          videos.forEach(function(video) {
            const src = video.src || video.currentSrc;
            if (!src) return;

            // 检测是否为H.265视频
            const isH265 = isH265Video(src, video);

            if (isH265) {
              console.log('Detected H.265 video:', src);
              optimizeH265Video(video);
            }
          });
        }

        // 检测H.265视频
        function isH265Video(src, videoElement) {
          // 通过文件扩展名检测
          if (src.includes('.hevc') || src.includes('.h265')) {
            return true;
          }

          // 通过MIME类型检测
          if (videoElement.canPlayType) {
            const hevcSupport = videoElement.canPlayType('video/mp4; codecs="hev1"');
            const hevcSupport2 = videoElement.canPlayType('video/mp4; codecs="hvc1"');
            if (hevcSupport === 'probably' || hevcSupport2 === 'probably') {
              return true;
            }
          }

          return false;
        }

        // 优化H.265视频
        function optimizeH265Video(video) {
          if (DEVICE_INFO.isRK3568 || !DEVICE_INFO.supportsH265) {
            // RK3568或不支持H.265时的处理
            handleUnsupportedH265(video);
          } else {
            // 支持H.265时的优化
            applyH265Optimizations(video);
          }
        }

        // 处理不支持的H.265视频
        function handleUnsupportedH265(video) {
          console.warn('H.265 not supported on this device, applying fallback');

          // 禁用硬件加速
          video.style.willChange = 'auto';
          video.style.webkitTransform = 'none';

          // 降低质量设置
          video.preload = 'none';
          video.setAttribute('playsinline', 'true');

          // 添加错误处理
          video.addEventListener('error', function(e) {
            console.error('H.265 video error:', e);
            showVideoError(video, 'H.265视频在此设备上不受支持');
          });

          // 尝试软件解码提示
          if (video.error) {
            showVideoError(video, 'H.265视频播放失败，建议使用其他格式');
          }
        }

        // 应用H.265优化
        function applyH265Optimizations(video) {
          // 启用硬件加速
          video.style.willChange = 'transform';
          video.style.webkitTransform = 'translateZ(0)';

          // 优化预加载
          video.preload = 'metadata';
          video.setAttribute('playsinline', 'true');

          // 监控播放质量
          monitorVideoQuality(video);
        }

        // 监控视频播放质量
        function monitorVideoQuality(video) {
          let errorCount = 0;
          const maxErrors = 3;

          video.addEventListener('error', function() {
            errorCount++;
            if (errorCount >= maxErrors) {
              console.warn('Too many video errors, switching to fallback mode');
              handleUnsupportedH265(video);
            }
          });

          video.addEventListener('stalled', function() {
            console.warn('Video stalled, may indicate decoding issues');
          });
        }

        // 显示视频错误
        function showVideoError(video, message) {
          const errorDiv = document.createElement('div');
          errorDiv.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 20px;
            border-radius: 5px;
            text-align: center;
            z-index: 9999;
          `;
          errorDiv.textContent = message;

          const container = video.parentElement;
          if (container) {
            container.style.position = 'relative';
            container.appendChild(errorDiv);
          }
        }

        // 初始化
        detectAndOptimizeH265Videos();

        // 监听动态添加的视频
        const observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
              if (node.tagName === 'VIDEO') {
                const src = node.src || node.currentSrc;
                if (src && isH265Video(src, node)) {
                  optimizeH265Video(node);
                }
              }
            });
          });
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });

        console.log('H.265 Video Optimization Script Loaded');
      })();
    ''';
  }

  /// 检测是否为H.265视频
  bool _isH265Video(String videoPath) {
    final path = videoPath.toLowerCase();
    return path.contains('.hevc') ||
        path.contains('.h265') ||
        path.contains('hevc') ||
        path.contains('h265');
  }

  /// 重新检测兼容性（用于设备状态变化时）
  Future<void> refreshCompatibilityInfo() async {
    H265CompatibilityDetector.clearCache();
    await initialize();
  }
}
