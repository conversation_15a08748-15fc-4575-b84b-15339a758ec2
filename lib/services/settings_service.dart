import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/settings_model.dart';
import 'mac_address_service.dart';

class SettingsService {
  static const String _settingsKey = 'esop_settings';

  // Singleton instance
  static final SettingsService _instance = SettingsService._internal();
  factory SettingsService() => _instance;
  SettingsService._internal();

  SharedPreferences? _prefs;
  SettingsModel? _settings;

  Future<void> _init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // Save settings to shared preferences
  Future<bool> saveSettings(SettingsModel settings) async {
    try {
      await _init();
      final String settingsJson = jsonEncode(settings.toJson());
      final success = await _prefs!.setString(_settingsKey, settingsJson);
      if (success) {
        _settings = settings;
      }
      return success;
    } catch (e) {
      debugPrint('Error saving settings: $e');
      return false;
    }
  }

  // Load settings from shared preferences
  Future<SettingsModel> loadSettings() async {
    if (_settings != null) return _settings!;

    try {
      await _init();
      final String? settingsJson = _prefs!.getString(_settingsKey);

      if (settingsJson != null) {
        _settings = SettingsModel.fromJson(jsonDecode(settingsJson));
      } else {
        // Create default settings if none exist
        _settings = SettingsModel();
        await saveSettings(_settings!);
      }

      // Ensure MAC address is set
      if (_settings!.macAddress == null || _settings!.macAddress!.isEmpty) {
        final macAddress = await MacAddressService().getMacAddress();
        _settings!.macAddress = macAddress;
        await saveSettings(_settings!);
      }

      return _settings!;
    } catch (e) {
      debugPrint('Error loading settings: $e');
      // Return default settings on error
      return SettingsModel();
    }
  }
}
