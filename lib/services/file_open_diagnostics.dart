import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../services/file_service.dart';
import '../providers/file_provider.dart';

/// 文件打开诊断工具
/// 用于调试文件解压缩后无法正确打开的问题
class FileOpenDiagnostics {
  static final FileService _fileService = FileService();

  /// 诊断文件处理流程
  static Future<Map<String, dynamic>> diagnoseFileProcessing({
    required String downloadUrl,
    String? hash,
  }) async {
    final results = <String, dynamic>{};

    debugPrint('🔍 开始诊断文件处理流程...');
    debugPrint('📂 下载URL: $downloadUrl');
    debugPrint('🔑 Hash: $hash');

    try {
      // 1. 检查现有文件
      debugPrint('\n📋 步骤1: 检查现有文件');
      final existingFile = await _fileService.checkExistingFile(
        downloadUrl,
        hash: hash,
      );
      results['existing_file_check'] = {
        'found': existingFile != null,
        'details': existingFile?.keys.toList() ?? [],
      };

      if (existingFile != null) {
        debugPrint('✅ 找到现有文件');
        debugPrint('   📁 ZIP文件: ${existingFile['file']?.path}');
        debugPrint('   📂 解压目录: ${existingFile['extractedDirPath']}');
        debugPrint('   📄 data.json: ${existingFile['dataJsonFile']?.path}');

        // 验证文件是否真的存在
        results['file_verification'] = await _verifyExtractedFiles(
          existingFile,
        );
      } else {
        debugPrint('❌ 未找到现有文件');
        results['file_verification'] = {
          'all_exist': false,
          'missing_files': ['all'],
        };
      }

      // 2. 如果有data.json文件，尝试读取内容
      if (existingFile?['dataJsonFile'] != null) {
        debugPrint('\n📋 步骤2: 验证data.json内容');
        final dataJsonFile = existingFile!['dataJsonFile'] as File;
        results['data_json_validation'] = await _validateDataJson(dataJsonFile);
      }

      // 3. 检查assets目录
      if (existingFile?['extractedDirPath'] != null) {
        debugPrint('\n📋 步骤3: 检查assets目录');
        final extractedPath = existingFile!['extractedDirPath'] as String;
        results['assets_check'] = await _checkAssetsDirectory(extractedPath);
      }
    } catch (e) {
      debugPrint('❌ 诊断过程中出现错误: $e');
      results['error'] = e.toString();
    }

    debugPrint('\n📊 诊断完成');
    return results;
  }

  /// 验证解压的文件是否都存在
  static Future<Map<String, dynamic>> _verifyExtractedFiles(
    Map<String, dynamic> fileData,
  ) async {
    final results = <String, dynamic>{
      'all_exist': true,
      'missing_files': <String>[],
      'file_details': <String, dynamic>{},
    };

    try {
      // 检查ZIP文件
      final zipFile = fileData['file'] as File?;
      if (zipFile != null) {
        final zipExists = await zipFile.exists();
        results['file_details']['zip_file'] = {
          'path': zipFile.path,
          'exists': zipExists,
          'size': zipExists ? await zipFile.length() : 0,
        };
        if (!zipExists) {
          results['all_exist'] = false;
          results['missing_files'].add('zip_file');
        }
      }

      // 检查解压目录
      final extractedDirPath = fileData['extractedDirPath'] as String?;
      if (extractedDirPath != null) {
        final extractedDir = Directory(extractedDirPath);
        final dirExists = await extractedDir.exists();
        results['file_details']['extracted_dir'] = {
          'path': extractedDirPath,
          'exists': dirExists,
        };
        if (!dirExists) {
          results['all_exist'] = false;
          results['missing_files'].add('extracted_dir');
        }
      }

      // 检查data.json文件
      final dataJsonFile = fileData['dataJsonFile'] as File?;
      if (dataJsonFile != null) {
        final jsonExists = await dataJsonFile.exists();
        results['file_details']['data_json'] = {
          'path': dataJsonFile.path,
          'exists': jsonExists,
          'size': jsonExists ? await dataJsonFile.length() : 0,
        };
        if (!jsonExists) {
          results['all_exist'] = false;
          results['missing_files'].add('data_json');
        }
      }
    } catch (e) {
      debugPrint('验证文件时出错: $e');
      results['error'] = e.toString();
      results['all_exist'] = false;
    }

    return results;
  }

  /// 验证data.json文件内容
  static Future<Map<String, dynamic>> _validateDataJson(
    File dataJsonFile,
  ) async {
    final results = <String, dynamic>{
      'valid': false,
      'readable': false,
      'parseable': false,
      'content_summary': <String, dynamic>{},
    };

    try {
      // 检查文件是否可读
      final content = await dataJsonFile.readAsString();
      results['readable'] = true;
      results['content_length'] = content.length;

      debugPrint('✅ data.json文件可读，长度: ${content.length}');

      // 尝试解析JSON
      final jsonData = await compute(_parseJson, content);
      results['parseable'] = jsonData != null;

      if (jsonData != null) {
        results['valid'] = true;
        results['content_summary'] = {
          'has_canvas_ratio': jsonData.containsKey('canvasRatio'),
          'has_template_sm': jsonData.containsKey('templateSm'),
          'template_count': jsonData['templateSm']?.length ?? 0,
          'canvas_ratio': jsonData['canvasRatio'],
        };

        debugPrint('✅ data.json解析成功');
        debugPrint('   🎨 Canvas尺寸: ${jsonData['canvasRatio']}');
        debugPrint('   🧩 模板组件数量: ${jsonData['templateSm']?.length ?? 0}');
      } else {
        debugPrint('❌ data.json解析失败');
      }
    } catch (e) {
      debugPrint('❌ 验证data.json时出错: $e');
      results['error'] = e.toString();
    }

    return results;
  }

  /// 静态JSON解析方法（用于compute）
  static dynamic _parseJson(String content) {
    try {
      return json.decode(content);
    } catch (e) {
      return null;
    }
  }

  /// 检查assets目录结构
  static Future<Map<String, dynamic>> _checkAssetsDirectory(
    String extractedPath,
  ) async {
    final results = <String, dynamic>{
      'assets_dir_exists': false,
      'asset_files': <String>[],
      'directory_structure': <String>[],
    };

    try {
      final assetsDir = Directory('$extractedPath/assets');
      final assetsDirExists = await assetsDir.exists();
      results['assets_dir_exists'] = assetsDirExists;

      if (assetsDirExists) {
        debugPrint('✅ assets目录存在: ${assetsDir.path}');

        // 列出所有文件
        final entities = await assetsDir.list(recursive: true).toList();
        final files = entities.whereType<File>().map((e) => e.path).toList();
        final dirs = entities
            .whereType<Directory>()
            .map((e) => e.path)
            .toList();

        results['asset_files'] = files;
        results['directory_structure'] = dirs;

        debugPrint('   📁 子目录数量: ${dirs.length}');
        debugPrint('   📄 文件数量: ${files.length}');

        // 列出前几个文件作为示例
        final sampleFiles = files.take(5).toList();
        if (sampleFiles.isNotEmpty) {
          debugPrint('   📋 示例文件:');
          for (final file in sampleFiles) {
            debugPrint('     - ${file.split('/').last}');
          }
        }
      } else {
        debugPrint('❌ assets目录不存在: ${assetsDir.path}');

        // 检查解压目录中有什么
        final extractedDir = Directory(extractedPath);
        if (await extractedDir.exists()) {
          final contents = await extractedDir.list().toList();
          results['extracted_contents'] = contents
              .map((e) => e.path.split('/').last)
              .toList();
          debugPrint('   📂 解压目录内容: ${results['extracted_contents']}');
        }
      }
    } catch (e) {
      debugPrint('❌ 检查assets目录时出错: $e');
      results['error'] = e.toString();
    }

    return results;
  }

  /// 诊断FileProvider状态
  static Map<String, dynamic> diagnoseFileProvider(FileProvider fileProvider) {
    final results = <String, dynamic>{};

    debugPrint('\n🔍 诊断FileProvider状态...');

    results['state'] = fileProvider.state.toString();
    results['has_downloaded_file'] = fileProvider.downloadedFile != null;
    results['has_data_json_file'] = fileProvider.dataJsonFile != null;
    results['has_extracted_dir'] = fileProvider.extractedDirPath != null;
    results['error'] = fileProvider.error;

    debugPrint('📊 FileProvider状态: ${fileProvider.state}');
    debugPrint('📦 下载文件: ${fileProvider.downloadedFile?.path ?? 'null'}');
    debugPrint('📄 data.json: ${fileProvider.dataJsonFile?.path ?? 'null'}');
    debugPrint('📂 解压目录: ${fileProvider.extractedDirPath ?? 'null'}');
    debugPrint(
      '❌ 错误信息: ${fileProvider.error.isEmpty ? 'none' : fileProvider.error}',
    );

    return results;
  }

  /// 打印完整的诊断报告
  static void printDiagnosticSummary(Map<String, dynamic> results) {
    debugPrint('\n📋 ====== 文件处理诊断报告 ======');

    if (results.containsKey('error')) {
      debugPrint('❌ 诊断过程出现错误: ${results['error']}');
      return;
    }

    // 现有文件检查
    final existingCheck =
        results['existing_file_check'] as Map<String, dynamic>?;
    if (existingCheck != null) {
      debugPrint('\n1️⃣ 现有文件检查: ${existingCheck['found'] ? '✅ 通过' : '❌ 失败'}');
    }

    // 文件验证
    final fileVerification =
        results['file_verification'] as Map<String, dynamic>?;
    if (fileVerification != null) {
      debugPrint(
        '2️⃣ 文件验证: ${fileVerification['all_exist'] ? '✅ 通过' : '❌ 失败'}',
      );
      if (!(fileVerification['all_exist'] as bool)) {
        final missingFiles = fileVerification['missing_files'] as List;
        debugPrint('   缺少文件: ${missingFiles.join(', ')}');
      }
    }

    // data.json验证
    final dataJsonValidation =
        results['data_json_validation'] as Map<String, dynamic>?;
    if (dataJsonValidation != null) {
      debugPrint(
        '3️⃣ data.json验证: ${dataJsonValidation['valid'] ? '✅ 通过' : '❌ 失败'}',
      );
      if (dataJsonValidation.containsKey('content_summary')) {
        final summary =
            dataJsonValidation['content_summary'] as Map<String, dynamic>;
        debugPrint('   模板组件数量: ${summary['template_count']}');
        debugPrint('   Canvas尺寸: ${summary['canvas_ratio']}');
      }
    }

    // assets检查
    final assetsCheck = results['assets_check'] as Map<String, dynamic>?;
    if (assetsCheck != null) {
      debugPrint(
        '4️⃣ Assets检查: ${assetsCheck['assets_dir_exists'] ? '✅ 通过' : '❌ 失败'}',
      );
      if (assetsCheck['assets_dir_exists'] as bool) {
        final fileCount = (assetsCheck['asset_files'] as List).length;
        debugPrint('   资源文件数量: $fileCount');
      }
    }

    debugPrint('\n============================');
  }
}
