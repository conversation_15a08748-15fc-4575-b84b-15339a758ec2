import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:mqtt5_client/mqtt5_client.dart';
import 'package:mqtt5_client/mqtt5_server_client.dart';
import '../models/mqtt_message_model.dart';
import '../models/mqtt_connection_state.dart' as AppConnectionState;
import 'settings_service.dart';
import 'broadcast_service.dart';
import 'report_service.dart';
import 'error_handler_service.dart';
import 'device_control_service.dart';
import 'ota_service.dart';
import 'equipment_api_service.dart';
import 'message_handler_service.dart';
import 'mqtt_command_queue.dart';

// A data class to hold messages that are queued for sending when offline.
class _QueuedMessage {
  final String topic;
  final String payload;
  final MqttQos qos;

  _QueuedMessage(this.topic, this.payload, this.qos);
}

class MqttService {
  MqttServerClient? _client;
  final SettingsService _settingsService = SettingsService();
  final BroadcastService _broadcastService = BroadcastService();
  final ReportService _reportService = ReportService();
  final ErrorHandlerService _errorHandler = ErrorHandlerService();
  final DeviceControlService _deviceControlService = DeviceControlService();
  final OtaService _otaService = OtaService();
  final EquipmentApiService _equipmentApiService = EquipmentApiService();
  final MessageHandlerService _messageHandlerService = MessageHandlerService();

  /// Constructor - initializes network connectivity monitoring
  MqttService() {
    _initializeNetworkMonitoring();
  }
  late final MqttCommandQueue _commandQueue = MqttCommandQueue(
    cancelHeavyOps: () {
      try {
        final fp = _fileProvider;
        if (fp != null) {
          (fp as dynamic).cancelCurrentOperation();
        }
      } catch (_) {}
    },
  );

  // FileProvider instance for file processing
  dynamic _fileProvider;

  // Stream controllers for external listeners
  final StreamController<AppConnectionState.MqttConnectionState>
  _connectionStateController =
      StreamController<AppConnectionState.MqttConnectionState>.broadcast();
  final StreamController<MqttMessageModel> _messageController =
      StreamController<MqttMessageModel>.broadcast();

  // Public streams
  Stream<AppConnectionState.MqttConnectionState> get connectionState =>
      _connectionStateController.stream;
  Stream<MqttMessageModel> get messages => _messageController.stream;

  // Internal state management
  AppConnectionState.MqttConnectionState _currentState =
      AppConnectionState.MqttConnectionState.disconnected;
  AppConnectionState.MqttConnectionState get currentState => _currentState;

  Timer? _reconnectTimer;
  Timer? _connectionHealthTimer;
  Timer? _pingTimer;
  int _reconnectAttempts = 0;
  int _authFailureCount = 0;
  DateTime? _lastAuthFailureTime;
  final Map<String, MqttQos> _subscribedTopics = {};
  final List<_QueuedMessage> _offlineMessageQueue = [];
  final List<Map<String, dynamic>> _pendingMessages = [];
  DateTime? _lastPongReceived;
  DateTime? _lastPingSent;

  // Network connectivity monitoring
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  List<ConnectivityResult> _currentConnectivity = [ConnectivityResult.none];
  bool _wasNetworkDisconnected = false;

  // Connection quality monitoring
  int _consecutivePingFailures = 0;
  int _messagesSentSinceLastPong = 0;
  final List<Duration> _recentPingLatencies = [];
  static const int _maxPingLatencyHistory = 10;

  // Connection history tracking
  final List<Map<String, dynamic>> _connectionHistory = [];
  static const int _maxConnectionHistoryEntries = 50;

  // --- Configuration for Exponential Backoff ---
  static const int _minReconnectDelaySeconds = 2;
  static const int _maxReconnectDelaySeconds = 60;
  static const int _maxReconnectAttempts = 10;

  // --- MQTT Connection Health Configuration ---
  static const int _keepAlivePeriodSeconds = 90;
  static const int _connectionTimeoutSeconds = 120;
  static const int _authFailureCooldownMinutes = 10;
  static const int _maxAuthFailures = 3;

  // --- Enhanced Connection Monitoring Configuration ---
  static const int _pingIntervalSeconds = 15; // 主动ping间隔
  static const int _maxConsecutivePingFailures = 3; // 最大连续ping失败次数
  static const int _networkReconnectDelaySeconds = 2; // 网络恢复后重连延迟
  static const int _maxMessagesWithoutPong = 5; // 没有pong响应时最大发送消息数

  // --- Public getters for testing ---
  @visibleForTesting
  static int get keepAlivePeriodSeconds => _keepAlivePeriodSeconds;
  @visibleForTesting
  static int get connectionTimeoutSeconds => _connectionTimeoutSeconds;
  @visibleForTesting
  static int get authFailureCooldownMinutes => _authFailureCooldownMinutes;
  @visibleForTesting
  static int get maxAuthFailures => _maxAuthFailures;
  @visibleForTesting
  static int get maxReconnectAttempts => _maxReconnectAttempts;

  // --- Public API ---

  /// Set the FileProvider instance for file processing
  void setFileProvider(dynamic fileProvider) {
    _fileProvider = fileProvider;
    if (_fileProvider != null && _pendingMessages.isNotEmpty) {
      debugPrint(
        '[MQTT] Flushing ${_pendingMessages.length} pending messages after FileProvider set.',
      );
      final pending = List<Map<String, dynamic>>.from(_pendingMessages);
      _pendingMessages.clear();
      for (final pm in pending) {
        try {
          _enqueueMqttCommand(
            pm['topic'] as String,
            pm['payload'] as String,
            pm['json'] as Map<String, dynamic>,
          );
        } catch (e) {
          debugPrint('[MQTT] Error enqueueing pending message: $e');
        }
      }
    }
  }

  /// Connects to the MQTT broker using MQTT 5.0 protocol
  Future<void> connect() async {
    // Check for authentication failure cooldown
    if (_isInAuthCooldown()) {
      final remainingCooldown = _getAuthCooldownRemaining();
      debugPrint(
        'MQTT connection blocked due to authentication failures. Remaining cooldown: ${remainingCooldown.inMinutes} minutes.',
      );
      _updateConnectionState(AppConnectionState.MqttConnectionState.error);
      return;
    }

    if (_currentState == AppConnectionState.MqttConnectionState.connected ||
        _currentState == AppConnectionState.MqttConnectionState.connecting) {
      debugPrint(
        'MQTT connect() called while already connected or connecting.',
      );
      return;
    }

    _updateConnectionState(AppConnectionState.MqttConnectionState.connecting);

    try {
      var settings = await _settingsService.loadSettings();
      if (settings.mqttServerAddress == null ||
          settings.mqttServerAddress!.isEmpty) {
        debugPrint('MQTT server address not set, attempting discovery...');
        final discoveryResult = await _broadcastService.listenForBroadcast();
        if (discoveryResult != null &&
            discoveryResult['serverAddress'] != null) {
          settings.mqttServerAddress = discoveryResult['serverAddress']!;
          if (discoveryResult['mqttPort'] != null) {
            settings.mqttPort = discoveryResult['mqttPort']!;
          }
          await _settingsService.saveSettings(settings);
          settings = await _settingsService.loadSettings();
        } else {
          throw Exception('MQTT server discovery failed.');
        }
      }

      // 严格验证认证信息 - 按照内存规范
      if (settings.macAddress == null || settings.macAddress!.isEmpty) {
        throw Exception('MAC地址为空，无法进行MQTT认证');
      }
      if (settings.registrationCode == null ||
          settings.registrationCode!.isEmpty) {
        throw Exception('注册码为空，无法进行MQTT认证');
      }

      final server = settings.mqttServerAddress!;
      final port = int.tryParse(settings.mqttPort ?? '1883') ?? 1883;
      // 严格使用MAC地址作为客户端ID - 按照内存规范
      final clientId = settings.macAddress!;

      _client = MqttServerClient(server, clientId);
      _client!.port = port;
      _client!.keepAlivePeriod = _keepAlivePeriodSeconds;
      _client!.logging(on: false);

      // Set up event handlers for MQTT 5.0
      _client!.onConnected = _onConnected;
      _client!.onDisconnected = _onDisconnected;
      _client!.pongCallback = _onPong;

      // Create MQTT 5.0 connection message
      final connMessage = MqttConnectMessage()
          .withClientIdentifier(clientId)
          .authenticateAs(
            settings.macAddress!, // 严格使用MAC地址作为用户名
            settings.registrationCode!, // 严格使用注册码作为密码
          );

      _client!.connectionMessage = connMessage;

      debugPrint(
        'MQTT 5.0 connecting with credentials - Username: ${settings.macAddress}, Password source: registration code',
      );

      await _client!.connect();
    } catch (e, stacktrace) {
      debugPrint('MQTT connection error: $e');

      // 输出详细认证信息用于诊断 - 按照内存规范
      try {
        final settings = await _settingsService.loadSettings();
        debugPrint('MQTT认证失败详细信息:');
        debugPrint('- 用户名来源: MAC地址 (${settings.macAddress ?? "空"})');
        debugPrint(
          '- 密码来源: 注册码 (${settings.registrationCode?.isNotEmpty == true ? "已设置" : "空"})',
        );
        debugPrint(
          '- 认证失败时的具体凭据: 用户名=${settings.macAddress}, 密码长度=${settings.registrationCode?.length ?? 0}',
        );
      } catch (_) {}

      if (_isAuthenticationError(e.toString())) {
        _handleAuthFailure();
      }

      _errorHandler.reportMqttError(
        operation: 'connect',
        errorMessage: e.toString(),
        additionalInfo: {'stacktrace': stacktrace.toString()},
      );
      _updateConnectionState(AppConnectionState.MqttConnectionState.error);
      _onDisconnected();
    }
  }

  /// Disconnects from the MQTT broker gracefully
  void disconnect() {
    if (_client?.connectionStatus?.state == MqttConnectionState.connected) {
      _updateConnectionState(
        AppConnectionState.MqttConnectionState.disconnecting,
      );
      _client?.disconnect();
    }
    _reconnectTimer?.cancel();
    _connectionHealthTimer?.cancel();
  }

  /// Subscribes to a given topic with a specific QoS
  void subscribe(String topic, MqttQos qos) {
    // 验证主题是否符合规范 - 按照内存规范
    if (!_isValidTopic(topic)) {
      debugPrint('MQTT主题不符合规范，替换特殊字符: $topic');
      topic = _sanitizeTopic(topic);
    }

    if (_subscribedTopics.containsKey(topic) &&
        _subscribedTopics[topic] == qos) {
      debugPrint('Already subscribed to topic: $topic with the same QoS.');
      return;
    }

    _subscribedTopics[topic] = qos;
    if (_currentState == AppConnectionState.MqttConnectionState.connected) {
      try {
        _client?.subscribe(topic, qos);
        debugPrint('Subscribed to topic: $topic with QoS: $qos');
      } catch (e) {
        debugPrint('订阅主题失败 - 主题: $topic, 原因: $e');
        // 实现重试机制 - 按照内存规范
        _retrySubscription(topic, qos);
      }
    }
  }

  /// Publishes a message to a given topic
  void publish(String topic, String payload, MqttQos qos) {
    // 验证主题是否符合规范
    if (!_isValidTopic(topic)) {
      debugPrint('MQTT主题不符合规范，替换特殊字符: $topic');
      topic = _sanitizeTopic(topic);
    }

    if (_currentState != AppConnectionState.MqttConnectionState.connected) {
      if (qos != MqttQos.atMostOnce) {
        debugPrint('MQTT client offline. Queuing message to topic: $topic');
        _offlineMessageQueue.add(_QueuedMessage(topic, payload, qos));
      } else {
        debugPrint('MQTT client offline. Discarding QoS 0 message.');
      }
      return;
    }

    final builder = MqttPayloadBuilder();
    builder.addString(payload);
    _client!.publishMessage(topic, qos, builder.payload!);

    // Track messages sent for connection quality monitoring
    _messagesSentSinceLastPong++;
  }

  /// Disposes of all resources used by the service
  void dispose() {
    _reconnectTimer?.cancel();
    _connectionHealthTimer?.cancel();
    _pingTimer?.cancel();
    _connectivitySubscription?.cancel();
    _connectionStateController.close();
    _messageController.close();
    _broadcastService.close();
    disconnect();
  }

  // --- Network Connectivity Monitoring ---

  /// Initialize network connectivity monitoring
  void _initializeNetworkMonitoring() {
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
      _onConnectivityChanged,
      onError: (error) {
        debugPrint('Network connectivity monitoring error: $error');
      },
    );

    // Get initial connectivity state
    Connectivity()
        .checkConnectivity()
        .then((result) {
          _currentConnectivity = result;
          debugPrint('Initial network connectivity: $_currentConnectivity');
        })
        .catchError((error) {
          debugPrint('Error checking initial connectivity: $error');
        });
  }

  /// Handle network connectivity changes
  void _onConnectivityChanged(List<ConnectivityResult> result) {
    final previousConnectivity = _currentConnectivity;
    _currentConnectivity = result;

    final wasDisconnected = previousConnectivity.contains(
      ConnectivityResult.none,
    );
    final isNowConnected = !result.contains(ConnectivityResult.none);

    debugPrint(
      'Network connectivity changed: $previousConnectivity -> $result',
    );

    if (wasDisconnected && isNowConnected) {
      // Network was disconnected and now connected
      _wasNetworkDisconnected = false;
      debugPrint(
        'Network connectivity restored, attempting MQTT reconnection...',
      );

      // Reset connection quality metrics
      _consecutivePingFailures = 0;
      _messagesSentSinceLastPong = 0;

      // Schedule reconnection after a short delay
      Timer(Duration(seconds: _networkReconnectDelaySeconds), () {
        if (_currentState != AppConnectionState.MqttConnectionState.connected &&
            _currentState !=
                AppConnectionState.MqttConnectionState.connecting) {
          connect();
        }
      });
    } else if (!wasDisconnected && !isNowConnected) {
      // Network just disconnected
      _wasNetworkDisconnected = true;
      debugPrint(
        'Network connectivity lost, MQTT will attempt to reconnect when network is restored',
      );
    }
  }

  /// Check if device has network connectivity
  bool _hasNetworkConnectivity() {
    return !_currentConnectivity.contains(ConnectivityResult.none);
  }

  /// Record connection event for analysis
  void _recordConnectionEvent(
    String event, {
    String? reason,
    Map<String, dynamic>? additionalData,
  }) {
    final entry = {
      'timestamp': DateTime.now().toIso8601String(),
      'event': event,
      'reason': reason,
      'networkConnectivity': _currentConnectivity
          .map((e) => e.toString())
          .toList(),
      'reconnectAttempts': _reconnectAttempts,
      'consecutivePingFailures': _consecutivePingFailures,
      'messagesSentSinceLastPong': _messagesSentSinceLastPong,
      'averagePingLatency': _getAveragePingLatency(),
      ...?additionalData,
    };

    _connectionHistory.add(entry);

    // Keep only recent entries
    if (_connectionHistory.length > _maxConnectionHistoryEntries) {
      _connectionHistory.removeAt(0);
    }

    debugPrint(
      'MQTT Connection Event: $event ${reason != null ? "($reason)" : ""}',
    );
  }

  /// Get average ping latency from recent measurements
  double? _getAveragePingLatency() {
    if (_recentPingLatencies.isEmpty) return null;

    final totalMs = _recentPingLatencies
        .map((latency) => latency.inMilliseconds)
        .reduce((a, b) => a + b);

    return totalMs / _recentPingLatencies.length;
  }

  /// Get connection quality metrics for debugging
  Map<String, dynamic> getConnectionQualityMetrics() {
    return {
      'currentState': _currentState.toString(),
      'networkConnectivity': _currentConnectivity
          .map((e) => e.toString())
          .toList(),
      'reconnectAttempts': _reconnectAttempts,
      'consecutivePingFailures': _consecutivePingFailures,
      'messagesSentSinceLastPong': _messagesSentSinceLastPong,
      'averagePingLatency': _getAveragePingLatency(),
      'lastPongReceived': _lastPongReceived?.toIso8601String(),
      'lastPingSent': _lastPingSent?.toIso8601String(),
      'connectionHistory': _connectionHistory
          .take(10)
          .toList(), // Last 10 events
    };
  }

  // --- Authentication Failure Protection ---

  bool _isInAuthCooldown() {
    if (_authFailureCount < _maxAuthFailures || _lastAuthFailureTime == null) {
      return false;
    }
    final cooldownEnd = _lastAuthFailureTime!.add(
      Duration(minutes: _authFailureCooldownMinutes),
    );
    return DateTime.now().isBefore(cooldownEnd);
  }

  Duration _getAuthCooldownRemaining() {
    if (_lastAuthFailureTime == null) {
      return Duration.zero;
    }
    final cooldownEnd = _lastAuthFailureTime!.add(
      Duration(minutes: _authFailureCooldownMinutes),
    );
    final remaining = cooldownEnd.difference(DateTime.now());
    return remaining.isNegative ? Duration.zero : remaining;
  }

  bool _isAuthenticationError(String errorMessage) {
    final authErrorPatterns = [
      'authentication failed',
      'not authorized',
      'bad username or password',
      'connection refused',
      'client identifier rejected',
    ];
    final lowerError = errorMessage.toLowerCase();
    return authErrorPatterns.any((pattern) => lowerError.contains(pattern));
  }

  void _handleAuthFailure() {
    _authFailureCount++;
    _lastAuthFailureTime = DateTime.now();
    debugPrint('Authentication failure #$_authFailureCount detected.');
    if (_authFailureCount >= _maxAuthFailures) {
      debugPrint(
        'Maximum authentication failures reached. Entering cooldown period for $_authFailureCooldownMinutes minutes.',
      );
    }
  }

  // --- Topic Validation and Sanitization - 按照内存规范 ---

  bool _isValidTopic(String topic) {
    final utf8Bytes = utf8.encode(topic);
    if (utf8Bytes.length > 65535) {
      return false;
    }
    final invalidChars = [' ', '#', '+'];
    return !invalidChars.any((char) => topic.contains(char));
  }

  String _sanitizeTopic(String topic) {
    String sanitized = topic;
    sanitized = sanitized.replaceAll(' ', '_');
    sanitized = sanitized.replaceAll('#', '_');
    sanitized = sanitized.replaceAll('+', '_');

    while (utf8.encode(sanitized).length > 65535) {
      sanitized = sanitized.substring(0, sanitized.length - 1);
    }
    return sanitized;
  }

  void _retrySubscription(String topic, MqttQos qos, {int maxRetries = 3}) {
    int attempts = 0;
    Timer.periodic(Duration(seconds: 5), (timer) {
      attempts++;
      if (attempts > maxRetries) {
        timer.cancel();
        debugPrint('订阅重试失败，达到最大重试次数: $topic');
        return;
      }
      if (_currentState == AppConnectionState.MqttConnectionState.connected) {
        try {
          _client?.subscribe(topic, qos);
          debugPrint('订阅重试成功: $topic (第$attempts次尝试)');
          timer.cancel();
        } catch (e) {
          debugPrint('订阅重试失败: $topic (第$attempts次尝试), 错误: $e');
        }
      }
    });
  }

  // --- Enhanced Connection Health Check ---

  void _startConnectionHealthCheck() {
    _connectionHealthTimer?.cancel();
    _pingTimer?.cancel();

    // Start periodic health check
    _connectionHealthTimer = Timer.periodic(
      Duration(seconds: _keepAlivePeriodSeconds),
      (_) {
        _checkConnectionHealth();
      },
    );

    // Start periodic ping sending
    _pingTimer = Timer.periodic(Duration(seconds: _pingIntervalSeconds), (_) {
      _sendPing();
    });
  }

  void _checkConnectionHealth() {
    if (_currentState != AppConnectionState.MqttConnectionState.connected) {
      return;
    }

    // Check if we have network connectivity
    if (!_hasNetworkConnectivity()) {
      debugPrint('No network connectivity detected during health check');
      return;
    }

    final now = DateTime.now();

    // Check pong timeout
    if (_lastPongReceived != null) {
      final timeSinceLastPong = now.difference(_lastPongReceived!);
      if (timeSinceLastPong.inSeconds > _connectionTimeoutSeconds) {
        debugPrint(
          'Connection health check failed. No pong received for ${timeSinceLastPong.inSeconds} seconds. Force reconnecting...',
        );
        _forceReconnect('Pong timeout');
        return;
      }
    }

    // Check consecutive ping failures
    if (_consecutivePingFailures >= _maxConsecutivePingFailures) {
      debugPrint(
        'Connection health check failed. $_consecutivePingFailures consecutive ping failures. Force reconnecting...',
      );
      _forceReconnect('Consecutive ping failures');
      return;
    }

    // Check if too many messages sent without pong response
    if (_messagesSentSinceLastPong >= _maxMessagesWithoutPong) {
      debugPrint(
        'Connection health check failed. $_messagesSentSinceLastPong messages sent without pong response. Force reconnecting...',
      );
      _forceReconnect('Too many messages without pong');
      return;
    }
  }

  void _sendPing() {
    if (_currentState != AppConnectionState.MqttConnectionState.connected ||
        _client == null) {
      return;
    }

    try {
      _lastPingSent = DateTime.now();
      // MQTT5 client uses keep-alive mechanism automatically
      // We'll use a lightweight publish to a system topic as ping
      final builder = MqttPayloadBuilder();
      builder.addString('ping');
      _client!.publishMessage(
        '\$SYS/ping',
        MqttQos.atMostOnce,
        builder.payload!,
      );
      debugPrint('MQTT ping sent at $_lastPingSent');
    } catch (e) {
      debugPrint('Failed to send MQTT ping: $e');
      _consecutivePingFailures++;
    }
  }

  void _forceReconnect(String reason) {
    debugPrint('Force reconnecting MQTT due to: $reason');

    // Record force reconnect event
    _recordConnectionEvent('force_reconnect', reason: reason);

    _client?.disconnect();
    _updateConnectionState(AppConnectionState.MqttConnectionState.disconnected);
    Timer(Duration(milliseconds: 500), () {
      if (_currentState ==
          AppConnectionState.MqttConnectionState.disconnected) {
        connect();
      }
    });
  }

  // --- Internal Callbacks and Logic ---

  void _onConnected() async {
    debugPrint('MQTT 5.0 client connected.');
    _updateConnectionState(AppConnectionState.MqttConnectionState.connected);

    // Record successful connection
    _recordConnectionEvent(
      'connected',
      additionalData: {
        'previousReconnectAttempts': _reconnectAttempts,
        'wasNetworkDisconnected': _wasNetworkDisconnected,
      },
    );

    _reconnectAttempts = 0;
    _authFailureCount = 0;
    _lastAuthFailureTime = null;
    _reconnectTimer?.cancel();
    _startConnectionHealthCheck();
    _lastPongReceived = DateTime.now();

    try {
      final settings = await _settingsService.loadSettings();
      if (settings.groupName != null &&
          settings.groupName!.isNotEmpty &&
          settings.deviceAlias != null &&
          settings.deviceAlias!.isNotEmpty) {
        final newTopic = settings.generateMqttTopic();
        debugPrint('Automatically subscribing to topic: $newTopic');
        subscribe(newTopic, MqttQos.atLeastOnce);
      } else {
        debugPrint('MQTT连接已建立，但不订阅topic，因为组名或设备别名为空');
      }
    } catch (e) {
      debugPrint('Could not subscribe to topic: $e');
    }

    _resubscribeTopics();
    _sendOfflineMessages();

    // Start listening to MQTT 5.0 message stream
    _client!.updates.listen(_onMessage);
  }

  void _onDisconnected() {
    debugPrint('MQTT client disconnected.');
    _connectionHealthTimer?.cancel();
    _pingTimer?.cancel();

    if (_currentState != AppConnectionState.MqttConnectionState.disconnecting) {
      _updateConnectionState(
        AppConnectionState.MqttConnectionState.disconnected,
      );

      // Determine disconnect reason for intelligent reconnection
      String? disconnectReason;
      if (!_hasNetworkConnectivity()) {
        disconnectReason = 'network connectivity lost';
      } else if (_wasNetworkDisconnected) {
        disconnectReason = 'network connectivity restored';
      }

      // Record disconnection event
      _recordConnectionEvent('disconnected', reason: disconnectReason);

      // 认证失败保护机制 - 按照内存规范
      if (!_isInAuthCooldown()) {
        _scheduleReconnect(reason: disconnectReason);
      } else {
        debugPrint('Skipping reconnect due to authentication cooldown.');
        _updateConnectionState(AppConnectionState.MqttConnectionState.error);
      }
    } else {
      _updateConnectionState(
        AppConnectionState.MqttConnectionState.disconnected,
      );
    }
  }

  void _scheduleReconnect({String? reason}) {
    _reconnectTimer?.cancel();

    // Check if we should skip reconnection due to network issues
    if (!_hasNetworkConnectivity()) {
      debugPrint(
        'Skipping reconnect due to no network connectivity. Will retry when network is restored.',
      );
      return;
    }

    if (_reconnectAttempts >= _maxReconnectAttempts) {
      debugPrint(
        'MQTT reconnection failed after $_maxReconnectAttempts attempts. Stopping.',
      );
      _updateConnectionState(AppConnectionState.MqttConnectionState.error);
      return;
    }

    // Calculate delay based on reason and attempt count
    int delay = _calculateReconnectDelay(reason);

    debugPrint(
      'Scheduling MQTT reconnection attempt #${_reconnectAttempts + 1} in $delay seconds... ${reason != null ? "Reason: $reason" : ""}',
    );

    _reconnectTimer = Timer(Duration(seconds: delay), () {
      connect();
    });
    _reconnectAttempts++;
  }

  int _calculateReconnectDelay(String? reason) {
    // Use shorter delays for network-related issues
    if (reason != null &&
        (reason.contains('network') || reason.contains('connectivity'))) {
      return min(_networkReconnectDelaySeconds, _maxReconnectDelaySeconds);
    }

    // Use exponential backoff for other issues
    return min(
      _minReconnectDelaySeconds * pow(2, _reconnectAttempts),
      _maxReconnectDelaySeconds,
    ).toInt();
  }

  void _resubscribeTopics() {
    if (_subscribedTopics.isEmpty) return;
    debugPrint('Resubscribing to ${_subscribedTopics.length} topics...');
    _subscribedTopics.forEach((topic, qos) {
      debugPrint(' - Subscribing to $topic');
      _client!.subscribe(topic, qos);
    });
  }

  void _sendOfflineMessages() {
    if (_offlineMessageQueue.isEmpty) return;
    debugPrint('Sending ${_offlineMessageQueue.length} offline messages...');
    final List<_QueuedMessage> messagesToSend = List.from(_offlineMessageQueue);
    _offlineMessageQueue.clear();
    for (final msg in messagesToSend) {
      publish(msg.topic, msg.payload, msg.qos);
    }
  }

  void _onPong() {
    final now = DateTime.now();
    _lastPongReceived = now;

    // Calculate ping latency if we have a recent ping
    if (_lastPingSent != null) {
      final latency = now.difference(_lastPingSent!);
      _recentPingLatencies.add(latency);

      // Keep only recent latencies
      if (_recentPingLatencies.length > _maxPingLatencyHistory) {
        _recentPingLatencies.removeAt(0);
      }

      debugPrint('MQTT pong received. Latency: ${latency.inMilliseconds}ms');
    } else {
      debugPrint('MQTT pong received.');
    }

    // Reset connection quality metrics
    _consecutivePingFailures = 0;
    _messagesSentSinceLastPong = 0;
  }

  void _updateConnectionState(AppConnectionState.MqttConnectionState state) {
    _currentState = state;
    if (!_connectionStateController.isClosed) {
      _connectionStateController.add(state);
    }
  }

  void _onMessage(List<MqttReceivedMessage<MqttMessage?>> messages) async {
    for (var message in messages) {
      final recMess = message.payload as MqttPublishMessage;
      final payload = MqttUtilities.bytesToStringAsString(
        recMess.payload.message!,
      );
      final topic = message.topic!;
      debugPrint('Received MQTT 5.0 message on topic $topic: $payload');

      try {
        final jsonData = jsonDecode(payload);
        _reportMqttCommandReceived(topic, payload, jsonData);

        if (jsonData['type'] == null) {
          throw Exception('Invalid message format: missing "type" field.');
        }

        if (_fileProvider == null) {
          debugPrint(
            '[MQTT] Deferring message because FileProvider not set yet.',
          );
          _pendingMessages.add({
            'topic': topic,
            'payload': payload,
            'json': jsonData,
          });
          continue;
        }

        _enqueueMqttCommand(topic, payload, jsonData);
      } catch (e) {
        debugPrint('Error processing MQTT message: $e');
        _reportMqttCommandProcessed(topic, payload, {}, 'failed', e.toString());

        try {
          final decoded = jsonDecode(payload);
          if ((decoded is Map<String, dynamic>) &&
              (decoded['type'] as int?) == 7) {
            final settings = await _settingsService.loadSettings();
            final ackTopic = '$topic/ack';
            final ackPayload = jsonEncode({
              'type': 7,
              'group_name': settings.groupName ?? '',
              'equipment_alias_name': settings.deviceAlias ?? '',
              'status': 'failed',
              'error': e.toString(),
              'powerOnTime': decoded['powerOnTime'] ?? decoded['power_on_time'],
              'powerOffTime':
                  decoded['powerOffTime'] ?? decoded['power_off_time'],
              'weekdays': decoded['weekdays'],
              'timestamp': DateTime.now().toIso8601String(),
            });
            publish(ackTopic, ackPayload, MqttQos.atLeastOnce);
          }
        } catch (_) {
          // ignore ack publish on decode error
        }
      }
    }
  }

  Future<void> _publishAckType7(
    String topic,
    Map<String, dynamic> jsonData,
    bool handled,
    String? errorMessage,
  ) async {
    try {
      final settings = await _settingsService.loadSettings();
      final ackTopic = '$topic/ack';
      final ackPayload = jsonEncode({
        'type': 7,
        'group_name': jsonData['group_name'] ?? (settings.groupName ?? ''),
        'equipment_alias_name':
            jsonData['equipment_alias_name'] ?? (settings.deviceAlias ?? ''),
        'status': handled ? 'success' : 'failed',
        'error': errorMessage,
        'powerOnTime': jsonData['powerOnTime'] ?? jsonData['power_on_time'],
        'powerOffTime': jsonData['powerOffTime'] ?? jsonData['power_off_time'],
        'weekdays': jsonData['weekdays'],
        'timestamp': DateTime.now().toIso8601String(),
      });
      publish(ackTopic, ackPayload, MqttQos.atLeastOnce);
    } catch (e) {
      debugPrint('[MQTT] Failed to publish ACK for type=7: $e');
    }
  }

  void _enqueueMqttCommand(
    String topic,
    String payload,
    Map<String, dynamic> jsonData,
  ) {
    try {
      final mqttMessage = MqttMessageModel.fromJson(jsonData);

      _commandQueue.enqueue(
        topic: topic,
        payload: payload,
        json: jsonData,
        model: mqttMessage,
        executor: () async {
          final handled = await _messageHandlerService.handleMessage(
            mqttMessage,
            (uiMessage) => _messageController.add(uiMessage),
            fileProvider: _fileProvider,
          );

          debugPrint(
            "[MQTT] Message processed result: handled=$handled type=${jsonData['type']} fileCount=${(jsonData['list'] as List? ?? []).length}",
          );

          return handled;
        },
        onComplete: (handled, error) {
          _reportMqttCommandProcessed(
            topic,
            payload,
            jsonData,
            handled ? 'success' : 'failed',
            error,
          );

          if ((jsonData['type'] as int?) == 7) {
            _publishAckType7(topic, jsonData, handled, error);
          }
        },
      );
    } catch (e) {
      debugPrint('[MQTT] Error enqueueing message: $e');
      _reportMqttCommandProcessed(
        topic,
        payload,
        jsonData,
        'failed',
        e.toString(),
      );
    }
  }

  void _reportMqttCommandReceived(
    String topic,
    String payload,
    Map<String, dynamic> jsonData,
  ) {
    try {
      _reportService.reportMqttCommandReceived(
        mqttTopic: topic,
        messageType: jsonData['type'] ?? 0,
        messageGroupName: jsonData['group_name'] ?? '',
        fileCount: (jsonData['list'] as List? ?? []).length,
        commandContent: payload,
        processingStatus: 'received',
      );
    } catch (e) {
      debugPrint('Error in _reportMqttCommandReceived: $e');
    }
  }

  void _reportMqttCommandProcessed(
    String topic,
    String payload,
    Map<String, dynamic> jsonData,
    String status,
    String? errorMessage,
  ) {
    try {
      _reportService.reportMqttCommandProcessed(
        mqttTopic: topic,
        messageType: jsonData['type'] ?? 0,
        messageGroupName: jsonData['group_name'] ?? '',
        fileCount: (jsonData['list'] as List? ?? []).length,
        commandContent: payload,
        processingStatus: status,
        errorMessage: errorMessage,
      );
    } catch (e) {
      debugPrint('Error in _reportMqttCommandProcessed: $e');
    }
  }

  // --- Public methods for testing ---

  @visibleForTesting
  bool isAuthenticationError(String errorMessage) {
    return _isAuthenticationError(errorMessage);
  }

  @visibleForTesting
  bool isInAuthCooldown() {
    return _isInAuthCooldown();
  }

  @visibleForTesting
  Duration getAuthCooldownRemaining() {
    return _getAuthCooldownRemaining();
  }
}
