import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';

/// 基于 pubspec.yaml 的版本号管理服务
/// 使用 Flutter 标准的版本号管理方式
class PubspecVersionService {
  static const String _lastUpdateTimeKey = 'last_update_time';

  /// 获取 pubspec.yaml 中定义的版本号
  /// 这是 Flutter 应用的标准版本号获取方式
  Future<String> getAppVersion() async {
    try {
      // 从 pubspec.yaml 读取版本号
      const platform = MethodChannel('flutter/platform');
      final version = await platform.invokeMethod('getAppVersion');
      return version ?? '1.0.0+1';
    } catch (e) {
      debugPrint('获取应用版本号失败: $e');
      // 如果无法获取，返回默认版本号
      return '1.0.0+1';
    }
  }

  /// 获取版本名称（不包含构建号）
  /// 例如：1.0.0+1 -> 1.0.0
  Future<String> getVersionName() async {
    final fullVersion = await getAppVersion();
    return fullVersion.split('+')[0];
  }

  /// 获取构建号
  /// 例如：1.0.0+1 -> 1
  Future<String> getBuildNumber() async {
    final fullVersion = await getAppVersion();
    final parts = fullVersion.split('+');
    return parts.length > 1 ? parts[1] : '1';
  }

  /// 获取完整的版本信息
  Future<Map<String, dynamic>> getVersionInfo() async {
    try {
      final fullVersion = await getAppVersion();
      final versionName = await getVersionName();
      final buildNumber = await getBuildNumber();
      final deviceInfo = await _getDeviceInfo();
      final prefs = await SharedPreferences.getInstance();
      final lastUpdateTime = prefs.getString(_lastUpdateTimeKey);

      return {
        'full_version': fullVersion,
        'version_name': versionName,
        'build_number': buildNumber,
        'platform': Platform.operatingSystem,
        'device_info': deviceInfo,
        'last_update_time': lastUpdateTime,
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('获取版本信息失败: $e');
      return {
        'full_version': '1.0.0+1',
        'version_name': '1.0.0',
        'build_number': '1',
        'error': e.toString(),
      };
    }
  }

  /// 记录版本更新时间
  Future<void> recordUpdateTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _lastUpdateTimeKey,
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      debugPrint('记录更新时间失败: $e');
    }
  }

  /// 获取设备信息
  Future<Map<String, String>> _getDeviceInfo() async {
    try {
      final deviceInfoPlugin = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        return {
          'device_model': androidInfo.model,
          'device_brand': androidInfo.brand,
          'android_version': androidInfo.version.release,
          'sdk_int': androidInfo.version.sdkInt.toString(),
        };
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        return {
          'device_model': iosInfo.model,
          'device_name': iosInfo.name,
          'system_version': iosInfo.systemVersion,
        };
      } else {
        return {
          'platform': Platform.operatingSystem,
          'version': Platform.operatingSystemVersion,
        };
      }
    } catch (e) {
      debugPrint('获取设备信息失败: $e');
      return {'platform': Platform.operatingSystem, 'error': e.toString()};
    }
  }

  /// 获取用于API提交的版本信息
  Future<Map<String, String>> getApiVersionInfo() async {
    final versionInfo = await getVersionInfo();
    return {
      'app_version': versionInfo['full_version'] ?? '1.0.0+1',
      'version_name': versionInfo['version_name'] ?? '1.0.0',
      'build_number': versionInfo['build_number'] ?? '1',
      'platform': versionInfo['platform'] ?? 'unknown',
      'generated_at': DateTime.now().toIso8601String(),
    };
  }

  /// 格式化版本号显示
  Future<String> getFormattedVersion() async {
    final versionName = await getVersionName();
    final buildNumber = await getBuildNumber();
    return "版本 $versionName (构建号: $buildNumber)";
  }
}
