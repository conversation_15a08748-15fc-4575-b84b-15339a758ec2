import 'dart:io';
import 'package:flutter/services.dart';
import 'package:mac_address_plus/mac_address_plus.dart';

class MacAddressService {
  final _macAddressPlusPlugin = MacAddressPlus();
  static const MethodChannel _channel = MethodChannel('mac_address');

  // Get device ID based on platform
  Future<String> getMacAddress() async {
    try {
      if (Platform.isAndroid) {
        return await _getAndroidMacAddress();
      } else {
        return 'unknown_device';
      }
    } catch (e) {
      print('Error getting Mac Address: $e');
      return 'error_mac_address';
    }
  }

  // Get Android device ID
  Future<String> _getAndroidMacAddress() async {
    String macAddress =
        await _macAddressPlusPlugin.getMacAddress() ?? 'unknown_mac_address';

    // String macAddress =
    //     await _channel.invokeMethod('getMacAddress') ?? 'unknown_mac_address';

    // 如果Flutter插件获取失败，尝试使用ysapi作为备用方法
    if (macAddress == 'unknown_mac_address' || macAddress.isEmpty) {
      try {
        print('Flutter插件获取MAC地址失败，尝试使用ysapi备用方法...');
        macAddress = await _channel.invokeMethod('getMacAddress');
        print('ysapi获取MAC地址结果: $macAddress');
      } on PlatformException catch (e) {
        print("Failed to get mac address from ysapi: '${e.message}'.");
        macAddress = 'error_mac_address';
      }
    }

    return macAddress;
  }
}
