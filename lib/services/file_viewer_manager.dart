import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'logger_service.dart';
import '../video/engine.dart';

/// 文件查看器管理器，用于管理所有类型的文件查看器实例
/// 确保任何时候只打开一个文件查看器，避免性能问题和资源泄露
class FileViewerManager {
  static final FileViewerManager _instance = FileViewerManager._internal();

  factory FileViewerManager() {
    return _instance;
  }

  FileViewerManager._internal();

  // 当前活动的视频引擎
  VideoEngine? _currentVideoEngine;
  // 用于序列化视频控制器操作的Future，防止竞态条件
  Future<void> _videoOperation = Future.value();

  // 当前活动的WebView控制器
  InAppWebViewController? _currentWebViewController;

  /// 获取当前视频引擎
  VideoEngine? get currentVideoEngine => _currentVideoEngine;

  /// 设置当前视频引擎，并确保旧引擎被安全地释放。
  set currentVideoEngine(VideoEngine? engine) {
    _videoOperation = _videoOperation.then((_) async {
      final old = _currentVideoEngine;
      if (old == engine) return;
      _currentVideoEngine = engine;
      if (old != null) {
        try {
          await old.stop();
          await old.dispose();
          logger.i('Previous VideoEngine disposed successfully.');
        } catch (e, s) {
          logger.e(
            'Error disposing previous video engine',
            error: e,
            stackTrace: s,
          );
        }
      }
    });
  }

  /// 获取当前WebView控制器
  InAppWebViewController? get currentWebViewController =>
      _currentWebViewController;

  /// 设置当前WebView控制器
  set currentWebViewController(InAppWebViewController? controller) {
    // 释放之前的WebView控制器
    if (_currentWebViewController != null &&
        _currentWebViewController != controller) {
      final oldController = _currentWebViewController;
      Future.microtask(() async {
        try {
          oldController!.dispose();
          logger.i('Previous WebViewController disposed successfully.');
        } catch (e, s) {
          logger.e(
            'Error disposing previous WebView controller',
            error: e,
            stackTrace: s,
          );
        }
      });
    }
    _currentWebViewController = controller;
  }

  /// 关闭当前视频播放器。
  void closeCurrentVideoPlayer() {
    currentVideoEngine = null;
  }

  /// 关闭当前WebView
  void closeCurrentWebView() {
    if (_currentWebViewController != null) {
      final oldController = _currentWebViewController;
      _currentWebViewController = null; // 立即清除引用
      Future.microtask(() async {
        try {
          oldController!.dispose();
          logger.i('Current WebViewController closed successfully.');
        } catch (e, s) {
          logger.e('Error closing WebView controller', error: e, stackTrace: s);
        }
      });
    }
  }

  /// 关闭所有活动的文件查看器
  void closeAllViewers() {
    closeCurrentVideoPlayer();
    closeCurrentWebView();
  }
}
