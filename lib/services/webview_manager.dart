import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class WebViewManager {
  static final WebViewManager _instance = WebViewManager._internal();
  InAppWebViewController? _currentWebViewController;

  factory WebViewManager() {
    return _instance;
  }

  WebViewManager._internal();

  // Get current WebView controller
  InAppWebViewController? get currentWebViewController =>
      _currentWebViewController;

  // Set current WebView controller
  set currentWebViewController(InAppWebViewController? controller) {
    // Close previous WebView if exists
    if (_currentWebViewController != null) {
      _currentWebViewController!.dispose();
    }
    _currentWebViewController = controller;
  }

  // Close current WebView
  void closeCurrentWebView() {
    if (_currentWebViewController != null) {
      _currentWebViewController!.dispose();
      _currentWebViewController = null;
    }
  }
}
