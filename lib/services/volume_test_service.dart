import 'package:flutter/foundation.dart';
import 'device_control_service.dart';

/// 音量测试服务，用于验证音量设置功能
class VolumeTestService {
  final DeviceControlService _deviceControlService = DeviceControlService();

  /// 测试音量设置功能
  Future<bool> testVolumeControl() async {
    debugPrint("=== 开始音量控制测试 ===");

    try {
      // 测试不同音量级别
      final testVolumes = [0, 25, 50, 75, 100];

      for (int volume in testVolumes) {
        debugPrint("测试设置音量到: $volume%");

        try {
          await _deviceControlService.setVolume(volume);
          debugPrint("音量 $volume% 设置成功");

          // 等待一秒让用户听到音量变化
          await Future.delayed(const Duration(seconds: 1));
        } catch (e) {
          debugPrint("音量 $volume% 设置失败: $e");
          return false;
        }
      }

      debugPrint("=== 音量控制测试完成 ===");
      return true;
    } catch (e) {
      debugPrint("音量控制测试失败: $e");
      return false;
    }
  }

  /// 测试静音功能
  Future<bool> testMuteFunction() async {
    debugPrint("=== 开始静音功能测试 ===");

    try {
      // 先设置到50%音量
      await _deviceControlService.setVolume(50);
      await Future.delayed(const Duration(seconds: 1));

      // 然后静音
      await _deviceControlService.setVolume(0);
      debugPrint("静音测试完成");

      return true;
    } catch (e) {
      debugPrint("静音功能测试失败: $e");
      return false;
    }
  }
}
