import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

/// WebView预加载服务
/// 用于预热WebView引擎，提高首次加载性能
class WebViewPreloader {
  static final WebViewPreloader _instance = WebViewPreloader._internal();
  static bool _isPreloaded = false;

  factory WebViewPreloader() {
    return _instance;
  }

  WebViewPreloader._internal();

  /// 预加载WebView引擎
  static Future<void> preloadWebView() async {
    if (_isPreloaded) return;

    try {
      // 在Android上预热WebView引擎
      if (Platform.isAndroid) {
        await InAppWebViewController.setWebContentsDebuggingEnabled(kDebugMode);

        // 预加载WebView设置
        final settings = InAppWebViewSettings(
          javaScriptEnabled: true,
          domStorageEnabled: true,
          databaseEnabled: true,
          cacheEnabled: false,
          clearCache: true,
        );

        debugPrint('WebView engine preloaded successfully');
      }

      _isPreloaded = true;
    } catch (e) {
      debugPrint('Failed to preload WebView engine: $e');
    }
  }

  /// 检查WebView是否已预加载
  static bool get isPreloaded => _isPreloaded;

  /// 重置预加载状态
  static void reset() {
    _isPreloaded = false;
  }
}
