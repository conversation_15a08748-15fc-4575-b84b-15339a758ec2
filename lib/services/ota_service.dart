import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'error_handler_service.dart';

class OtaService {
  final Dio _dio = Dio();
  final ErrorHandlerService _errorHandler = ErrorHandlerService();
  static const MethodChannel _channel = MethodChannel(
    'com.mingsign.esop_client/silent_install',
  );

  Future<void> startUpdate(String otaUrl) async {
    debugPrint('Starting OTA update from: $otaUrl');

    try {
      // 1. Request necessary permissions
      if (!await _requestPermissions()) {
        return;
      }

      // 2. Get download path
      final downloadPath = await _getDownloadPath();
      if (downloadPath == null) {
        return;
      }
      final apkPath = '$downloadPath/app-release.apk';

      // 3. Download the APK
      await _downloadApk(otaUrl, apkPath);

      // 4. 尝试静默安装，如果失败则使用传统方式
      final silentInstallSuccess = await _attemptSilentInstall(apkPath);
      if (!silentInstallSuccess) {
        debugPrint('静默安装失败，使用传统安装方式');
        await _openApk(apkPath);
      }
    } catch (e, s) {
      debugPrint('OTA update failed: $e');
      _errorHandler.reportError(
        module: 'OtaService',
        message: 'OTA update failed: $e',
        stackTrace: s.toString(),
        additionalInfo: {'otaUrl': otaUrl},
      );
    }
  }

  Future<bool> _requestPermissions() async {
    debugPrint('Requesting permissions...');
    // Request storage permission for older Android versions
    if (Platform.isAndroid) {
      var status = await Permission.storage.request();
      if (status.isDenied) {
        _handlePermissionDenied('Storage');
        return false;
      }

      // Request "install unknown apps" permission
      status = await Permission.requestInstallPackages.request();
      if (status.isDenied) {
        _handlePermissionDenied('Install Packages');
        return false;
      }
    }
    debugPrint('Permissions granted.');
    return true;
  }

  Future<String?> _getDownloadPath() async {
    try {
      final directory = await getExternalStorageDirectory();
      return directory?.path;
    } catch (e) {
      debugPrint('Error getting download path: $e');
      _errorHandler.reportError(
        module: 'OtaService',
        message: 'Failed to get download path: $e',
      );
      return null;
    }
  }

  Future<void> _downloadApk(String url, String savePath) async {
    debugPrint('Downloading APK from $url to $savePath');
    await _dio.download(
      url,
      savePath,
      onReceiveProgress: (received, total) {
        if (total != -1) {
          final progress = (received / total * 100).toStringAsFixed(0);
          debugPrint('Download progress: $progress%');
        }
      },
    );
    debugPrint('APK downloaded successfully.');
  }

  Future<void> _openApk(String path) async {
    debugPrint('Opening APK at: $path');
    final result = await OpenFile.open(path);
    if (result.type != ResultType.done) {
      throw Exception('Failed to open APK: ${result.message}');
    }
    debugPrint('APK open request sent.');
  }

  /// 尝试静默安装APK
  Future<bool> _attemptSilentInstall(String apkPath) async {
    try {
      debugPrint('尝试静默安装: $apkPath');

      // 检查是否有静默安装权限
      final hasPermission =
          await _channel.invokeMethod<bool>('hasInstallPermission') ?? false;
      if (!hasPermission) {
        debugPrint('没有静默安装权限');
        return false;
      }

      // 执行静默安装
      final result =
          await _channel.invokeMethod<bool>('silentInstall', {
            'apkPath': apkPath,
          }) ??
          false;

      if (result) {
        debugPrint('静默安装成功');
        return true;
      } else {
        debugPrint('静默安装失败');
        return false;
      }
    } catch (e) {
      debugPrint('静默安装异常: $e');
      _errorHandler.reportError(
        module: 'OtaService',
        message: '静默安装失败: $e',
        errorCode: 'SILENT_INSTALL_ERROR',
      );
      return false;
    }
  }

  void _handlePermissionDenied(String permissionName) {
    final message =
        '$permissionName permission denied. Cannot proceed with OTA update.';
    debugPrint(message);
    _errorHandler.reportError(
      module: 'OtaService',
      message: message,
      errorCode: 'PERMISSION_DENIED',
    );
  }
}
