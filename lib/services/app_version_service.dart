import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'dart:io';

/// Flutter 官方推荐的版本号管理服务
/// 基于 pubspec.yaml 和 package_info_plus 插件
class AppVersionService {
  static const String _lastUpdateTimeKey = 'last_update_time';

  /// 获取应用版本信息
  Future<PackageInfo> getPackageInfo() async {
    return await PackageInfo.fromPlatform();
  }

  /// 获取完整版本号（版本名+构建号）
  /// 例如：1.0.0+1
  Future<String> getFullVersion() async {
    final packageInfo = await getPackageInfo();
    return '${packageInfo.version}+${packageInfo.buildNumber}';
  }

  /// 获取版本名称（不包含构建号）
  /// 例如：1.0.0
  Future<String> getVersionName() async {
    final packageInfo = await getPackageInfo();
    return packageInfo.version;
  }

  /// 获取构建号
  /// 例如：1
  Future<String> getBuildNumber() async {
    final packageInfo = await getPackageInfo();
    return packageInfo.buildNumber;
  }

  /// 获取应用名称
  Future<String> getAppName() async {
    final packageInfo = await getPackageInfo();
    return packageInfo.appName;
  }

  /// 获取包名
  Future<String> getPackageName() async {
    final packageInfo = await getPackageInfo();
    return packageInfo.packageName;
  }

  /// 获取完整的版本信息
  Future<Map<String, dynamic>> getVersionInfo() async {
    try {
      final packageInfo = await getPackageInfo();
      final deviceInfo = await _getDeviceInfo();
      final prefs = await SharedPreferences.getInstance();
      final lastUpdateTime = prefs.getString(_lastUpdateTimeKey);

      return {
        'app_name': packageInfo.appName,
        'package_name': packageInfo.packageName,
        'version_name': packageInfo.version,
        'build_number': packageInfo.buildNumber,
        'full_version': '${packageInfo.version}+${packageInfo.buildNumber}',
        'platform': Platform.operatingSystem,
        'device_info': deviceInfo,
        'last_update_time': lastUpdateTime,
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('获取版本信息失败: $e');
      return {
        'app_name': 'Unknown',
        'package_name': 'Unknown',
        'version_name': '1.0.0',
        'build_number': '1',
        'full_version': '1.0.0+1',
        'error': e.toString(),
      };
    }
  }

  /// 记录版本更新时间
  Future<void> recordUpdateTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _lastUpdateTimeKey,
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      debugPrint('记录更新时间失败: $e');
    }
  }

  /// 获取设备信息
  Future<Map<String, String>> _getDeviceInfo() async {
    try {
      final deviceInfoPlugin = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        return {
          'device_model': androidInfo.model,
          'device_brand': androidInfo.brand,
          'android_version': androidInfo.version.release,
          'sdk_int': androidInfo.version.sdkInt.toString(),
        };
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        return {
          'device_model': iosInfo.model,
          'device_name': iosInfo.name,
          'system_version': iosInfo.systemVersion,
        };
      } else {
        return {
          'platform': Platform.operatingSystem,
          'version': Platform.operatingSystemVersion,
        };
      }
    } catch (e) {
      debugPrint('获取设备信息失败: $e');
      return {'platform': Platform.operatingSystem, 'error': e.toString()};
    }
  }

  /// 获取用于API提交的版本信息
  Future<Map<String, String>> getApiVersionInfo() async {
    final versionInfo = await getVersionInfo();
    return {
      'app_name': versionInfo['app_name'] ?? 'Unknown',
      'package_name': versionInfo['package_name'] ?? 'Unknown',
      'app_version': versionInfo['full_version'] ?? '1.0.0+1',
      'version_name': versionInfo['version_name'] ?? '1.0.0',
      'build_number': versionInfo['build_number'] ?? '1',
      'platform': versionInfo['platform'] ?? 'unknown',
      'generated_at': DateTime.now().toIso8601String(),
    };
  }

  /// 格式化版本号显示
  Future<String> getFormattedVersion() async {
    final versionName = await getVersionName();
    final buildNumber = await getBuildNumber();
    return "版本 $versionName (构建号: $buildNumber)";
  }

  /// 检查版本号格式是否有效
  bool isValidVersionFormat(String version) {
    // 检查版本号格式：major.minor.patch+buildNumber
    final regex = RegExp(r'^\d+\.\d+\.\d+\+\d+$');
    return regex.hasMatch(version);
  }

  /// 比较两个版本号
  /// 返回值：-1 表示 version1 < version2，0 表示相等，1 表示 version1 > version2
  int compareVersions(String version1, String version2) {
    try {
      final v1Parts = version1.split('+')[0].split('.');
      final v2Parts = version2.split('+')[0].split('.');

      for (int i = 0; i < 3; i++) {
        final v1Num = int.parse(v1Parts[i]);
        final v2Num = int.parse(v2Parts[i]);

        if (v1Num < v2Num) return -1;
        if (v1Num > v2Num) return 1;
      }

      // 如果版本号相同，比较构建号
      final b1 = int.parse(version1.split('+')[1]);
      final b2 = int.parse(version2.split('+')[1]);

      if (b1 < b2) return -1;
      if (b1 > b2) return 1;

      return 0;
    } catch (e) {
      debugPrint('版本号比较失败: $e');
      return 0;
    }
  }
}
