import 'dart:io';
import 'package:flutter/foundation.dart';
import '../models/mqtt_message_model.dart';
import '../models/settings_model.dart';
import 'settings_service.dart';
import 'device_control_service.dart';
import 'ota_service.dart';
import 'equipment_api_service.dart';
import 'report_service.dart';
import 'file_open_diagnostics.dart';
import '../providers/file_provider.dart';

/// A service dedicated to processing business logic for incoming MQTT messages.
class MessageHandlerService {
  final SettingsService _settingsService = SettingsService();
  final DeviceControlService _deviceControlService = DeviceControlService();
  final OtaService _otaService = OtaService();
  final EquipmentApiService _equipmentApiService = EquipmentApiService();
  final ReportService _reportService = ReportService();

  /// Handles the incoming MQTT message based on its type.
  ///
  /// Return true if this message triggered a real action (e.g. file download/process,
  /// device control, OTA, volume set). Return false if ignored or filtered.
  Future<bool> handleMessage(
    MqttMessageModel message,
    Function(MqttMessageModel) onUIMessage, {
    FileProvider? fileProvider,
  }) async {
    try {
      final settings = await _settingsService.loadSettings();

      // Filter messages not intended for this group or device (robust matching)
      final msgGroup = (message.groupName ?? '').trim();
      final setGroup = (settings.groupName ?? '').trim();

      bool groupMatch = false;
      if (msgGroup == '*' || setGroup == '*') {
        groupMatch = true;
      } else {
        final msgGroups = msgGroup
            .split(RegExp(r'[,;，；\s]+'))
            .where((s) => s.isNotEmpty)
            .toSet();
        final setGroups = setGroup
            .split(RegExp(r'[,;，；\s]+'))
            .where((s) => s.isNotEmpty)
            .toSet();
        groupMatch =
            msgGroups.intersection(setGroups).isNotEmpty ||
            msgGroup.contains(setGroup) ||
            setGroup.contains(msgGroup);
      }

      if (!groupMatch) {
        debugPrint(
          '[MHS] Message ignored: wrong group (service filter). msg="$msgGroup" settings="$setGroup"',
        );
        return false;
      }

      switch (message.type) {
        case 1: // Partial send - 部分发送
        case 2: // All send - 全部发送
        case 3: // Rule-based send - 规则发送
          {
            final processed = await _handleFileMessage(
              message,
              settings,
              onUIMessage,
              fileProvider,
            );
            return processed;
          }

        case 4: // Device control commands
          {
            if (message.equipmentAliasName != settings.deviceAlias) {
              debugPrint(
                '[MHS] Device control ignored: alias mismatch msg="${message.equipmentAliasName}" settings="${settings.deviceAlias}"',
              );
              return false;
            }
            await _handleDeviceControlCommand(message, settings);
            return true;
          }

        case 5: // OTA update command
          {
            if (message.equipmentAliasName != settings.deviceAlias) {
              debugPrint(
                '[MHS] OTA ignored: alias mismatch msg="${message.equipmentAliasName}" settings="${settings.deviceAlias}"',
              );
              return false;
            }
            if (message.otaUrl != null) {
              final otaUrl =
                  'http://${settings.mqttServerAddress}:${settings.serverPort}/assets/ota_update/${message.otaUrl}';
              _otaService.startUpdate(otaUrl);
              return true;
            }
            return false;
          }

        case 6: // Set volume command
          {
            if (message.equipmentAliasName == settings.deviceAlias ||
                message.equipmentAliasName == "*") {
              if (message.mute == true) {
                await _deviceControlService.setVolume(0);
                return true;
              } else if (message.volume != null) {
                await _deviceControlService.setVolume(message.volume!);
                return true;
              }
            }
            return false;
          }

        case 7: // Weekly power on/off schedule
          {
            // 仅 Android 执行；目标匹配别名或 *（分组内所有）
            if ((message.equipmentAliasName == settings.deviceAlias ||
                    message.equipmentAliasName == "*") &&
                Platform.isAndroid) {
              try {
                // README 中 powerOnTime/powerOffTime 示例可能为字符串 "{8,30}" 或对象/数组
                // 这里做稳健解析：支持字符串 "{H,M}"、列表 [H,M]、对象 {"h":8,"m":30}
                List<int>? parseTime(dynamic v) {
                  if (v == null) return null;
                  if (v is String) {
                    final s = v.replaceAll(RegExp(r'[\{\}\s]'), '');
                    final parts = s.split(',');
                    if (parts.length == 2) {
                      final h = int.tryParse(parts[0]);
                      final m = int.tryParse(parts[1]);
                      if (h != null && m != null) return [h, m];
                    }
                  } else if (v is List) {
                    if (v.length >= 2 && v[0] is num && v[1] is num) {
                      return [(v[0] as num).toInt(), (v[1] as num).toInt()];
                    }
                  } else if (v is Map) {
                    final h = v['h'] ?? v['hour'] ?? v['H'];
                    final m = v['m'] ?? v['minute'] ?? v['M'];
                    if (h is num && m is num) {
                      return [h.toInt(), m.toInt()];
                    }
                  }
                  return null;
                }

                List<int>? parseWeekdays(dynamic v) {
                  // 期望 7 个 0/1，周一至周日
                  if (v == null) return null;
                  if (v is String) {
                    final s = v.replaceAll(RegExp(r'[\{\}\s]'), '');
                    final parts = s
                        .split(',')
                        .where((e) => e.isNotEmpty)
                        .toList();
                    if (parts.length == 7) {
                      final arr = <int>[];
                      for (final p in parts) {
                        final n = int.tryParse(p);
                        if (n == null) return null;
                        arr.add(n);
                      }
                      return arr;
                    }
                  } else if (v is List) {
                    if (v.length == 7 && v.every((e) => e is num)) {
                      return v.map((e) => (e as num).toInt()).toList();
                    }
                  }
                  return null;
                }

                final on = parseTime(message.powerOnTimeRaw);
                final off = parseTime(message.powerOffTimeRaw);
                final weekdays = parseWeekdays(message.weekdaysRaw);

                if (on == null || off == null || weekdays == null) {
                  debugPrint(
                    '[MHS] type=7 invalid payload: on=$on off=$off weekdays=$weekdays',
                  );
                  return false;
                }

                await _deviceControlService.setPowerOnOffWithWeekly(
                  powerOnHour: on[0],
                  powerOnMinute: on[1],
                  powerOffHour: off[0],
                  powerOffMinute: off[1],
                  weekdays: weekdays,
                );
                return true;
              } catch (e) {
                debugPrint('[MHS] type=7 failed: $e');
                return false;
              }
            }
            return false;
          }

        case 8: // Clear power on/off schedule
          {
            // 仅 Android 执行；目标匹配别名或 *（分组内所有）
            if ((message.equipmentAliasName == settings.deviceAlias ||
                    message.equipmentAliasName == "*") &&
                Platform.isAndroid) {
              try {
                debugPrint('[MHS] type=8 clearing power on/off schedule');
                await _deviceControlService.clearPowerOnOffTime();
                debugPrint(
                  '[MHS] type=8 clear power on/off schedule succeeded',
                );
                return true;
              } catch (e) {
                debugPrint('[MHS] type=8 failed: $e');
                return false;
              }
            }
            return false;
          }

        default:
          debugPrint('[MHS] Invalid message type: ${message.type}');
          return false;
      }
    } catch (e) {
      debugPrint('Error processing MQTT message in MessageHandlerService: $e');
      return false;
    }
  }

  /// Handles file messages (types 1, 2, 3) with file download and processing logic.
  /// Return true if at least one file was successfully processed (downloaded/handled).
  Future<bool> _handleFileMessage(
    MqttMessageModel message,
    SettingsModel settings,
    Function(MqttMessageModel) onUIMessage,
    FileProvider? fileProvider,
  ) async {
    debugPrint('[MHS] Processing file message type ${message.type}');

    // Check if message has file list
    if (message.fileList == null || message.fileList!.isEmpty) {
      debugPrint('[MHS] No files to process in MQTT message');
      return false;
    }

    final deviceAlias = settings.deviceAlias ?? '';
    final groupName = settings.groupName ?? '';

    bool anySuccess = false;

    switch (message.type) {
      case 1: // Partial send
        anySuccess = await _processPartialMessage(
          message,
          deviceAlias,
          groupName,
          settings,
          fileProvider,
        );
        break;
      case 2: // All send
        anySuccess = await _processAllMessage(message, settings, fileProvider);
        break;
      case 3: // Rule-based send
        anySuccess = await _processRuleBasedMessage(
          message,
          deviceAlias,
          groupName,
          settings,
          fileProvider,
        );
        break;
    }

    // After processing files, call the UI callback
    onUIMessage(message);
    return anySuccess;
  }

  /// Process partial message (type 1).
  /// Return true if at least one file processed successfully.
  Future<bool> _processPartialMessage(
    MqttMessageModel message,
    String deviceAlias,
    String groupName,
    SettingsModel settings,
    FileProvider? fileProvider,
  ) async {
    final msgGroupName = message.groupName ?? '';

    // Check group name match
    if (msgGroupName != '*' && !msgGroupName.contains(groupName)) {
      debugPrint(
        '[MHS] Partial message ignored: group mismatch msg="$msgGroupName" settings="$groupName"',
      );
      return false;
    }

    bool anySuccess = false;

    // Process each file in the list
    for (final fileItem in message.fileList!) {
      if (fileItem.downloadFile == null || fileItem.downloadFile!.isEmpty) {
        debugPrint('[MHS] Skipping file item with empty download URL');
        continue;
      }

      if (fileItem.equipmentAliasName == '*' ||
          fileItem.equipmentAliasName == deviceAlias) {
        final ok = await _downloadAndProcessFile(
          fileItem.downloadFile!,
          settings,
          fileProvider,
          hash: fileItem.hash,
        );
        anySuccess = anySuccess || ok;
      }
    }

    return anySuccess;
  }

  /// Process all message (type 2).
  /// Return true if at least one file processed successfully.
  Future<bool> _processAllMessage(
    MqttMessageModel message,
    SettingsModel settings,
    FileProvider? fileProvider,
  ) async {
    bool anySuccess = false;

    for (final fileItem in message.fileList!) {
      if (fileItem.downloadFile == null || fileItem.downloadFile!.isEmpty) {
        debugPrint('[MHS] Skipping file item with empty download URL');
        continue;
      }

      final ok = await _downloadAndProcessFile(
        fileItem.downloadFile!,
        settings,
        fileProvider,
        hash: fileItem.hash,
      );
      anySuccess = anySuccess || ok;
    }

    return anySuccess;
  }

  /// Process rule-based message (type 3).
  /// Return true if at least one file processed successfully.
  Future<bool> _processRuleBasedMessage(
    MqttMessageModel message,
    String deviceAlias,
    String groupName,
    SettingsModel settings,
    FileProvider? fileProvider,
  ) async {
    final msgGroupName = message.groupName ?? '';

    // Check group name match
    if (msgGroupName != '*' && !msgGroupName.contains(groupName)) {
      debugPrint(
        '[MHS] Rule-based message ignored: group mismatch msg="$msgGroupName" settings="$groupName"',
      );
      return false;
    }

    bool anySuccess = false;

    for (final fileItem in message.fileList!) {
      if (fileItem.downloadFile == null || fileItem.downloadFile!.isEmpty) {
        debugPrint('[MHS] Skipping file item with empty download URL');
        continue;
      }

      if (fileItem.equipmentAliasName == '*' ||
          fileItem.equipmentAliasName == deviceAlias) {
        final ok = await _downloadAndProcessFile(
          fileItem.downloadFile!,
          settings,
          fileProvider,
          hash: fileItem.hash,
        );
        anySuccess = anySuccess || ok;
      }
    }

    return anySuccess;
  }

  /// Download and process file. Return true if successfully processed.
  Future<bool> _downloadAndProcessFile(
    String fileUrl,
    SettingsModel settings,
    FileProvider? fileProvider, {
    String? hash,
  }) async {
    if (fileUrl.isEmpty) {
      debugPrint('[MHS] Empty file URL');
      return false;
    }

    if (fileProvider == null) {
      debugPrint('[MHS] FileProvider is null, cannot process file');
      return false;
    }

    debugPrint('[MHS] Processing file URL: $fileUrl');

    // Get server address from settings
    final serverAddress = settings.mqttServerAddress ?? '';
    final serverPort = settings.serverPort ?? '';

    // Check if URL starts with http or https
    String fullUrl = fileUrl;
    if (!fileUrl.toLowerCase().startsWith('http')) {
      // URL doesn't start with http, add server address as prefix
      if (serverAddress.isEmpty) {
        debugPrint(
          '[MHS] Server address is empty, cannot prepend to URL: $fileUrl',
        );
        return false;
      }

      // Ensure server address has http:// prefix
      String prefix = serverAddress.toLowerCase().startsWith('http')
          ? serverAddress
          : 'http://$serverAddress:$serverPort';

      // Ensure path starts with / if needed
      if (!fileUrl.startsWith('/') && !prefix.endsWith('/')) {
        fullUrl = '$prefix/$fileUrl';
      } else {
        fullUrl = '$prefix$fileUrl';
      }

      debugPrint('[MHS] Modified URL: $fullUrl (original: $fileUrl)');
    } else {
      debugPrint('[MHS] Using original URL: $fileUrl');
    }

    try {
      // Process file from URL using FileProvider
      final success = await fileProvider.processFileFromUrl(
        fullUrl,
        hash: hash,
      );

      if (success) {
        debugPrint('[MHS] File processed successfully: $fullUrl');

        // 诊断文件处理结果
        if (hash != null) {
          debugPrint('[MHS] 开始诊断文件处理结果...');
          final diagnosticResults =
              await FileOpenDiagnostics.diagnoseFileProcessing(
                downloadUrl: fullUrl,
                hash: hash,
              );
          FileOpenDiagnostics.printDiagnosticSummary(diagnosticResults);

          // 诊断FileProvider状态
          final providerDiagnostics = FileOpenDiagnostics.diagnoseFileProvider(
            fileProvider,
          );
          debugPrint('[MHS] FileProvider诊断完成');
        }

        // The UI will be updated through the FileProvider's notifyListeners
        return true;
      } else {
        debugPrint('[MHS] Failed to process file: $fullUrl');
        return false;
      }
    } catch (e) {
      debugPrint('[MHS] Error processing file $fullUrl: $e');
      return false;
    }
  }

  /// Handles specific device control commands like reboot, shutdown, screenshot.
  Future<void> _handleDeviceControlCommand(
    MqttMessageModel message,
    SettingsModel settings,
  ) async {
    switch (message.command) {
      case 'reboot':
        _deviceControlService.reboot();
        break;
      case 'shutdown':
        _deviceControlService.shutdown();
        break;
      case 'screenshot':
        final path = await _deviceControlService.takeScreenshot();
        if (path != null) {
          final success = await _equipmentApiService.uploadScreenshot(
            screenshotFile: File(path),
            groupName: settings.groupName!,
            aliasName: settings.deviceAlias!,
            taskId: message.taskId,
          );
          _reportService.reportScreenshotTaken(
            status: success ? 'success' : 'failed',
            message: success
                ? 'Screenshot uploaded'
                : 'Failed to upload screenshot',
            imageUrl: success ? path : null,
          );
        } else {
          _reportService.reportScreenshotTaken(
            status: 'failed',
            message: 'Failed to take screenshot',
          );
        }
        break;
    }
  }
}
