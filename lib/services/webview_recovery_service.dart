import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import '../utils/webview_compatibility.dart';

/// WebView错误恢复服务
/// 专门处理WebView在不同设备上的兼容性问题和错误恢复
class WebViewRecoveryService {
  static const int _maxRecoveryAttempts = 5;
  static const Duration _recoveryDelay = Duration(milliseconds: 1000);

  /// 执行全面的WebView恢复
  static Future<bool> performFullRecovery(
    InAppWebViewController controller,
    File htmlFile, {
    int attemptCount = 0,
  }) async {
    if (attemptCount >= _maxRecoveryAttempts) {
      debugPrint('WebViewRecovery: Max recovery attempts reached');
      return false;
    }

    debugPrint(
      'WebViewRecovery: Starting recovery attempt ${attemptCount + 1}',
    );

    try {
      // 步骤1: 清理WebView状态
      await _cleanWebViewState(controller);

      // 步骤2: 重新注入兼容性脚本
      await _reinjectCompatibilityScripts(controller);

      // 步骤3: 修复渲染问题
      await _fixRenderingIssues(controller);

      // 步骤4: 重新加载文件
      await _reloadFileWithFallback(controller, htmlFile);

      // 步骤5: 验证恢复结果
      final isRecovered = await _validateRecovery(controller);

      if (isRecovered) {
        debugPrint('WebViewRecovery: Recovery successful');
        return true;
      } else {
        // 如果恢复失败，等待后重试
        await Future.delayed(_recoveryDelay);
        return await performFullRecovery(
          controller,
          htmlFile,
          attemptCount: attemptCount + 1,
        );
      }
    } catch (e) {
      debugPrint('WebViewRecovery: Recovery attempt failed: $e');

      // 等待后重试
      await Future.delayed(_recoveryDelay);
      return await performFullRecovery(
        controller,
        htmlFile,
        attemptCount: attemptCount + 1,
      );
    }
  }

  /// 清理WebView状态
  static Future<void> _cleanWebViewState(
    InAppWebViewController controller,
  ) async {
    try {
      // 清理缓存和存储
      await controller.clearCache();

      // 停止所有加载
      await controller.stopLoading();

      // 清理JavaScript上下文
      await controller.evaluateJavascript(
        source: '''
        // 清理可能的错误状态
        if (window.location) {
          window.location.hash = '';
        }

        // 清理事件监听器
        window.removeEventListener('error', arguments.callee);
        window.removeEventListener('unhandledrejection', arguments.callee);

        console.log('WebView state cleaned');
      ''',
      );

      debugPrint('WebViewRecovery: WebView state cleaned');
    } catch (e) {
      debugPrint('WebViewRecovery: Failed to clean WebView state: $e');
    }
  }

  /// 重新注入兼容性脚本
  static Future<void> _reinjectCompatibilityScripts(
    InAppWebViewController controller,
  ) async {
    try {
      const enhancedScript = '''
        // 增强的WebView兼容性脚本
        (function() {
          // 防止页面崩溃的全局错误处理
          window.addEventListener('error', function(e) {
            console.log('Global error caught:', e.message, e.filename, e.lineno);
            e.preventDefault();
            return true;
          });

          window.addEventListener('unhandledrejection', function(e) {
            console.log('Unhandled promise rejection:', e.reason);
            e.preventDefault();
          });

          // 修复常见的兼容性问题
          function applyCompatibilityFixes() {
            // 修复CSS渲染问题
            if (document.body) {
              document.body.style.webkitTransform = 'translateZ(0)';
              document.body.style.webkitBackfaceVisibility = 'hidden';
              document.body.style.webkitPerspective = '1000px';

              // 强制重排
              document.body.offsetHeight;

              // 修复可能的布局问题
              document.body.style.minHeight = '100vh';
              document.body.style.overflow = 'auto';
            }

            // 修复表格显示问题
            const tables = document.querySelectorAll('table');
            tables.forEach(table => {
              table.style.tableLayout = 'auto';
              table.style.width = '100%';
              table.style.borderCollapse = 'collapse';
            });

            // 修复可能的字体渲染问题
            document.documentElement.style.webkitFontSmoothing = 'antialiased';
            document.documentElement.style.mozOsxFontSmoothing = 'grayscale';

            console.log('Compatibility fixes applied');
          }

          // 立即应用修复
          if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', applyCompatibilityFixes);
          } else {
            applyCompatibilityFixes();
          }

          // 页面完全加载后再次应用修复
          window.addEventListener('load', function() {
            setTimeout(applyCompatibilityFixes, 100);
          });

          // 定期检查和修复
          setInterval(function() {
            if (document.body && document.body.scrollHeight < 100) {
              console.log('Detected rendering issue, applying fixes');
              applyCompatibilityFixes();
            }
          }, 2000);

          console.log('Enhanced WebView compatibility script loaded');
        })();
      ''';

      await controller.evaluateJavascript(source: enhancedScript);
      debugPrint('WebViewRecovery: Enhanced compatibility scripts injected');
    } catch (e) {
      debugPrint('WebViewRecovery: Failed to inject compatibility scripts: $e');
    }
  }

  /// 修复渲染问题
  static Future<void> _fixRenderingIssues(
    InAppWebViewController controller,
  ) async {
    try {
      await controller.evaluateJavascript(
        source: '''
        // 强制重新渲染整个页面
        function forceFullRerender() {
          const html = document.documentElement;
          const body = document.body;

          // 临时隐藏页面
          html.style.visibility = 'hidden';

          // 强制重排
          html.offsetHeight;

          // 重新显示页面
          html.style.visibility = 'visible';

          // 触发resize事件
          window.dispatchEvent(new Event('resize'));

          // 滚动到顶部确保内容可见
          window.scrollTo(0, 0);

          console.log('Full rerender completed');
        }

        forceFullRerender();
      ''',
      );

      debugPrint('WebViewRecovery: Rendering issues fixed');
    } catch (e) {
      debugPrint('WebViewRecovery: Failed to fix rendering issues: $e');
    }
  }

  /// 使用回退策略重新加载文件
  static Future<void> _reloadFileWithFallback(
    InAppWebViewController controller,
    File htmlFile,
  ) async {
    try {
      // 首先尝试标准加载
      final uri = WebViewCompatibility.getSafeFileUri(htmlFile);
      await controller.loadUrl(urlRequest: URLRequest(url: uri));

      // 等待加载完成
      await Future.delayed(const Duration(milliseconds: 500));

      debugPrint('WebViewRecovery: File reloaded successfully');
    } catch (e) {
      debugPrint(
        'WebViewRecovery: Standard reload failed, trying fallback: $e',
      );

      try {
        // 回退策略：直接加载HTML内容
        final htmlContent = await htmlFile.readAsString();
        await controller.loadData(data: htmlContent, mimeType: 'text/html');

        debugPrint('WebViewRecovery: Fallback reload successful');
      } catch (fallbackError) {
        debugPrint(
          'WebViewRecovery: Fallback reload also failed: $fallbackError',
        );
        rethrow;
      }
    }
  }

  /// 验证恢复结果
  static Future<bool> _validateRecovery(
    InAppWebViewController controller,
  ) async {
    try {
      // 检查页面是否正常加载
      final readyState =
          await controller.evaluateJavascript(source: 'document.readyState')
              as String?;

      if (readyState != 'complete') {
        debugPrint('WebViewRecovery: Page not fully loaded');
        return false;
      }

      // 检查页面内容
      final bodyHeight =
          await controller.evaluateJavascript(
                source: 'document.body ? document.body.scrollHeight : 0',
              )
              as int?;

      if (bodyHeight == null || bodyHeight < 50) {
        debugPrint('WebViewRecovery: Page content appears empty or too small');
        return false;
      }

      // 检查是否有JavaScript错误
      final hasErrors =
          await controller.evaluateJavascript(
                source: '''
        (function() {
          return window.hasJavaScriptErrors || false;
        })();
      ''',
              )
              as bool? ??
          false;

      if (hasErrors) {
        debugPrint('WebViewRecovery: JavaScript errors detected');
        return false;
      }

      debugPrint('WebViewRecovery: Recovery validation passed');
      return true;
    } catch (e) {
      debugPrint('WebViewRecovery: Recovery validation failed: $e');
      return false;
    }
  }

  /// 检测WebView是否需要恢复
  static Future<bool> needsRecovery(InAppWebViewController controller) async {
    try {
      // 检查页面内容高度
      final bodyHeight =
          await controller.evaluateJavascript(
                source: 'document.body ? document.body.scrollHeight : 0',
              )
              as int?;

      // 检查页面宽度
      final bodyWidth =
          await controller.evaluateJavascript(
                source: 'document.body ? document.body.scrollWidth : 0',
              )
              as int?;

      // 如果内容太小，可能需要恢复
      if ((bodyHeight != null && bodyHeight < 100) ||
          (bodyWidth != null && bodyWidth < 100)) {
        debugPrint('WebViewRecovery: Content size too small, recovery needed');
        return true;
      }

      // 检查是否有可见内容
      final hasVisibleContent =
          await controller.evaluateJavascript(
                source: '''
        (function() {
          const elements = document.querySelectorAll('*');
          for (let i = 0; i < elements.length; i++) {
            const rect = elements[i].getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {
              return true;
            }
          }
          return false;
        })();
      ''',
              )
              as bool? ??
          false;

      if (!hasVisibleContent) {
        debugPrint(
          'WebViewRecovery: No visible content detected, recovery needed',
        );
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('WebViewRecovery: Error checking if recovery needed: $e');
      return true; // 如果检查失败，假设需要恢复
    }
  }
}
