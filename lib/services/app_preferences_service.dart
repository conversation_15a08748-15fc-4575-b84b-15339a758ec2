import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppPreferencesService {
  static const String _lastOpenedFileKey = 'last_opened_file';

  // Get last opened file
  Future<String?> getLastOpenedFile() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getString(_lastOpenedFileKey);
    } catch (e) {
      debugPrint('Error getting last opened file: $e');
      return null;
    }
  }

  // Update last opened file
  Future<bool> updateLastOpenedFile(String filePath) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_lastOpenedFileKey, filePath);
    } catch (e) {
      debugPrint('Error updating last opened file: $e');
      return false;
    }
  }
}
