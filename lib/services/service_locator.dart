import 'package:get_it/get_it.dart';
import 'package:screenshot/screenshot.dart';
import 'storage_cleanup_service.dart';
import 'report_service.dart';
import '../providers/mqtt_provider.dart';

final GetIt getIt = GetIt.instance;
final GetIt locator = GetIt.instance; // 保持向后兼容

void setupLocator() {
  // 检查是否已经注册，避免重复注册
  if (!getIt.isRegistered<ScreenshotController>()) {
    getIt.registerLazySingleton(() => ScreenshotController());
  }
  if (!getIt.isRegistered<StorageCleanupService>()) {
    getIt.registerLazySingleton(() => StorageCleanupService());
  }
  if (!getIt.isRegistered<ReportService>()) {
    getIt.registerLazySingleton(() => ReportService());
  }
  if (!getIt.isRegistered<MqttProvider>()) {
    getIt.registerLazySingleton(() => MqttProvider()..initialize());
  }
}
