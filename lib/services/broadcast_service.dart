import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';

class BroadcastService {
  RawDatagramSocket? _socket;

  Future<Map<String, String>?> listenForBroadcast({
    Duration timeout = const Duration(seconds: 30),
  }) async {
    final completer = Completer<Map<String, String>?>();

    try {
      _socket = await RawDatagramSocket.bind(InternetAddress.anyIPv4, 9999);
      _socket?.broadcastEnabled = true;
      debugPrint('Listening for server broadcast on port 9999...');

      final timer = Timer(timeout, () {
        if (!completer.isCompleted) {
          debugPrint('Broadcast discovery timed out.');
          completer.complete(null);
          close();
        }
      });

      _socket?.listen((RawSocketEvent event) {
        if (event == RawSocketEvent.read) {
          final datagram = _socket?.receive();
          if (datagram != null) {
            final message = String.fromCharCodes(datagram.data);
            debugPrint('Received broadcast message: $message');

            if (message.contains('SERVER_ADDRESS:')) {
              try {
                final parts = message.split(';');
                final result = <String, String>{};

                // 解析服务器地址
                final serverAddressPart = parts.firstWhere(
                  (part) => part.startsWith('SERVER_ADDRESS:'),
                  orElse: () => '',
                );

                if (serverAddressPart.isNotEmpty) {
                  final address = serverAddressPart
                      .substring(serverAddressPart.indexOf(':') + 1)
                      .trim();
                  result['serverAddress'] = address;
                  debugPrint('Parsed server address: $address');
                }

                // 解析Salt
                // final saltPart = parts.firstWhere(
                //   (part) => part.startsWith('Salt:'),
                //   orElse: () => '',
                // );

                // if (saltPart.isNotEmpty) {
                //   final encryptedSalt = saltPart
                //       .substring(saltPart.indexOf(':') + 1)
                //       .trim();
                //   debugPrint('Encrypted salt: $encryptedSalt');

                //   final decryptedSalt = CryptoService.decryptSalt(
                //     encryptedSalt,
                //   );
                //   if (decryptedSalt != null) {
                //     result['salt'] = decryptedSalt;
                //     debugPrint('Decrypted salt: $decryptedSalt');
                //   } else {
                //     debugPrint('Failed to decrypt salt');
                //   }
                // }

                // 解析MQTT端口
                final mqttPortPart = parts.firstWhere(
                  (part) => part.startsWith('MqttPort:'),
                  orElse: () => '',
                );

                if (mqttPortPart.isNotEmpty) {
                  final port = mqttPortPart
                      .substring(mqttPortPart.indexOf(':') + 1)
                      .trim();
                  result['mqttPort'] = port;
                  debugPrint('Parsed MQTT port: $port');
                }

                // 只有在至少有服务器地址时才返回结果
                if (result.containsKey('serverAddress') &&
                    result['serverAddress']!.isNotEmpty) {
                  debugPrint('Broadcasting parsing successful: $result');
                  if (!completer.isCompleted) {
                    completer.complete(result);
                    timer.cancel();
                    close();
                  }
                } else {
                  debugPrint(
                    'No valid server address found in broadcast message',
                  );
                }
              } catch (e) {
                debugPrint('Error parsing broadcast message: $e');
              }
            }
          }
        }
      });
    } catch (e) {
      debugPrint('Error listening for broadcast: $e');
      if (!completer.isCompleted) {
        completer.complete(null);
      }
      close();
    }

    return completer.future;
  }

  void close() {
    _socket?.close();
    _socket = null;
    debugPrint('Broadcast socket closed.');
  }
}
