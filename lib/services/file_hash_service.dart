import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path/path.dart' as path;
import 'dart:convert';

/// 文件hash管理服务
/// 用于管理文件下载的hash值，判断是否需要重新下载文件
class FileHashService {
  // 单例模式
  static final FileHashService _instance = FileHashService._internal();
  factory FileHashService() => _instance;
  FileHashService._internal();

  // hash记录的键前缀
  static const String _hashPrefix = 'file_hash_';

  /// 保存文件hash记录
  /// [downloadUrl] 下载URL
  /// [hash] 文件hash值
  /// [localFilePath] 本地文件路径
  /// [extractedDirPath] 解压目录路径（如果是zip文件）
  Future<void> saveFileHash({
    required String downloadUrl,
    required String hash,
    required String localFilePath,
    String? extractedDirPath,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final fileName = path.basename(downloadUrl);
      final hashKey = '$_hashPrefix$fileName';

      final hashData = {
        'hash': hash,
        'downloadUrl': downloadUrl,
        'localFilePath': localFilePath,
        'extractedDirPath': extractedDirPath,
        'lastUpdated': DateTime.now().millisecondsSinceEpoch,
      };

      await prefs.setString(hashKey, jsonEncode(hashData));
      debugPrint('保存文件hash记录: $fileName -> $hash');
    } catch (e) {
      debugPrint('保存文件hash记录失败: $e');
    }
  }

  /// 获取文件hash记录
  /// [downloadUrl] 下载URL
  /// 返回hash记录，如果不存在返回null
  Future<Map<String, dynamic>?> getFileHash(String downloadUrl) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final fileName = path.basename(downloadUrl);
      final hashKey = '$_hashPrefix$fileName';

      final hashJson = prefs.getString(hashKey);
      if (hashJson != null) {
        final hashData = jsonDecode(hashJson) as Map<String, dynamic>;
        debugPrint('获取文件hash记录: $fileName -> ${hashData['hash']}');
        return hashData;
      }

      return null;
    } catch (e) {
      debugPrint('获取文件hash记录失败: $e');
      return null;
    }
  }

  /// 检查是否需要重新下载文件
  /// [downloadUrl] 下载URL
  /// [newHash] 新的hash值
  /// 返回true表示需要重新下载，false表示可以使用现有文件
  Future<bool> shouldRedownload(String downloadUrl, String? newHash) async {
    if (newHash == null || newHash.isEmpty) {
      // 如果没有提供hash，默认不重新下载（使用现有文件）
      debugPrint('无hash值提供，使用现有文件');
      return false;
    }

    try {
      final hashData = await getFileHash(downloadUrl);
      if (hashData == null) {
        // 没有hash记录，需要下载
        debugPrint('无hash记录，需要下载文件');
        return true;
      }

      final existingHash = hashData['hash'] as String?;
      if (existingHash != newHash) {
        // hash不匹配，需要重新下载
        debugPrint('hash不匹配，需要重新下载: 现有=$existingHash, 新=$newHash');
        return true;
      }

      // 检查本地文件是否存在
      final localFilePath = hashData['localFilePath'] as String?;
      if (localFilePath != null) {
        final file = File(localFilePath);
        if (!await file.exists()) {
          debugPrint('本地文件不存在，需要重新下载: $localFilePath');
          return true;
        }
      }

      debugPrint('hash匹配且文件存在，无需重新下载');
      return false;
    } catch (e) {
      debugPrint('检查是否需要重新下载时出错: $e，默认重新下载');
      return true;
    }
  }

  /// 删除文件hash记录
  /// [downloadUrl] 下载URL
  Future<void> removeFileHash(String downloadUrl) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final fileName = path.basename(downloadUrl);
      final hashKey = '$_hashPrefix$fileName';

      await prefs.remove(hashKey);
      debugPrint('删除文件hash记录: $fileName');
    } catch (e) {
      debugPrint('删除文件hash记录失败: $e');
    }
  }

  /// 根据文件路径删除hash记录
  /// [filePath] 文件路径
  Future<void> removeFileHashByPath(String filePath) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final fileName = path.basename(filePath);
      final hashKey = '$_hashPrefix$fileName';

      await prefs.remove(hashKey);
      debugPrint('根据路径删除文件hash记录: $fileName');
    } catch (e) {
      debugPrint('根据路径删除文件hash记录失败: $e');
    }
  }

  /// 获取所有hash记录
  /// 返回所有文件的hash记录
  Future<Map<String, Map<String, dynamic>>> getAllFileHashes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final hashRecords = <String, Map<String, dynamic>>{};

      for (final key in keys) {
        if (key.startsWith(_hashPrefix)) {
          final hashJson = prefs.getString(key);
          if (hashJson != null) {
            final fileName = key.substring(_hashPrefix.length);
            final hashData = jsonDecode(hashJson) as Map<String, dynamic>;
            hashRecords[fileName] = hashData;
          }
        }
      }

      debugPrint('获取所有hash记录: ${hashRecords.length}个记录');
      return hashRecords;
    } catch (e) {
      debugPrint('获取所有hash记录失败: $e');
      return {};
    }
  }

  /// 清理无效的hash记录（对应的文件已不存在）
  Future<void> cleanupInvalidHashes() async {
    try {
      final allHashes = await getAllFileHashes();
      int cleanedCount = 0;

      for (final entry in allHashes.entries) {
        final fileName = entry.key;
        final hashData = entry.value;
        final localFilePath = hashData['localFilePath'] as String?;

        if (localFilePath != null) {
          final file = File(localFilePath);
          if (!await file.exists()) {
            // 文件不存在，删除hash记录
            final prefs = await SharedPreferences.getInstance();
            final hashKey = '$_hashPrefix$fileName';
            await prefs.remove(hashKey);
            cleanedCount++;
            debugPrint('清理无效hash记录: $fileName');
          }
        }
      }

      debugPrint('清理完成: 删除了$cleanedCount个无效hash记录');
    } catch (e) {
      debugPrint('清理无效hash记录失败: $e');
    }
  }

  /// 生成解压目录名称（使用文件名+hash）
  /// [downloadUrl] 下载URL
  /// [hash] 文件hash值
  /// 返回解压目录名称，格式：filename_hash
  String generateExtractedDirName(String downloadUrl, String? hash) {
    final fileName = path.basenameWithoutExtension(downloadUrl);
    if (hash != null && hash.isNotEmpty) {
      return '${fileName}_$hash';
    }
    return fileName;
  }

  /// 获取解压目录名称（从已保存的hash记录中）
  /// [downloadUrl] 下载URL
  /// 返回解压目录名称，如果没有记录返回默认名称
  Future<String> getExtractedDirName(String downloadUrl) async {
    try {
      final hashData = await getFileHash(downloadUrl);
      if (hashData != null) {
        final hash = hashData['hash'] as String?;
        return generateExtractedDirName(downloadUrl, hash);
      }

      // 没有hash记录，返回默认名称
      return path.basenameWithoutExtension(downloadUrl);
    } catch (e) {
      debugPrint('获取解压目录名称失败: $e');
      return path.basenameWithoutExtension(downloadUrl);
    }
  }

  /// 清理所有hash记录
  Future<void> clearAllHashes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      int cleanedCount = 0;

      for (final key in keys) {
        if (key.startsWith(_hashPrefix)) {
          await prefs.remove(key);
          cleanedCount++;
        }
      }

      debugPrint('清理所有hash记录完成: 删除了$cleanedCount个记录');
    } catch (e) {
      debugPrint('清理所有hash记录失败: $e');
    }
  }
}
