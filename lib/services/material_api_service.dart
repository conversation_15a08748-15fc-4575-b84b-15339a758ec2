import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../models/material_file_model.dart';
import 'settings_service.dart';

class MaterialApiService {
  final Dio _dio = Dio();

  /// 获取设备材料文件列表
  Future<MaterialFileResponse> getMaterial(String equipmentId) async {
    try {
      // 检查网络连接
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult.contains(ConnectivityResult.none)) {
        debugPrint('No internet connection, cannot get materials');
        return MaterialFileResponse(code: -1, message: '没有网络连接', data: []);
      }

      // 获取服务器配置
      final serverAddress = await _getServerAddressFromSettings();
      final serverPort = await _getServerPortFromSettings();

      if (serverAddress.isEmpty) {
        debugPrint('Server address is empty, cannot get materials');
        return MaterialFileResponse(code: -1, message: '服务器地址为空', data: []);
      }

      // 构建请求URL
      final baseUrl = _buildBaseUrl(serverAddress, serverPort);
      final url = '$baseUrl/v1/equipment/getMaterial/$equipmentId';

      debugPrint('Getting materials from: $url');

      final response = await _dio.get(url, options: _getDioOptions());

      if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
        debugPrint('Materials retrieved successfully: ${response.data}');
        return MaterialFileResponse.fromJson(response.data);
      } else {
        debugPrint(
          'Failed to get materials: ${response.statusCode}, ${response.data}',
        );
        return MaterialFileResponse(
          code: response.statusCode ?? -1,
          message: '获取材料失败',
          data: [],
        );
      }
    } catch (e) {
      debugPrint('Error getting materials: $e');
      return MaterialFileResponse(code: -1, message: '获取材料时发生错误: $e', data: []);
    }
  }

  /// 从设置中获取服务器地址
  Future<String> _getServerAddressFromSettings() async {
    try {
      final settingsService = SettingsService();
      final settings = await settingsService.loadSettings();
      return settings.mqttServerAddress ?? '';
    } catch (e) {
      debugPrint('Error getting server address from settings: $e');
      return '';
    }
  }

  /// 从设置中获取服务器端口
  Future<String> _getServerPortFromSettings() async {
    try {
      final settingsService = SettingsService();
      final settings = await settingsService.loadSettings();
      return settings.serverPort ?? '8567';
    } catch (e) {
      debugPrint('Error getting server port from settings: $e');
      return '8567';
    }
  }

  String _buildBaseUrl(String serverAddress, String port) {
    if (!serverAddress.startsWith('http://') &&
        !serverAddress.startsWith('https://')) {
      return 'http://$serverAddress:$port';
    }
    return serverAddress;
  }

  Options _getDioOptions() {
    return Options(
      headers: {'Content-Type': 'application/json'},
      receiveTimeout: const Duration(seconds: 10),
      sendTimeout: const Duration(seconds: 10),
    );
  }
}
