import 'package:flutter/foundation.dart';
import 'package:media_kit/media_kit.dart';

/// 视频播放监控服务
/// 监控视频播放状态，收集性能数据，处理播放异常
class VideoPlaybackMonitor {
  static VideoPlaybackMonitor? _instance;
  static VideoPlaybackMonitor get instance =>
      _instance ??= VideoPlaybackMonitor._();

  VideoPlaybackMonitor._();

  // 播放统计数据
  final Map<String, VideoPlaybackStats> _playbackStats = {};
  final List<VideoPlaybackEvent> _playbackEvents = [];

  // 当前监控的播放器
  Player? _currentMediaKitPlayer;

  /// 开始监控MediaKit播放器
  void startMonitoringMediaKit(Player player, String videoPath) {
    _currentMediaKitPlayer = player;
    _initializeStats(videoPath);

    // 监听播放状态变化
    player.stream.playing.listen((isPlaying) {
      _recordEvent(videoPath, VideoPlaybackEventType.playStateChanged, {
        'isPlaying': isPlaying,
        'engine': 'media_kit',
      });
    });

    // 监听播放位置变化
    player.stream.position.listen((position) {
      _updatePlaybackPosition(videoPath, position.inMilliseconds);
    });

    // 监听缓冲状态
    player.stream.buffering.listen((isBuffering) {
      _recordEvent(videoPath, VideoPlaybackEventType.bufferingChanged, {
        'isBuffering': isBuffering,
        'engine': 'media_kit',
      });
    });

    // 监听错误
    player.stream.error.listen((error) {
      _recordEvent(videoPath, VideoPlaybackEventType.error, {
        'error': error,
        'engine': 'media_kit',
      });
    });

    debugPrint(
      'VideoPlaybackMonitor: Started monitoring MediaKit player for $videoPath',
    );
  }

  /// 停止监控
  void stopMonitoring(String videoPath) {
    _currentMediaKitPlayer = null;

    _recordEvent(videoPath, VideoPlaybackEventType.stopped, {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });

    debugPrint('VideoPlaybackMonitor: Stopped monitoring for $videoPath');
  }

  /// 初始化播放统计
  void _initializeStats(String videoPath) {
    _playbackStats[videoPath] = VideoPlaybackStats(
      videoPath: videoPath,
      startTime: DateTime.now(),
    );

    _recordEvent(videoPath, VideoPlaybackEventType.started, {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  /// 记录播放事件
  void _recordEvent(
    String videoPath,
    VideoPlaybackEventType type,
    Map<String, dynamic> data,
  ) {
    final event = VideoPlaybackEvent(
      videoPath: videoPath,
      type: type,
      timestamp: DateTime.now(),
      data: data,
    );

    _playbackEvents.add(event);

    // 更新统计数据
    final stats = _playbackStats[videoPath];
    if (stats != null) {
      stats.updateWithEvent(event);
    }

    // 限制事件数量，避免内存泄漏
    if (_playbackEvents.length > 1000) {
      _playbackEvents.removeRange(0, 500);
    }

    debugPrint('VideoPlaybackMonitor: Event recorded - $type for $videoPath');
  }

  /// 更新播放位置
  void _updatePlaybackPosition(String videoPath, int positionMs) {
    final stats = _playbackStats[videoPath];
    if (stats != null) {
      stats.currentPosition = positionMs;
      stats.lastUpdateTime = DateTime.now();
    }
  }

  /// 获取播放统计数据
  VideoPlaybackStats? getStats(String videoPath) {
    return _playbackStats[videoPath];
  }

  /// 获取所有播放事件
  List<VideoPlaybackEvent> getEvents({String? videoPath}) {
    if (videoPath != null) {
      return _playbackEvents
          .where((event) => event.videoPath == videoPath)
          .toList();
    }
    return List.from(_playbackEvents);
  }

  /// 获取播放质量报告
  String getQualityReport(String videoPath) {
    final stats = _playbackStats[videoPath];
    final events = getEvents(videoPath: videoPath);

    if (stats == null) {
      return '没有找到播放统计数据';
    }

    final buffer = StringBuffer();
    buffer.writeln('=== 视频播放质量报告 ===');
    buffer.writeln('视频文件: $videoPath');
    buffer.writeln('开始时间: ${stats.startTime}');
    buffer.writeln('播放时长: ${stats.playDuration.inSeconds}秒');
    buffer.writeln('缓冲次数: ${stats.bufferingCount}');
    buffer.writeln('错误次数: ${stats.errorCount}');
    buffer.writeln('当前位置: ${stats.currentPosition}ms');
    buffer.writeln();

    buffer.writeln('播放事件:');
    for (final event in events.take(10)) {
      buffer.writeln('  ${event.timestamp}: ${event.type} - ${event.data}');
    }

    if (events.length > 10) {
      buffer.writeln('  ... 还有${events.length - 10}个事件');
    }

    return buffer.toString();
  }

  /// 检查播放健康状况
  PlaybackHealthStatus checkPlaybackHealth(String videoPath) {
    final stats = _playbackStats[videoPath];
    if (stats == null) {
      return PlaybackHealthStatus.unknown;
    }

    // 检查错误率
    if (stats.errorCount > 3) {
      return PlaybackHealthStatus.poor;
    }

    // 检查缓冲频率
    final playDurationMinutes = stats.playDuration.inMinutes;
    if (playDurationMinutes > 0 &&
        stats.bufferingCount / playDurationMinutes > 2) {
      return PlaybackHealthStatus.fair;
    }

    // 检查是否长时间无更新
    final timeSinceLastUpdate = DateTime.now().difference(stats.lastUpdateTime);
    if (timeSinceLastUpdate.inMinutes > 5) {
      return PlaybackHealthStatus.stalled;
    }

    return PlaybackHealthStatus.good;
  }

  /// 清理旧数据
  void cleanup() {
    final now = DateTime.now();

    // 清理超过1小时的统计数据
    _playbackStats.removeWhere((key, stats) {
      return now.difference(stats.startTime).inHours > 1;
    });

    // 清理超过1小时的事件
    _playbackEvents.removeWhere((event) {
      return now.difference(event.timestamp).inHours > 1;
    });

    debugPrint('VideoPlaybackMonitor: Cleaned up old data');
  }

  /// 获取性能建议
  List<String> getPerformanceRecommendations(String videoPath) {
    final stats = _playbackStats[videoPath];
    final recommendations = <String>[];

    if (stats == null) {
      return ['无法获取播放统计数据'];
    }

    if (stats.errorCount > 2) {
      recommendations.add('播放错误较多，建议检查视频文件或网络连接');
    }

    if (stats.bufferingCount > 5) {
      recommendations.add('缓冲次数较多，建议降低视频质量或检查网络速度');
    }

    final playDurationMinutes = stats.playDuration.inMinutes;
    if (playDurationMinutes > 10 &&
        stats.bufferingCount / playDurationMinutes > 1) {
      recommendations.add('缓冲频率过高，建议使用软件解码或更换播放引擎');
    }

    if (recommendations.isEmpty) {
      recommendations.add('播放状况良好，无需特殊优化');
    }

    return recommendations;
  }

  /// 导出监控数据
  Map<String, dynamic> exportData() {
    return {
      'stats': _playbackStats.map((key, value) => MapEntry(key, value.toMap())),
      'events': _playbackEvents.map((event) => event.toMap()).toList(),
      'exportTime': DateTime.now().toIso8601String(),
    };
  }
}

/// 视频播放统计数据
class VideoPlaybackStats {
  final String videoPath;
  final DateTime startTime;
  DateTime lastUpdateTime;
  int currentPosition = 0;
  int bufferingCount = 0;
  int errorCount = 0;
  Duration playDuration = Duration.zero;

  VideoPlaybackStats({required this.videoPath, required this.startTime})
    : lastUpdateTime = startTime;

  void updateWithEvent(VideoPlaybackEvent event) {
    lastUpdateTime = event.timestamp;
    playDuration = event.timestamp.difference(startTime);

    switch (event.type) {
      case VideoPlaybackEventType.bufferingChanged:
        if (event.data['isBuffering'] == true) {
          bufferingCount++;
        }
        break;
      case VideoPlaybackEventType.error:
        errorCount++;
        break;
      default:
        break;
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'videoPath': videoPath,
      'startTime': startTime.toIso8601String(),
      'lastUpdateTime': lastUpdateTime.toIso8601String(),
      'currentPosition': currentPosition,
      'bufferingCount': bufferingCount,
      'errorCount': errorCount,
      'playDuration': playDuration.inMilliseconds,
    };
  }
}

/// 视频播放事件
class VideoPlaybackEvent {
  final String videoPath;
  final VideoPlaybackEventType type;
  final DateTime timestamp;
  final Map<String, dynamic> data;

  VideoPlaybackEvent({
    required this.videoPath,
    required this.type,
    required this.timestamp,
    required this.data,
  });

  Map<String, dynamic> toMap() {
    return {
      'videoPath': videoPath,
      'type': type.toString(),
      'timestamp': timestamp.toIso8601String(),
      'data': data,
    };
  }
}

/// 播放事件类型
enum VideoPlaybackEventType {
  started,
  stopped,
  playStateChanged,
  bufferingChanged,
  error,
  positionChanged,
}

/// 播放健康状况
enum PlaybackHealthStatus { good, fair, poor, stalled, unknown }
