import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/mqtt_provider.dart';
import '../providers/settings_provider.dart';
import '../services/service_locator.dart';
import '../services/settings_service.dart';

class MqttTopicHelper {
  /// 更新组名和设备别名后重新连接MQTT
  static Future<void> updateTopicAndReconnect({
    required BuildContext context,
    required String? groupName,
    required String? deviceAlias,
  }) async {
    try {
      final settingsProvider = Provider.of<SettingsProvider>(
        context,
        listen: false,
      );

      // 更新设置 (内部会自动重新连接MQTT)
      await settingsProvider.updateGroupAndAlias(groupName, deviceAlias);

      debugPrint('MQTT topic updated successfully');
    } catch (e) {
      debugPrint('Error updating MQTT topic: $e');
    }
  }

  /// 获取当前的MQTT topic格式
  static String getCurrentTopic(SettingsProvider settingsProvider) {
    return settingsProvider.settings.generateMqttTopic();
  }

  // 防止重复重连的标志
  static bool _isReconnecting = false;

  /// 手动重新连接MQTT
  static Future<void> reconnectMqtt() async {
    // 如果已经在重连过程中，直接返回
    if (_isReconnecting) {
      debugPrint('MQTT重连已在进行中，跳过此次重连请求');
      return;
    }

    _isReconnecting = true;

    try {
      // 获取设置服务
      final settingsService = SettingsService();
      final settings = await settingsService.loadSettings();

      // 检查组名和设备别名是否都存在
      if (settings.groupName == null ||
          settings.groupName!.isEmpty ||
          settings.deviceAlias == null ||
          settings.deviceAlias!.isEmpty) {
        // 如果组名或设备别名为空，断开MQTT连接
        final mqttProvider = locator<MqttProvider>();
        mqttProvider.disconnect();
        debugPrint('组名或设备别名为空，断开MQTT连接');
        return;
      }

      final mqttProvider = locator<MqttProvider>();

      // 断开连接
      mqttProvider.disconnect();

      // 等待足够长的时间确保断开连接完成
      await Future.delayed(const Duration(seconds: 3));

      // 重新连接
      await mqttProvider.connect();

      debugPrint('MQTT manually reconnected successfully');
    } catch (e) {
      debugPrint('Error manually reconnecting MQTT: $e');
    } finally {
      _isReconnecting = false;
    }
  }
}
