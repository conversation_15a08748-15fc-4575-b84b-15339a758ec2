import '../services/version_service.dart';

/// 版本号工具类
/// 提供便捷的版本号操作方法
class VersionUtils {
  static final VersionService _versionService = VersionService();

  /// 获取当前版本号
  static Future<String> getCurrentVersion() async {
    return await _versionService.getCurrentVersion();
  }

  /// 获取构建号
  static Future<int> getBuildNumber() async {
    return await _versionService.getBuildNumber();
  }

  /// 递增构建号并返回新版本
  static Future<String> incrementVersion() async {
    return await _versionService.incrementBuildNumber();
  }

  /// 获取完整版本信息
  static Future<Map<String, dynamic>> getVersionInfo() async {
    return await _versionService.getVersionInfo();
  }

  /// 格式化版本号显示
  /// 例如: "版本 2025.01.19+5 (构建号: 5)"
  static Future<String> getFormattedVersion() async {
    final version = await getCurrentVersion();
    final buildNumber = await getBuildNumber();
    return "版本 $version (构建号: $buildNumber)";
  }

  /// 获取简短版本号（不包含构建号）
  static Future<String> getShortVersion() async {
    final version = await getCurrentVersion();
    return version.split('+')[0];
  }

  /// 检查是否需要更新版本号
  static Future<bool> shouldUpdateVersion() async {
    return await _versionService.shouldUpdateVersion();
  }

  /// 自动更新版本号（如果需要）
  static Future<String> autoUpdateIfNeeded() async {
    return await _versionService.autoUpdateVersionIfNeeded();
  }

  /// 重置版本号
  static Future<void> resetVersion() async {
    await _versionService.resetVersion();
  }

  /// 手动设置版本号
  static Future<void> setVersion(String version) async {
    await _versionService.setVersion(version);
  }

  /// 生成用于API提交的版本信息
  static Future<Map<String, String>> getApiVersionInfo() async {
    final version = await getCurrentVersion();
    final buildNumber = await getBuildNumber();
    final versionInfo = await getVersionInfo();

    return {
      'app_version': version,
      'build_number': buildNumber.toString(),
      'platform': versionInfo['platform'] ?? 'unknown',
      'generated_at': DateTime.now().toIso8601String(),
    };
  }
}