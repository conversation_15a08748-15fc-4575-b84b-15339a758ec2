import '../services/app_version_service.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// Flutter 官方推荐的版本号工具类
/// 提供便捷的版本号操作方法
class AppVersionUtils {
  static final AppVersionService _versionService = AppVersionService();

  /// 获取完整版本号（包含构建号）
  /// 例如：1.0.0+1
  static Future<String> getFullVersion() async {
    return await _versionService.getFullVersion();
  }

  /// 获取版本名称（不包含构建号）
  /// 例如：1.0.0
  static Future<String> getVersionName() async {
    return await _versionService.getVersionName();
  }

  /// 获取构建号
  /// 例如：1
  static Future<String> getBuildNumber() async {
    return await _versionService.getBuildNumber();
  }

  /// 获取应用名称
  static Future<String> getAppName() async {
    return await _versionService.getAppName();
  }

  /// 获取包名
  static Future<String> getPackageName() async {
    return await _versionService.getPackageName();
  }

  /// 获取格式化的版本显示
  /// 例如：版本 1.0.0 (构建号: 1)
  static Future<String> getFormattedVersion() async {
    return await _versionService.getFormattedVersion();
  }

  /// 获取完整版本信息
  static Future<Map<String, dynamic>> getVersionInfo() async {
    return await _versionService.getVersionInfo();
  }

  /// 获取API提交用的版本信息
  static Future<Map<String, String>> getApiVersionInfo() async {
    return await _versionService.getApiVersionInfo();
  }

  /// 记录版本更新时间
  static Future<void> recordUpdateTime() async {
    await _versionService.recordUpdateTime();
  }

  /// 获取 PackageInfo 对象
  static Future<PackageInfo> getPackageInfo() async {
    return await _versionService.getPackageInfo();
  }

  /// 检查版本号格式是否有效
  static bool isValidVersionFormat(String version) {
    return _versionService.isValidVersionFormat(version);
  }

  /// 比较两个版本号
  static int compareVersions(String version1, String version2) {
    return _versionService.compareVersions(version1, version2);
  }

  /// 获取简单的版本信息字符串（用于显示）
  static Future<String> getSimpleVersionString() async {
    final appName = await getAppName();
    final version = await getVersionName();
    final buildNumber = await getBuildNumber();
    return '$appName v$version ($buildNumber)';
  }
}
