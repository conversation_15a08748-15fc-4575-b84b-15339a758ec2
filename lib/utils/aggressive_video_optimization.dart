import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

/// 激进的视频优化工具类
/// 专门解决严重的视频卡顿问题
class AggressiveVideoOptimization {
  /// 应用激进的WebView设置
  static InAppWebViewSettings getAggressiveSettings() {
    return InAppWebViewSettings(
      // 基础设置
      javaScriptEnabled: true,
      domStorageEnabled: true,
      databaseEnabled: false, // 禁用数据库以减少开销
      // 文件访问
      allowFileAccess: true,
      allowFileAccessFromFileURLs: true,
      allowUniversalAccessFromFileURLs: true,

      // 缓存设置 - 激进优化
      cacheEnabled: true, // 禁用缓存以避免内存问题
      clearCache: true,

      // 网络设置
      useShouldOverrideUrlLoading: false, // 减少拦截开销
      mediaPlaybackRequiresUserGesture: false,

      // 显示设置
      supportZoom: false,
      transparentBackground: false,
      verticalScrollBarEnabled: false, // 禁用滚动条
      horizontalScrollBarEnabled: false,

      // 字体设置
      minimumFontSize: 12,
      defaultFontSize: 16,
      defaultFixedFontSize: 13,

      // 视口设置
      useWideViewPort: false, // 简化视口处理
      loadWithOverviewMode: false,

      // 错误页面
      disableDefaultErrorPage: true,

      // 资源加载
      useOnLoadResource: false, // 减少资源监听开销
      // 渲染优化 - 最激进的设置
      forceDark: ForceDark.OFF,
      hardwareAcceleration: false, // 强制禁用硬件加速，使用软件解码
      useHybridComposition: false, // 软件解码时建议使用Hybrid Composition
      // 混合内容
      mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,

      // 文本和布局
      textZoom: 100,
      layoutAlgorithm: LayoutAlgorithm.NORMAL, // 使用最简单的布局算法
      // 图片加载
      loadsImagesAutomatically: true,
      blockNetworkImage: false,
      blockNetworkLoads: false,

      // 安全设置
      allowContentAccess: true,
      builtInZoomControls: false,
      displayZoomControls: false,

      // 性能优化
      overScrollMode: OverScrollMode.NEVER,
      scrollBarStyle: ScrollBarStyle.SCROLLBARS_OUTSIDE_OVERLAY,
    );
  }

  /// 强制清理WebView内存
  static Future<void> forceMemoryCleanup(
    InAppWebViewController controller,
  ) async {
    try {
      // 强制垃圾回收
      await controller.evaluateJavascript(
        source: '''
          if (window.aggressiveVideoOptimization) {
            window.aggressiveVideoOptimization.forceGC();
          }

          // 清理可能的内存泄漏
          if (window.gc) {
            window.gc();
          }

          // 清理DOM
          const videos = document.querySelectorAll('video');
          videos.forEach(function(video) {
            if (video.paused && video.currentTime === 0) {
              video.removeAttribute('src');
              video.load();
            }
          });
        ''',
      );

      debugPrint('AggressiveVideoOptimization: Memory cleanup completed');
    } catch (e) {
      debugPrint('AggressiveVideoOptimization: Memory cleanup failed: $e');
    }
  }
}
