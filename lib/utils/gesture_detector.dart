import 'package:flutter/material.dart';

class MultiTouchGestureDetector extends StatefulWidget {
  final Widget child;
  final VoidCallback onDoubleTapWithTwoFingers;

  const MultiTouchGestureDetector({
    super.key,
    required this.child,
    required this.onDoubleTapWithTwoFingers,
  });

  @override
  State<MultiTouchGestureDetector> createState() =>
      _MultiTouchGestureDetectorState();
}

class _MultiTouchGestureDetectorState extends State<MultiTouchGestureDetector> {
  // Constants for gesture detection
  static const int _requiredFingerCount = 2;
  static const int _requiredTapCount = 2;
  static const Duration _maxTapInterval = Duration(milliseconds: 300);
  static const Duration _maxDoubleTapInterval = Duration(milliseconds: 500);

  // State variables
  int _fingerCount = 0;
  int _tapCount = 0;
  DateTime? _lastTapTime;

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: _onPointerDown,
      onPointerUp: _onPointerUp,
      child: widget.child,
    );
  }

  // Handle pointer down event
  void _onPointerDown(PointerDownEvent event) {
    _fingerCount++;

    // Reset tap count if more than required fingers are detected
    if (_fingerCount > _requiredFingerCount) {
      _tapCount = 0;
      _lastTapTime = null;
    }
  }

  // Handle pointer up event
  void _onPointerUp(PointerUpEvent event) {
    _fingerCount--;

    // If all fingers are up, check if it was a valid tap
    if (_fingerCount == 0) {
      final now = DateTime.now();

      // Check if this is part of a double tap
      if (_lastTapTime != null &&
          now.difference(_lastTapTime!) <= _maxDoubleTapInterval) {
        _tapCount++;

        // Check if we have reached the required tap count with the required finger count
        if (_tapCount >= _requiredTapCount) {
          widget.onDoubleTapWithTwoFingers();
          _tapCount = 0;
          _lastTapTime = null;
        } else {
          _lastTapTime = now;
        }
      } else {
        // This is the first tap
        _tapCount = 1;
        _lastTapTime = now;
      }

      // Reset tap count after a certain interval
      Future.delayed(_maxTapInterval, () {
        if (mounted &&
            _lastTapTime != null &&
            DateTime.now().difference(_lastTapTime!) > _maxTapInterval) {
          _tapCount = 0;
          _lastTapTime = null;
        }
      });
    }
  }
}
