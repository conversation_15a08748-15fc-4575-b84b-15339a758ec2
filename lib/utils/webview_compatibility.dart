import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:device_info_plus/device_info_plus.dart';

/// WebView兼容性工具类
/// 用于处理不同设备和Android版本的WebView兼容性问题
class WebViewCompatibility {
  // 缓存设备信息以避免重复查询
  static AndroidDeviceInfo? _androidDeviceInfo;
  static IosDeviceInfo? _iosDeviceInfo;
  static bool _deviceInfoInitialized = false;

  /// 初始化设备信息
  static Future<void> _initDeviceInfo() async {
    if (_deviceInfoInitialized) return;

    try {
      final deviceInfo = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        _androidDeviceInfo = await deviceInfo.androidInfo;
      } else if (Platform.isIOS) {
        _iosDeviceInfo = await deviceInfo.iosInfo;
      }
      _deviceInfoInitialized = true;
    } catch (e) {
      debugPrint('WebViewCompatibility: Failed to get device info: $e');
      _deviceInfoInitialized = true; // 标记为已初始化，避免重复尝试
    }
  }

  /// 检查是否应该禁用硬件加速（兼容性问题设备）
  static bool _shouldDisableHardwareAcceleration() {
    // 强制禁用硬件加速以使用软件解码
    debugPrint(
      'WebViewCompatibility: Hardware acceleration is forcibly disabled for software decoding.',
    );
    return true;
  }

  /// 获取优化的硬件加速设置
  static bool _getOptimalHardwareAcceleration() {
    if (Platform.isIOS) {
      // iOS设备通常硬件加速兼容性较好
      return true;
    }

    if (Platform.isAndroid) {
      // 检查是否应该禁用硬件加速
      if (_shouldDisableHardwareAcceleration()) {
        return false;
      }

      // 默认启用硬件加速以获得更好的性能
      return false;
    }

    return false;
  }

  /// 获取兼容的WebView设置
  static Future<InAppWebViewSettings> getCompatibleSettings() async {
    // 确保设备信息已初始化
    await _initDeviceInfo();

    final hardwareAcceleration = _getOptimalHardwareAcceleration();

    debugPrint(
      'WebViewCompatibility: Hardware acceleration enabled: $hardwareAcceleration',
    );

    return InAppWebViewSettings(
      // 基础设置
      javaScriptEnabled: true,
      domStorageEnabled: true,
      databaseEnabled: true,

      // 文件访问权限 - 对于本地HTML文件很重要
      allowFileAccess: true,
      allowFileAccessFromFileURLs: true,
      allowUniversalAccessFromFileURLs: true,

      // 缓存设置 - 适度缓存以提高性能
      cacheEnabled: true,
      clearCache: false,

      // 网络设置
      useShouldOverrideUrlLoading: true,
      mediaPlaybackRequiresUserGesture: false,

      // 显示设置
      supportZoom: false,
      transparentBackground: false,
      verticalScrollBarEnabled: true,
      horizontalScrollBarEnabled: true,

      // 字体设置 - 确保文本正常显示
      minimumFontSize: 8,
      defaultFontSize: 16,
      defaultFixedFontSize: 13,

      // 视口设置 - 修复布局问题
      useWideViewPort: true,
      loadWithOverviewMode: true,

      // 错误页面
      disableDefaultErrorPage: false,

      // 资源加载
      useOnLoadResource: true,

      // 渲染优化
      forceDark: ForceDark.OFF,

      // 硬件加速设置 - 关键优化
      hardwareAcceleration: false, // 强制使用软件解码
      // 混合内容模式
      mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,

      // 文本缩放
      textZoom: 100,

      // 布局算法 - 根据硬件加速情况选择
      layoutAlgorithm: hardwareAcceleration
          ? LayoutAlgorithm.TEXT_AUTOSIZING
          : LayoutAlgorithm.NORMAL,

      // 图片加载
      loadsImagesAutomatically: true,
      blockNetworkImage: false,
      blockNetworkLoads: false,

      // 安全设置 - 允许本地文件加载
      allowContentAccess: true,
      builtInZoomControls: false,
      displayZoomControls: false,

      // 性能优化
      overScrollMode: OverScrollMode.NEVER,
      scrollBarStyle: ScrollBarStyle.SCROLLBARS_INSIDE_OVERLAY,

      // 渲染模式 - 强制使用Virtual Display模式以获得最佳视频性能
      useHybridComposition: true, // 软件解码时建议使用Hybrid Composition
      // iOS特定设置
      allowsInlineMediaPlayback: true,
      allowsBackForwardNavigationGestures: false,
      allowsPictureInPictureMediaPlayback: false, // 禁用画中画，避免全屏播放
      isFraudulentWebsiteWarningEnabled: false,
      selectionGranularity: SelectionGranularity.DYNAMIC,
      dataDetectorTypes: [DataDetectorTypes.NONE],

      // 内容模式设置
      preferredContentMode: UserPreferredContentMode.DESKTOP,
    );
  }

  /// 获取兼容的WebView设置（同步版本，用于向后兼容）
  static InAppWebViewSettings getCompatibleSettingsSync() {
    // 如果设备信息未初始化，使用保守的默认设置
    final hardwareAcceleration = _deviceInfoInitialized
        ? _getOptimalHardwareAcceleration()
        : true; // 默认启用硬件加速

    debugPrint(
      'WebViewCompatibility: Hardware acceleration enabled (sync): $hardwareAcceleration',
    );

    return InAppWebViewSettings(
      // 基础设置
      javaScriptEnabled: true,
      domStorageEnabled: true,
      databaseEnabled: true,

      // 文件访问权限
      allowFileAccess: true,
      allowFileAccessFromFileURLs: true,
      allowUniversalAccessFromFileURLs: true,

      // 缓存设置
      cacheEnabled: true,
      clearCache: false,

      // 网络设置
      useShouldOverrideUrlLoading: true,
      mediaPlaybackRequiresUserGesture: false,
      // 显示设置
      supportZoom: false,
      transparentBackground: false,
      verticalScrollBarEnabled: true,
      horizontalScrollBarEnabled: true,

      // 字体设置
      minimumFontSize: 8,
      defaultFontSize: 16,
      defaultFixedFontSize: 13,

      // 视口设置
      useWideViewPort: true,
      loadWithOverviewMode: true,

      // 错误页面
      disableDefaultErrorPage: false,

      // 资源加载
      useOnLoadResource: true,

      // 渲染优化
      forceDark: ForceDark.OFF,

      // 硬件加速设置
      hardwareAcceleration: false, // 强制使用软件解码
      // 其他设置
      mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
      textZoom: 100,
      layoutAlgorithm: hardwareAcceleration
          ? LayoutAlgorithm.TEXT_AUTOSIZING
          : LayoutAlgorithm.NORMAL,
      loadsImagesAutomatically: true,
      blockNetworkImage: false,
      blockNetworkLoads: false,
      allowContentAccess: true,
      builtInZoomControls: false,
      displayZoomControls: false,
      overScrollMode: OverScrollMode.NEVER,
      scrollBarStyle: ScrollBarStyle.SCROLLBARS_INSIDE_OVERLAY,
      useHybridComposition: true, // 软件解码时建议使用Hybrid Composition
      // iOS特定设置
      allowsInlineMediaPlayback: true,
      allowsBackForwardNavigationGestures: false,
      allowsPictureInPictureMediaPlayback: true,
      isFraudulentWebsiteWarningEnabled: false,
      selectionGranularity: SelectionGranularity.DYNAMIC,
      dataDetectorTypes: [DataDetectorTypes.NONE],
      preferredContentMode: UserPreferredContentMode.DESKTOP,
    );
  }

  /// 获取Android特定设置
  static Map<String, dynamic> getAndroidSpecificSettings() {
    if (!Platform.isAndroid) return {};

    return {
      // 渲染模式 - 使用更兼容的模式
      'useHybridComposition': true, // 软件解码时建议使用Hybrid Composition
      // 硬件加速 - 在某些设备上可能导致问题
      'hardwareAcceleration': false, // 强制使用软件解码
      // 网络安全配置
      'mixedContentMode': MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,

      // 文本缩放
      'textZoom': 100,

      // 布局算法
      'layoutAlgorithm': LayoutAlgorithm.TEXT_AUTOSIZING,

      // 加载图片
      'loadsImagesAutomatically': true,

      // 阻止网络图片加载（对于本地HTML文件）
      'blockNetworkImage': false,

      // 阻止网络加载
      'blockNetworkLoads': false,
    };
  }

  /// 获取iOS特定设置
  static Map<String, dynamic> getIOSSpecificSettings() {
    if (!Platform.isIOS) return {};

    return {
      'allowsInlineMediaPlayback': true,
      'allowsBackForwardNavigationGestures': false,
      'allowsPictureInPictureMediaPlayback': true,
      'isFraudulentWebsiteWarningEnabled': false,
      'selectionGranularity': SelectionGranularity.DYNAMIC,
      'dataDetectorTypes': [DataDetectorTypes.NONE],
    };
  }

  /// 检查文件是否可以安全加载
  static bool canLoadFile(File file) {
    if (!file.existsSync()) {
      debugPrint('WebView: File does not exist: ${file.path}');
      return false;
    }

    if (!file.path.toLowerCase().endsWith('.html')) {
      debugPrint('WebView: File is not HTML: ${file.path}');
      return false;
    }

    try {
      // 尝试读取文件的前几个字节来验证文件完整性
      final bytes = file.readAsBytesSync().take(100).toList();
      if (bytes.isEmpty) {
        debugPrint('WebView: File is empty: ${file.path}');
        return false;
      }
    } catch (e) {
      debugPrint('WebView: Cannot read file: ${file.path}, error: $e');
      return false;
    }

    return true;
  }

  /// 获取安全的文件URI
  static WebUri getSafeFileUri(File file) {
    // 确保路径格式正确
    String path = file.absolute.path;

    // 规范化路径分隔符
    path = path.replaceAll('\\', '/');

    // 处理不同平台的URI格式
    if (Platform.isAndroid) {
      // Android需要特殊处理
      // 移除可能存在的file://前缀
      if (path.startsWith('file://')) {
        path = path.substring(7);
      }

      // 确保路径以/开头
      if (!path.startsWith('/')) {
        path = '/$path';
      }

      // 对路径进行URL编码，但保留路径分隔符
      final segments = path.split('/');
      final encodedSegments = segments.map((segment) {
        if (segment.isEmpty) return segment;
        return Uri.encodeComponent(segment);
      }).toList();
      path = encodedSegments.join('/');

      // 添加file://协议
      path = 'file://$path';
    } else if (Platform.isIOS) {
      // iOS处理
      if (!path.startsWith('file://')) {
        path = 'file://$path';
      }
    }

    debugPrint('WebViewCompatibility: Generated URI: $path');
    return WebUri(path);
  }

  /// 处理WebView错误的辅助方法
  static bool shouldIgnoreError(WebResourceError error) {
    // 某些错误可以忽略，不影响HTML文件的正常显示
    final ignorableErrors = [
      WebResourceErrorType.UNKNOWN,
      WebResourceErrorType.HOST_LOOKUP,
      WebResourceErrorType.TIMEOUT,
    ];

    if (ignorableErrors.contains(error.type)) {
      return true;
    }

    // 忽略某些特定的错误消息
    final ignorableMessages = [
      'net::ERR_CLEARTEXT_NOT_PERMITTED',
      'net::ERR_NETWORK_CHANGED',
      'net::ERR_INTERNET_DISCONNECTED',
    ];

    for (final message in ignorableMessages) {
      if (error.description.contains(message)) {
        return true;
      }
    }

    return false;
  }

  /// 获取错误重试延迟时间
  static Duration getRetryDelay(int retryCount) {
    // 递增延迟：500ms, 1s, 2s, 4s...
    final delayMs = 500 * (1 << retryCount.clamp(0, 3));
    return Duration(milliseconds: delayMs);
  }
}
