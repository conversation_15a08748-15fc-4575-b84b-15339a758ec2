import 'package:path/path.dart' as path;
import 'package:flutter/material.dart';

class FileUtils {
  // Supported document file extensions
  static const Set<String> documentExtensions = {
    '.pdf',
    '.doc',
    '.docx',
    '.xls',
    '.xlsx',
    '.ppt',
    '.pptx',
  };

  // ZIP file extensions
  static const Set<String> zipExtensions = {'.zip'};

  // Supported image file extensions
  static const Set<String> imageExtensions = {
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.bmp',
    '.webp',
  };

  // Supported video file extensions
  static const Set<String> videoExtensions = {'.mp4', '.mov', '.avi', '.mkv'};

  // Supported HTML file extensions
  static const Set<String> htmlExtensions = {'.html', '.htm'};

  /// Check if a file is a document file (PDF, PPT, Excel, etc.)
  static bool isDocumentFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    return documentExtensions.contains(extension);
  }

  /// Check if a file is a PDF file
  static bool isPdfFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    return extension == '.pdf';
  }

  /// Check if a file is an Office file
  static bool isOfficeFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    return documentExtensions.contains(extension) && extension != '.pdf';
  }

  /// Check if a file is a ZIP file
  static bool isZipFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    return zipExtensions.contains(extension);
  }

  /// Check if a file is an image file
  static bool isImageFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    return imageExtensions.contains(extension);
  }

  /// Check if a file is a video file
  static bool isVideoFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    return videoExtensions.contains(extension);
  }

  /// Check if a file is an HTML file
  static bool isHtmlFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    return htmlExtensions.contains(extension);
  }

  /// Get the file extension from a file path
  static String getFileExtension(String filePath) {
    return path.extension(filePath).toLowerCase();
  }

  /// Get the file name without extension
  static String getFileNameWithoutExtension(String filePath) {
    return path.basenameWithoutExtension(filePath);
  }

  /// Get the file name with extension
  static String getFileName(String filePath) {
    return path.basename(filePath);
  }

  /// Check if a file type is supported for opening
  static bool isSupportedFileType(String filePath) {
    return isDocumentFile(filePath) ||
        isZipFile(filePath) ||
        isImageFile(filePath) ||
        isVideoFile(filePath) ||
        isHtmlFile(filePath);
  }

  /// Get MIME type for document files (for Android)
  static String? getMimeType(String filePath) {
    final extension = getFileExtension(filePath);

    switch (extension) {
      case '.pdf':
        return 'application/pdf';
      case '.doc':
        return 'application/msword';
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case '.xls':
        return 'application/vnd.ms-excel';
      case '.xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case '.ppt':
        return 'application/vnd.ms-powerpoint';
      case '.pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.bmp':
        return 'image/bmp';
      case '.webp':
        return 'image/webp';
      case '.mp4':
        return 'video/mp4';
      case '.mov':
        return 'video/quicktime';
      case '.avi':
        return 'video/x-msvideo';
      case '.mkv':
        return 'video/x-matroska';
      default:
        return null;
    }
  }

  static String getFileType(String filePath) {
    if (isPdfFile(filePath)) {
      return 'pdf';
    } else if (isOfficeFile(filePath)) {
      return 'office';
    } else if (isZipFile(filePath)) {
      return 'zip';
    } else if (isImageFile(filePath)) {
      return 'image';
    } else if (isVideoFile(filePath)) {
      return 'video';
    } else if (isHtmlFile(filePath)) {
      return 'html';
    } else {
      return 'unsupported';
    }
  }

  /// Get appropriate icon for file type
  static IconData getFileIcon(String filePath) {
    final extension = getFileExtension(filePath);

    if (isPdfFile(filePath)) {
      return Icons.picture_as_pdf;
    } else if (isOfficeFile(filePath)) {
      switch (extension) {
        case '.doc':
        case '.docx':
          return Icons.description;
        case '.xls':
        case '.xlsx':
          return Icons.table_chart;
        case '.ppt':
        case '.pptx':
          return Icons.slideshow;
        default:
          return Icons.description;
      }
    } else if (isImageFile(filePath)) {
      return Icons.image;
    } else if (isVideoFile(filePath)) {
      return Icons.video_file;
    } else if (isZipFile(filePath)) {
      return Icons.archive;
    } else if (isHtmlFile(filePath)) {
      return Icons.web;
    } else {
      return Icons.insert_drive_file;
    }
  }

  /// Format file size in human readable format
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}
