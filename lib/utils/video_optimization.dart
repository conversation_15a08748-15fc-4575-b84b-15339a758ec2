import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

/// 视频播放优化工具类
/// 专门处理WebView中HTML5视频播放的性能优化
class VideoOptimization {
  /// 为WebView注入视频优化脚本
  static Future<void> injectVideoOptimizationScript(
    InAppWebViewController controller,
  ) async {
    const script = '''
      // 视频播放优化脚本
      (function() {
        console.log('Video optimization script loading...');

        // 全局视频优化配置
        const VIDEO_CONFIG = {
          preload: 'metadata', // 预加载元数据
          playsinline: true,   // 内联播放
          muted: false,        // 不静音（根据需要调整）
          controls: true,      // 显示控制条
          disablePictureInPicture: false, // 允许画中画
          crossorigin: 'anonymous' // 跨域设置
        };

        // 优化单个视频元素
        function optimizeVideoElement(video) {
          try {
            // 设置基本属性
            Object.keys(VIDEO_CONFIG).forEach(function(key) {
              if (key === 'disablePictureInPicture') {
                video.disablePictureInPicture = VIDEO_CONFIG[key];
              } else {
                video.setAttribute(key, VIDEO_CONFIG[key]);
              }
            });

            // 设置WebKit特定属性
            video.setAttribute('webkit-playsinline', 'true');
            video.setAttribute('x5-video-player-type', 'h5');
            video.setAttribute('x5-video-player-fullscreen', 'true');

            // 优化视频样式以提高渲染性能
            const style = video.style;
            style.webkitTransform = 'translateZ(0)';
            style.webkitBackfaceVisibility = 'hidden';
            style.webkitPerspective = '1000px';
            style.willChange = 'transform';

            // 设置视频容器优化
            const parent = video.parentElement;
            if (parent) {
              parent.style.webkitTransform = 'translateZ(0)';
              parent.style.webkitBackfaceVisibility = 'hidden';
            }

            // 添加性能监控事件
            addVideoPerformanceListeners(video);

            console.log('Video element optimized:', video.src || video.currentSrc);
            if (window.debugLog) {
              window.debugLog('Video optimized: ' + (video.src || video.currentSrc || 'unknown'));
            }

          } catch (error) {
            console.error('Error optimizing video:', error);
            if (window.debugLog) {
              window.debugLog('Video optimization error: ' + error.message);
            }
          }
        }

        // 添加视频性能监控
        function addVideoPerformanceListeners(video) {
          const events = [
            'loadstart', 'loadedmetadata', 'loadeddata', 'canplay',
            'canplaythrough', 'playing', 'waiting', 'seeking',
            'seeked', 'ended', 'error', 'stalled', 'suspend'
          ];

          events.forEach(function(eventName) {
            video.addEventListener(eventName, function(e) {
              const timestamp = Date.now();
              const message = 'Video event: ' + eventName + ' at ' + timestamp;
              console.log(message);

              // 特殊处理某些关键事件
              if (eventName === 'error') {
                console.error('Video error details:', e);
                if (window.debugLog) {
                  window.debugLog('Video error: ' + (e.message || 'Unknown error'));
                }
              } else if (eventName === 'waiting') {
                console.warn('Video buffering...');
                if (window.debugLog) {
                  window.debugLog('Video buffering');
                }
              } else if (eventName === 'canplaythrough') {
                console.log('Video ready for smooth playback');
                if (window.debugLog) {
                  window.debugLog('Video ready for playback');
                }
              }
            });
          });
        }

        // 批量优化所有视频
        function optimizeAllVideos() {
          const videos = document.querySelectorAll('video');
          let optimizedCount = 0;

          videos.forEach(function(video) {
            optimizeVideoElement(video);
            optimizedCount++;
          });

          console.log('Optimized ' + optimizedCount + ' video elements');
          if (window.debugLog) {
            window.debugLog('Optimized ' + optimizedCount + ' videos');
          }

          return optimizedCount;
        }

        // 监听动态添加的视频元素
        function setupVideoMutationObserver() {
          if (!window.MutationObserver) {
            console.warn('MutationObserver not supported');
            return;
          }

          const observer = new MutationObserver(function(mutations) {
            let hasNewVideos = false;

            mutations.forEach(function(mutation) {
              mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1) { // Element node
                  if (node.tagName === 'VIDEO') {
                    optimizeVideoElement(node);
                    hasNewVideos = true;
                  } else if (node.querySelectorAll) {
                    const videos = node.querySelectorAll('video');
                    if (videos.length > 0) {
                      videos.forEach(optimizeVideoElement);
                      hasNewVideos = true;
                    }
                  }
                }
              });
            });

            if (hasNewVideos) {
              console.log('New videos detected and optimized');
            }
          });

          observer.observe(document.body, {
            childList: true,
            subtree: true
          });

          console.log('Video mutation observer setup complete');
        }

        // 强制刷新视频渲染
        function forceVideoRefresh() {
          const videos = document.querySelectorAll('video');
          videos.forEach(function(video) {
            const display = video.style.display;
            video.style.display = 'none';
            video.offsetHeight; // 触发重排
            video.style.display = display || '';
          });

          console.log('Forced video refresh for ' + videos.length + ' videos');
        }

        // 暴露全局方法供调试使用
        window.videoOptimization = {
          optimizeAll: optimizeAllVideos,
          forceRefresh: forceVideoRefresh,
          getVideoCount: function() {
            return document.querySelectorAll('video').length;
          }
        };

        // 初始化优化
        function initialize() {
          console.log('Initializing video optimization...');

          // 立即优化现有视频
          optimizeAllVideos();

          // 设置动态监听
          setupVideoMutationObserver();

          // 页面完全加载后再次优化
          if (document.readyState === 'complete') {
            setTimeout(optimizeAllVideos, 100);
          } else {
            window.addEventListener('load', function() {
              setTimeout(optimizeAllVideos, 100);
            });
          }

          console.log('Video optimization initialization complete');
        }

        // 启动初始化
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', initialize);
        } else {
          initialize();
        }

      })();
    ''';

    try {
      await controller.evaluateJavascript(source: script);
      debugPrint(
        'VideoOptimization: Video optimization script injected successfully',
      );
    } catch (e) {
      debugPrint(
        'VideoOptimization: Failed to inject video optimization script: $e',
      );
    }
  }

  /// 检查并修复视频播放问题
  static Future<void> checkAndFixVideoIssues(
    InAppWebViewController controller,
  ) async {
    try {
      // 检查视频数量
      final videoCount =
          await controller.evaluateJavascript(
                source: 'document.querySelectorAll("video").length',
              )
              as int? ??
          0;

      if (videoCount > 0) {
        debugPrint(
          'VideoOptimization: Found $videoCount videos, applying fixes',
        );

        // 强制刷新视频渲染
        await controller.evaluateJavascript(
          source: '''
            if (window.videoOptimization) {
              window.videoOptimization.forceRefresh();
            }
          ''',
        );

        // 重新优化所有视频
        await controller.evaluateJavascript(
          source: '''
            if (window.videoOptimization) {
              window.videoOptimization.optimizeAll();
            }
          ''',
        );
      }
    } catch (e) {
      debugPrint('VideoOptimization: Error checking video issues: $e');
    }
  }

  /// 获取视频播放状态信息
  static Future<Map<String, dynamic>> getVideoPlaybackInfo(
    InAppWebViewController controller,
  ) async {
    try {
      final result = await controller.evaluateJavascript(
        source: '''
          (function() {
            const videos = document.querySelectorAll('video');
            const info = {
              totalVideos: videos.length,
              playingVideos: 0,
              pausedVideos: 0,
              errorVideos: 0,
              loadingVideos: 0
            };

            videos.forEach(function(video) {
              if (video.error) {
                info.errorVideos++;
              } else if (video.readyState < 3) {
                info.loadingVideos++;
              } else if (!video.paused) {
                info.playingVideos++;
              } else {
                info.pausedVideos++;
              }
            });

            return info;
          })();
        ''',
      );

      if (result is Map) {
        return Map<String, dynamic>.from(result);
      }
    } catch (e) {
      debugPrint('VideoOptimization: Error getting video info: $e');
    }

    return {
      'totalVideos': 0,
      'playingVideos': 0,
      'pausedVideos': 0,
      'errorVideos': 0,
      'loadingVideos': 0,
    };
  }

  /// 应用Android特定的视频优化
  static Map<String, dynamic> getAndroidVideoSettings() {
    if (!Platform.isAndroid) return {};

    return {
      // 启用硬件加速视频解码
      'hardwareAcceleration': true,

      // 使用Virtual Display模式以获得更好的视频性能
      'useHybridComposition': false,

      // 允许媒体播放不需要用户手势
      'mediaPlaybackRequiresUserGesture': false,

      // 混合内容模式
      'mixedContentMode': MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
    };
  }

  /// 应用iOS特定的视频优化
  static Map<String, dynamic> getIOSVideoSettings() {
    if (!Platform.isIOS) return {};

    return {
      // 允许内联媒体播放
      'allowsInlineMediaPlaybook': true,

      // 允许画中画
      'allowsPictureInPictureMediaPlayback': true,

      // 不需要用户手势即可播放媒体
      'mediaPlaybackRequiresUserGesture': false,
    };
  }
}
