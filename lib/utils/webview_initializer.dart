import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'webview_compatibility.dart';

/// WebView初始化辅助类
/// 专门处理WebView的初始化和错误恢复
class WebViewInitializer {
  static const int _maxInitRetries = 3;
  static const Duration _initDelay = Duration(milliseconds: 200);

  /// 安全初始化WebView
  static Future<bool> safeInitialize(
    InAppWebViewController controller,
    File htmlFile, {
    int retryCount = 0,
  }) async {
    try {
      // 等待WebView完全初始化
      await Future.delayed(_initDelay);

      // 检查文件是否存在且可读
      if (!await _validateFile(htmlFile)) {
        debugPrint('WebViewInitializer: File validation failed');
        return false;
      }

      // 注入基础JavaScript用于调试和兼容性
      await _injectCompatibilityScript(controller);

      // // 注入激进的视频优化脚本
      // await AggressiveVideoOptimization.injectAggressiveOptimization(
      //   controller,
      // );

      // 设置用户代理以提高兼容性
      await _setCompatibleUserAgent(controller);

      // 加载文件
      final uri = WebViewCompatibility.getSafeFileUri(htmlFile);
      await controller.loadUrl(urlRequest: URLRequest(url: uri));

      debugPrint('WebViewInitializer: Successfully initialized WebView');
      return true;
    } catch (e) {
      debugPrint('WebViewInitializer: Initialization failed: $e');

      // 如果初始化失败且还有重试次数，则重试
      if (retryCount < _maxInitRetries) {
        debugPrint(
          'WebViewInitializer: Retrying initialization (${retryCount + 1}/$_maxInitRetries)',
        );
        await Future.delayed(Duration(milliseconds: 500 * (retryCount + 1)));
        return await safeInitialize(
          controller,
          htmlFile,
          retryCount: retryCount + 1,
        );
      }

      return false;
    }
  }

  /// 验证文件是否可以安全加载
  static Future<bool> _validateFile(File file) async {
    try {
      if (!await file.exists()) {
        debugPrint('WebViewInitializer: File does not exist: ${file.path}');
        return false;
      }

      final stat = await file.stat();
      if (stat.size == 0) {
        debugPrint('WebViewInitializer: File is empty: ${file.path}');
        return false;
      }

      // 尝试读取文件前几个字节
      final bytes = await file.openRead(0, 100).toList();
      if (bytes.isEmpty) {
        debugPrint('WebViewInitializer: Cannot read file: ${file.path}');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('WebViewInitializer: File validation error: $e');
      return false;
    }
  }

  /// 注入兼容性JavaScript代码
  static Future<void> _injectCompatibilityScript(
    InAppWebViewController controller,
  ) async {
    const script = '''
      // WebView兼容性脚本
      (function() {
        // 防止某些错误导致页面崩溃
        window.addEventListener('error', function(e) {
          console.log('JS Error caught:', e.message);
          if (window.debugLog) {
            window.debugLog('JS Error: ' + e.message);
          }
        });

        // 确保DOM完全加载
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM fully loaded');
            if (window.debugLog) {
              window.debugLog('DOM fully loaded');
            }
            // DOM加载完成后优化视频
            optimizeVideos();
          });
        }

        // 视频播放优化函数
        function optimizeVideos() {
          const videos = document.querySelectorAll('video');
          videos.forEach(function(video) {
            // 设置视频属性以优化播放
            video.setAttribute('playsinline', 'true');
            video.setAttribute('webkit-playsinline', 'true');
            video.setAttribute('preload', 'metadata');

            // 禁用画中画（可能导致性能问题）
            if (video.disablePictureInPicture !== undefined) {
              video.disablePictureInPicture = false;
            }

            // 监听视频事件
            video.addEventListener('loadstart', function() {
              console.log('Video loading started');
              if (window.debugLog) {
                window.debugLog('Video loading started: ' + video.src);
              }
            });

            video.addEventListener('canplay', function() {
              console.log('Video can start playing');
              if (window.debugLog) {
                window.debugLog('Video can play: ' + video.src);
              }
            });

            video.addEventListener('error', function(e) {
              console.log('Video error:', e);
              if (window.debugLog) {
                window.debugLog('Video error: ' + e.message);
              }
            });

            // 优化视频容器样式
            video.style.webkitTransform = 'translateZ(0)';
            video.style.webkitBackfaceVisibility = 'hidden';
          });

          console.log('Optimized ' + videos.length + ' videos');
          if (window.debugLog) {
            window.debugLog('Optimized ' + videos.length + ' videos');
          }
        }

        // 修复某些CSS渲染问题
        function fixRendering() {
          document.body.style.webkitTransform = 'translateZ(0)';
          document.body.style.webkitBackfaceVisibility = 'hidden';
          document.body.style.webkitPerspective = '1000';
        }

        // 页面加载完成后执行修复
        if (document.readyState === 'complete') {
          fixRendering();
          optimizeVideos();
        } else {
          window.addEventListener('load', function() {
            fixRendering();
            optimizeVideos();
          });
        }

        // 监听动态添加的视频元素
        if (window.MutationObserver) {
          const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
              mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1) { // Element node
                  if (node.tagName === 'VIDEO') {
                    optimizeVideos();
                  } else if (node.querySelectorAll) {
                    const videos = node.querySelectorAll('video');
                    if (videos.length > 0) {
                      optimizeVideos();
                    }
                  }
                }
              });
            });
          });

          observer.observe(document.body, {
            childList: true,
            subtree: true
          });
        }

        console.log('WebView compatibility script loaded');
      })();
    ''';

    try {
      await controller.evaluateJavascript(source: script);
      debugPrint('WebViewInitializer: Compatibility script injected');
    } catch (e) {
      debugPrint(
        'WebViewInitializer: Failed to inject compatibility script: $e',
      );
    }
  }

  /// 设置兼容的用户代理
  static Future<void> _setCompatibleUserAgent(
    InAppWebViewController controller,
  ) async {
    try {
      // 获取当前用户代理
      final userAgent =
          await controller.evaluateJavascript(source: 'navigator.userAgent')
              as String? ??
          '';

      // 为Android设备添加特殊标识
      if (Platform.isAndroid && !userAgent.contains('EsopClient')) {
        final newUserAgent = '$userAgent EsopClient/1.0';

        // 通过JavaScript设置用户代理
        await controller.evaluateJavascript(
          source:
              '''
            Object.defineProperty(navigator, 'userAgent', {
              get: function() { return '$newUserAgent'; }
            });
          ''',
        );

        debugPrint(
          'WebViewInitializer: Set compatible user agent: $newUserAgent',
        );
      }
    } catch (e) {
      debugPrint('WebViewInitializer: Failed to set user agent: $e');
    }
  }

  /// 执行WebView健康检查
  static Future<bool> performHealthCheck(
    InAppWebViewController controller,
  ) async {
    try {
      // 检查JavaScript是否可以执行
      final result = await controller.evaluateJavascript(
        source: 'document.readyState',
      );

      if (result == null) {
        debugPrint('WebViewInitializer: Health check failed - no response');
        return false;
      }

      debugPrint(
        'WebViewInitializer: Health check passed - readyState: $result',
      );
      return true;
    } catch (e) {
      debugPrint('WebViewInitializer: Health check failed: $e');
      return false;
    }
  }

  /// 强制刷新WebView渲染
  static Future<void> forceRefreshRendering(
    InAppWebViewController controller,
  ) async {
    try {
      await controller.evaluateJavascript(
        source: '''
        // 强制重新渲染
        document.body.style.display = 'none';
        document.body.offsetHeight; // 触发重排
        document.body.style.display = '';

        // 触发resize事件
        window.dispatchEvent(new Event('resize'));

        console.log('Forced rendering refresh');
      ''',
      );
      debugPrint('WebViewInitializer: Forced rendering refresh');
    } catch (e) {
      debugPrint('WebViewInitializer: Failed to force refresh rendering: $e');
    }
  }
}
