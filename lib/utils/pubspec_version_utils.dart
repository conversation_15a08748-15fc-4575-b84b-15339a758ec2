import '../services/pubspec_version_service.dart';

/// 基于 pubspec.yaml 的版本号工具类
/// 提供便捷的版本号操作方法
class PubspecVersionUtils {
  static final PubspecVersionService _versionService = PubspecVersionService();

  /// 获取完整版本号（包含构建号）
  /// 例如：1.0.0+1
  static Future<String> getAppVersion() async {
    return await _versionService.getAppVersion();
  }

  /// 获取版本名称（不包含构建号）
  /// 例如：1.0.0
  static Future<String> getVersionName() async {
    return await _versionService.getVersionName();
  }

  /// 获取构建号
  /// 例如：1
  static Future<String> getBuildNumber() async {
    return await _versionService.getBuildNumber();
  }

  /// 获取格式化的版本显示
  /// 例如：版本 1.0.0 (构建号: 1)
  static Future<String> getFormattedVersion() async {
    return await _versionService.getFormattedVersion();
  }

  /// 获取完整版本信息
  static Future<Map<String, dynamic>> getVersionInfo() async {
    return await _versionService.getVersionInfo();
  }

  /// 获取API提交用的版本信息
  static Future<Map<String, String>> getApiVersionInfo() async {
    return await _versionService.getApiVersionInfo();
  }

  /// 记录版本更新时间
  static Future<void> recordUpdateTime() async {
    await _versionService.recordUpdateTime();
  }
}
