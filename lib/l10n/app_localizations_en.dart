// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Esop Client';

  @override
  String get settings => 'Settings';

  @override
  String get language => 'Language';

  @override
  String get systemLanguage => 'System Language';

  @override
  String get serverAddress => 'Server Address';

  @override
  String get serverPort => 'Server Port';

  @override
  String get mqttPort => 'MQTT Port';

  @override
  String get deviceAlias => 'Device Alias';

  @override
  String get groupName => 'Group Name';

  @override
  String get registrationCode => 'Registration Code';

  @override
  String get save => 'Save';

  @override
  String get reset => 'Reset';

  @override
  String get scanToConfigure => 'Scan QR Code to Configure';

  @override
  String get scanQRCode => 'Scan QR Code';

  @override
  String get manualConfiguration => 'Manual Configuration';

  @override
  String mqttStatus(Object status) {
    return 'MQTT Status: $status';
  }

  @override
  String get connected => 'Connected';

  @override
  String get disconnected => 'Disconnected';

  @override
  String get connecting => 'Connecting';

  @override
  String get error => 'Error';

  @override
  String get connect => 'Connect';

  @override
  String get reconnecting => 'Reconnecting...';

  @override
  String get enterServerAddress => 'Please enter server address';

  @override
  String get enterDeviceAlias => 'Please enter device alias';

  @override
  String get searchingForServer => 'Searching for server...';

  @override
  String get discoveryFailed => 'Server discovery failed';

  @override
  String get settingsSaved => 'Settings saved';

  @override
  String get registrationFailed => 'Registration failed';

  @override
  String get mqttServerAddress => 'MQTT Server Address';

  @override
  String get discovering => 'Discovering...';

  @override
  String get mqttServerAddressHint => 'e.g., *************';

  @override
  String get pleaseEnterMqttServerAddress => 'Please enter MQTT server address';

  @override
  String get apiServerPort => 'API Server Port';

  @override
  String get apiServerPortHint => 'e.g., 8080';

  @override
  String get mqttPortHint => 'e.g., 1883';

  @override
  String get groupNameHint => 'e.g., a-team';

  @override
  String get deviceAliasHint => 'e.g., display-01';

  @override
  String get registrationCodeHint => 'Auto-generated or manual entry';

  @override
  String get pleaseEnterRegistrationCode => 'Please enter registration code';

  @override
  String get loading => 'Loading...';

  @override
  String get macAddress => 'MAC Address';

  @override
  String get saveSettings => 'Save Settings';

  @override
  String get chinese => '中文';

  @override
  String get english => 'English';

  @override
  String get screenOrientation => 'Screen Orientation';

  @override
  String get landscapeDisplay => 'Landscape';

  @override
  String get portraitDisplay => 'Portrait';

  @override
  String get pleaseEnterMqttPort => 'Please enter MQTT port';

  @override
  String get portNumberMustBeBetween => 'Port number must be between 1-65535';

  @override
  String get pleaseEnterValidPort => 'Please enter a valid port number';
}
