{"appTitle": "Esop Client", "settings": "Settings", "language": "Language", "systemLanguage": "System Language", "serverAddress": "Server Address", "serverPort": "Server Port", "mqttPort": "MQTT Port", "deviceAlias": "<PERSON><PERSON>", "groupName": "Group Name", "registrationCode": "Registration Code", "save": "Save", "reset": "Reset", "scanToConfigure": "Scan QR Code to Configure", "scanQRCode": "Scan QR Code", "manualConfiguration": "Manual Configuration", "mqttStatus": "MQTT Status: {status}", "connected": "Connected", "disconnected": "Disconnected", "connecting": "Connecting", "error": "Error", "connect": "Connect", "reconnecting": "Reconnecting...", "enterServerAddress": "Please enter server address", "enterDeviceAlias": "Please enter device alias", "searchingForServer": "Searching for server...", "discoveryFailed": "Server discovery failed", "settingsSaved": "Setting<PERSON> saved", "registrationFailed": "Registration failed", "mqttServerAddress": "MQTT Server Address", "discovering": "Discovering...", "mqttServerAddressHint": "e.g., *************", "pleaseEnterMqttServerAddress": "Please enter MQTT server address", "apiServerPort": "API Server Port", "apiServerPortHint": "e.g., 8080", "mqttPortHint": "e.g., 1883", "groupNameHint": "e.g., a-team", "deviceAliasHint": "e.g., display-01", "registrationCodeHint": "Auto-generated or manual entry", "pleaseEnterRegistrationCode": "Please enter registration code", "loading": "Loading...", "macAddress": "MAC Address", "saveSettings": "Save Settings", "chinese": "中文", "english": "English", "screenOrientation": "Screen Orientation", "landscapeDisplay": "Landscape", "portraitDisplay": "Portrait", "pleaseEnterMqttPort": "Please enter MQTT port", "portNumberMustBeBetween": "Port number must be between 1-65535", "pleaseEnterValidPort": "Please enter a valid port number"}