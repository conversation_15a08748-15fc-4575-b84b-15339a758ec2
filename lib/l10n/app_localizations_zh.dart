// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => 'Esop客户端';

  @override
  String get settings => '设置';

  @override
  String get language => '语言';

  @override
  String get systemLanguage => '跟随系统';

  @override
  String get serverAddress => '服务器地址';

  @override
  String get serverPort => '服务器端口';

  @override
  String get mqttPort => 'MQTT端口';

  @override
  String get deviceAlias => '设备别名';

  @override
  String get groupName => '分组名称';

  @override
  String get registrationCode => '注册码';

  @override
  String get save => '保存';

  @override
  String get reset => '重置';

  @override
  String get scanToConfigure => '扫描二维码配置';

  @override
  String get scanQRCode => '扫描二维码';

  @override
  String get manualConfiguration => '手动配置';

  @override
  String mqttStatus(Object status) {
    return 'MQTT状态: $status';
  }

  @override
  String get connected => '已连接';

  @override
  String get disconnected => '已断开';

  @override
  String get connecting => '连接中';

  @override
  String get error => '错误';

  @override
  String get connect => '连接';

  @override
  String get reconnecting => '重新连接中...';

  @override
  String get enterServerAddress => '请输入服务器地址';

  @override
  String get enterDeviceAlias => '请输入设备别名';

  @override
  String get searchingForServer => '正在搜索服务器...';

  @override
  String get discoveryFailed => '服务器发现失败';

  @override
  String get settingsSaved => '设置已保存';

  @override
  String get registrationFailed => '注册失败';

  @override
  String get mqttServerAddress => 'MQTT服务器地址';

  @override
  String get discovering => '发现中...';

  @override
  String get mqttServerAddressHint => '例如: *************';

  @override
  String get pleaseEnterMqttServerAddress => '请输入MQTT服务器地址';

  @override
  String get apiServerPort => 'API服务器端口';

  @override
  String get apiServerPortHint => '例如: 8080';

  @override
  String get mqttPortHint => '例如: 1883';

  @override
  String get groupNameHint => '例如: a-team';

  @override
  String get deviceAliasHint => '例如: display-01';

  @override
  String get registrationCodeHint => '自动生成或手动输入';

  @override
  String get pleaseEnterRegistrationCode => '请输入注册码';

  @override
  String get loading => '加载中...';

  @override
  String get macAddress => 'MAC地址';

  @override
  String get saveSettings => '保存设置';

  @override
  String get chinese => '中文';

  @override
  String get english => 'English';

  @override
  String get screenOrientation => '屏幕方向';

  @override
  String get landscapeDisplay => '横屏显示';

  @override
  String get portraitDisplay => '竖屏显示';

  @override
  String get pleaseEnterMqttPort => '请输入MQTT端口';

  @override
  String get portNumberMustBeBetween => '端口号必须在1-65535之间';

  @override
  String get pleaseEnterValidPort => '请输入有效的端口号';
}
