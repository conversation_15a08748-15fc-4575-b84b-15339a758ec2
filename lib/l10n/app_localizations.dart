import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('zh'),
  ];

  /// No description provided for @appTitle.
  ///
  /// In en, this message translates to:
  /// **'Esop Client'**
  String get appTitle;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @systemLanguage.
  ///
  /// In en, this message translates to:
  /// **'System Language'**
  String get systemLanguage;

  /// No description provided for @serverAddress.
  ///
  /// In en, this message translates to:
  /// **'Server Address'**
  String get serverAddress;

  /// No description provided for @serverPort.
  ///
  /// In en, this message translates to:
  /// **'Server Port'**
  String get serverPort;

  /// No description provided for @mqttPort.
  ///
  /// In en, this message translates to:
  /// **'MQTT Port'**
  String get mqttPort;

  /// No description provided for @deviceAlias.
  ///
  /// In en, this message translates to:
  /// **'Device Alias'**
  String get deviceAlias;

  /// No description provided for @groupName.
  ///
  /// In en, this message translates to:
  /// **'Group Name'**
  String get groupName;

  /// No description provided for @registrationCode.
  ///
  /// In en, this message translates to:
  /// **'Registration Code'**
  String get registrationCode;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @reset.
  ///
  /// In en, this message translates to:
  /// **'Reset'**
  String get reset;

  /// No description provided for @scanToConfigure.
  ///
  /// In en, this message translates to:
  /// **'Scan QR Code to Configure'**
  String get scanToConfigure;

  /// No description provided for @scanQRCode.
  ///
  /// In en, this message translates to:
  /// **'Scan QR Code'**
  String get scanQRCode;

  /// No description provided for @manualConfiguration.
  ///
  /// In en, this message translates to:
  /// **'Manual Configuration'**
  String get manualConfiguration;

  /// No description provided for @mqttStatus.
  ///
  /// In en, this message translates to:
  /// **'MQTT Status: {status}'**
  String mqttStatus(Object status);

  /// No description provided for @connected.
  ///
  /// In en, this message translates to:
  /// **'Connected'**
  String get connected;

  /// No description provided for @disconnected.
  ///
  /// In en, this message translates to:
  /// **'Disconnected'**
  String get disconnected;

  /// No description provided for @connecting.
  ///
  /// In en, this message translates to:
  /// **'Connecting'**
  String get connecting;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @connect.
  ///
  /// In en, this message translates to:
  /// **'Connect'**
  String get connect;

  /// No description provided for @reconnecting.
  ///
  /// In en, this message translates to:
  /// **'Reconnecting...'**
  String get reconnecting;

  /// No description provided for @enterServerAddress.
  ///
  /// In en, this message translates to:
  /// **'Please enter server address'**
  String get enterServerAddress;

  /// No description provided for @enterDeviceAlias.
  ///
  /// In en, this message translates to:
  /// **'Please enter device alias'**
  String get enterDeviceAlias;

  /// No description provided for @searchingForServer.
  ///
  /// In en, this message translates to:
  /// **'Searching for server...'**
  String get searchingForServer;

  /// No description provided for @discoveryFailed.
  ///
  /// In en, this message translates to:
  /// **'Server discovery failed'**
  String get discoveryFailed;

  /// No description provided for @settingsSaved.
  ///
  /// In en, this message translates to:
  /// **'Settings saved'**
  String get settingsSaved;

  /// No description provided for @registrationFailed.
  ///
  /// In en, this message translates to:
  /// **'Registration failed'**
  String get registrationFailed;

  /// No description provided for @mqttServerAddress.
  ///
  /// In en, this message translates to:
  /// **'MQTT Server Address'**
  String get mqttServerAddress;

  /// No description provided for @discovering.
  ///
  /// In en, this message translates to:
  /// **'Discovering...'**
  String get discovering;

  /// No description provided for @mqttServerAddressHint.
  ///
  /// In en, this message translates to:
  /// **'e.g., *************'**
  String get mqttServerAddressHint;

  /// No description provided for @pleaseEnterMqttServerAddress.
  ///
  /// In en, this message translates to:
  /// **'Please enter MQTT server address'**
  String get pleaseEnterMqttServerAddress;

  /// No description provided for @apiServerPort.
  ///
  /// In en, this message translates to:
  /// **'API Server Port'**
  String get apiServerPort;

  /// No description provided for @apiServerPortHint.
  ///
  /// In en, this message translates to:
  /// **'e.g., 8080'**
  String get apiServerPortHint;

  /// No description provided for @mqttPortHint.
  ///
  /// In en, this message translates to:
  /// **'e.g., 1883'**
  String get mqttPortHint;

  /// No description provided for @groupNameHint.
  ///
  /// In en, this message translates to:
  /// **'e.g., a-team'**
  String get groupNameHint;

  /// No description provided for @deviceAliasHint.
  ///
  /// In en, this message translates to:
  /// **'e.g., display-01'**
  String get deviceAliasHint;

  /// No description provided for @registrationCodeHint.
  ///
  /// In en, this message translates to:
  /// **'Auto-generated or manual entry'**
  String get registrationCodeHint;

  /// No description provided for @pleaseEnterRegistrationCode.
  ///
  /// In en, this message translates to:
  /// **'Please enter registration code'**
  String get pleaseEnterRegistrationCode;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// No description provided for @macAddress.
  ///
  /// In en, this message translates to:
  /// **'MAC Address'**
  String get macAddress;

  /// No description provided for @saveSettings.
  ///
  /// In en, this message translates to:
  /// **'Save Settings'**
  String get saveSettings;

  /// No description provided for @chinese.
  ///
  /// In en, this message translates to:
  /// **'中文'**
  String get chinese;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @screenOrientation.
  ///
  /// In en, this message translates to:
  /// **'Screen Orientation'**
  String get screenOrientation;

  /// No description provided for @landscapeDisplay.
  ///
  /// In en, this message translates to:
  /// **'Landscape'**
  String get landscapeDisplay;

  /// No description provided for @portraitDisplay.
  ///
  /// In en, this message translates to:
  /// **'Portrait'**
  String get portraitDisplay;

  /// No description provided for @pleaseEnterMqttPort.
  ///
  /// In en, this message translates to:
  /// **'Please enter MQTT port'**
  String get pleaseEnterMqttPort;

  /// No description provided for @portNumberMustBeBetween.
  ///
  /// In en, this message translates to:
  /// **'Port number must be between 1-65535'**
  String get portNumberMustBeBetween;

  /// No description provided for @pleaseEnterValidPort.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid port number'**
  String get pleaseEnterValidPort;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
