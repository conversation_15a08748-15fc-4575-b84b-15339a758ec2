import 'mqtt_file_item_model.dart';

class MqttMessageModel {
  int? type; // 1: partial, 2: all, 3: rule-based
  String? groupName;
  List<MqttFileItemModel>? fileList;
  String? equipmentAliasName;
  String? command;
  String? version;
  String? otaUrl;
  String? uploadUrl;
  String? taskId;
  int? volume;
  bool? mute;

  // For type=7 schedule command: keep raw fields for robust parsing
  dynamic powerOnTimeRaw;
  dynamic powerOffTimeRaw;
  dynamic weekdaysRaw;

  MqttMessageModel({
    this.type,
    this.groupName,
    this.fileList,
    this.equipmentAliasName,
    this.command,
    this.version,
    this.otaUrl,
    this.uploadUrl,
    this.taskId,
    this.volume,
    this.mute,
    this.powerOnTimeRaw,
    this.powerOffTimeRaw,
    this.weekdaysRaw,
  });

  MqttMessageModel.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    groupName = json['group_name'];
    equipmentAliasName = json['equipment_alias_name'];
    command = json['command'];
    version = json['version'];
    otaUrl = json['ota_url'];
    uploadUrl = json['upload_url'];
    taskId = json['task_id'];
    volume = json['volume'];
    mute = json['mute'];

    // store raw for robust parsing in service layer
    powerOnTimeRaw = json['powerOnTime'] ?? json['power_on_time'];
    powerOffTimeRaw = json['powerOffTime'] ?? json['power_off_time'];
    weekdaysRaw = json['weekdays'];

    if (json['list'] != null) {
      fileList = <MqttFileItemModel>[];
      json['list'].forEach((v) {
        fileList!.add(MqttFileItemModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    data['group_name'] = groupName;
    data['equipment_alias_name'] = equipmentAliasName;
    data['command'] = command;
    data['version'] = version;
    data['ota_url'] = otaUrl;
    data['upload_url'] = uploadUrl;
    data['task_id'] = taskId;
    data['volume'] = volume;
    data['mute'] = mute;
    if (powerOnTimeRaw != null) data['powerOnTime'] = powerOnTimeRaw;
    if (powerOffTimeRaw != null) data['powerOffTime'] = powerOffTimeRaw;
    if (weekdaysRaw != null) data['weekdays'] = weekdaysRaw;

    if (fileList != null) {
      data['list'] = fileList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
