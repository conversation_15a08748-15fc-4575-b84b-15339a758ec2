/// MQTT连接状态枚举
/// 提供给界面层判断连接状态使用
enum MqttConnectionState {
  /// 已断开连接
  disconnected,

  /// 正在连接中
  connecting,

  /// 已连接
  connected,

  /// 正在断开连接
  disconnecting,

  /// 连接错误（包括认证失败、网络错误等）
  error,
}

/// MQTT连接状态扩展方法
extension MqttConnectionStateExtension on MqttConnectionState {
  /// 是否可以尝试连接
  bool get canConnect {
    return this == MqttConnectionState.disconnected ||
        this == MqttConnectionState.error;
  }

  /// 是否已连接
  bool get isConnected {
    return this == MqttConnectionState.connected;
  }

  /// 是否正在连接过程中
  bool get isConnecting {
    return this == MqttConnectionState.connecting ||
        this == MqttConnectionState.disconnecting;
  }

  /// 是否处于错误状态
  bool get isError {
    return this == MqttConnectionState.error;
  }

  /// 获取状态描述文本
  String get description {
    switch (this) {
      case MqttConnectionState.disconnected:
        return '已断开';
      case MqttConnectionState.connecting:
        return '连接中';
      case MqttConnectionState.connected:
        return '已连接';
      case MqttConnectionState.disconnecting:
        return '断开中';
      case MqttConnectionState.error:
        return '连接错误';
    }
  }
}
