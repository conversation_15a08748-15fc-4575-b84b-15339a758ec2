class SettingsModel {
  String? mqttServerAddress;
  String? mqttPort;
  String? mqttUsername; // Added for MQTT username
  String? mqttPassword; // Added for MQTT password
  String? serverPort;
  String? mqttTopic;
  String? groupName;
  String? deviceAlias;
  String? deviceId;
  String? macAddress;
  String? registrationCode;
  String? languageCode;
  String? screenOrientation;
  int? equipmentId;

  SettingsModel({
    this.mqttServerAddress = '',
    this.mqttPort = '1883',

    this.serverPort = '8567',
    this.mqttTopic = 'esopChannel',
    this.groupName,
    this.deviceAlias,
    this.deviceId,
    this.macAddress,
    this.equipmentId,
    this.registrationCode,
    this.languageCode = 'zh',
    this.screenOrientation = 'landscape',

    this.mqttUsername = 'fsmmufong', // Default username
    this.mqttPassword = 'fsmmufong', // Default password
  });

  SettingsModel.fromJson(Map<String, dynamic> json) {
    mqttServerAddress = json['mqttServerAddress'] ?? '';
    mqttPort = json['mqttPort'] ?? '1883';
    mqttUsername = json['mqttUsername'] ?? 'fsmmufong';
    mqttPassword = json['mqttPassword'] ?? 'fsmmufong';
    serverPort = json['serverPort'] ?? '8567';
    mqttTopic = json['mqttTopic'] ?? 'esopChannel';
    groupName = json['groupName'];
    deviceAlias = json['deviceAlias'];
    deviceId = json['deviceId'];
    equipmentId = json['equipmentId'];
    macAddress = json['macAddress'];
    registrationCode = json['registrationCode'];
    languageCode = json['languageCode'] ?? 'zh';
    screenOrientation = json['screenOrientation'] ?? 'landscape';
  }

  /// 生成新的MQTT topic格式: esop/${group_name}/${alias_name}
  String generateMqttTopic() {
    if (groupName != null &&
        groupName!.isNotEmpty &&
        deviceAlias != null &&
        deviceAlias!.isNotEmpty) {
      return 'esop/$groupName/$deviceAlias';
    }
    // 如果groupName或deviceAlias为空，返回默认topic
    return mqttTopic ?? 'esopChannel';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mqttServerAddress'] = mqttServerAddress;
    data['mqttPort'] = mqttPort;
    data['mqttUsername'] = mqttUsername;
    data['mqttPassword'] = mqttPassword;
    data['serverPort'] = serverPort;
    data['mqttTopic'] = mqttTopic;
    data['groupName'] = groupName;
    data['deviceAlias'] = deviceAlias;
    data['deviceId'] = deviceId;
    data['macAddress'] = macAddress;
    data['registrationCode'] = registrationCode;
    data['languageCode'] = languageCode;
    data['equipmentId'] = equipmentId;
    data['screenOrientation'] = screenOrientation;

    return data;
  }
}
