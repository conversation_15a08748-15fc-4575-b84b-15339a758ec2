class MaterialFileModel {
  final int id;
  final String name;
  final int type;
  final String contentType;
  final String path;
  final int sourceWidth;
  final int sourceHeight;
  final int size;
  final int createdAt;
  final int updatedAt;
  final int isDeleted;

  MaterialFileModel({
    required this.id,
    required this.name,
    required this.type,
    required this.contentType,
    required this.path,
    required this.sourceWidth,
    required this.sourceHeight,
    required this.size,
    required this.createdAt,
    required this.updatedAt,
    required this.isDeleted,
  });

  factory MaterialFileModel.fromJson(Map<String, dynamic> json) {
    return MaterialFileModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      type: json['type'] ?? 0,
      contentType: json['content_type'] ?? '',
      path: json['path'] ?? '',
      sourceWidth: json['source_width'] ?? 0,
      sourceHeight: json['source_height'] ?? 0,
      size: json['size'] ?? 0,
      createdAt: json['created_at'] ?? 0,
      updatedAt: json['updated_at'] ?? 0,
      isDeleted: json['is_deleted'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'content_type': contentType,
      'path': path,
      'source_width': sourceWidth,
      'source_height': sourceHeight,
      'size': size,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'is_deleted': isDeleted,
    };
  }

  // 获取文件大小的可读格式
  String get formattedSize {
    if (size < 1024) {
      return '${size}B';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)}KB';
    } else if (size < 1024 * 1024 * 1024) {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }

  // 获取文件类型图标
  String get fileTypeIcon {
    if (contentType.startsWith('video/')) {
      return '🎥';
    } else if (contentType.startsWith('image/')) {
      return '🖼️';
    } else if (contentType.startsWith('audio/')) {
      return '🎵';
    } else if (contentType.contains('pdf')) {
      return '📄';
    } else if (contentType.contains('word') ||
        contentType.contains('document')) {
      return '📝';
    } else if (contentType.contains('excel') ||
        contentType.contains('spreadsheet')) {
      return '📊';
    } else if (contentType.contains('powerpoint') ||
        contentType.contains('presentation')) {
      return '📽️';
    } else {
      return '📁';
    }
  }

  // 判断是否为视频文件
  bool get isVideo => contentType.startsWith('video/');

  // 判断是否为图片文件
  bool get isImage => contentType.startsWith('image/');

  // 判断是否为音频文件
  bool get isAudio => contentType.startsWith('audio/');

  // 判断是否为文档文件
  bool get isDocument =>
      contentType.contains('pdf') ||
      contentType.contains('word') ||
      contentType.contains('document') ||
      contentType.contains('excel') ||
      contentType.contains('spreadsheet') ||
      contentType.contains('powerpoint') ||
      contentType.contains('presentation');
}

class MaterialFileResponse {
  final int code;
  final String message;
  final List<MaterialFileModel> data;

  MaterialFileResponse({
    required this.code,
    required this.message,
    required this.data,
  });

  factory MaterialFileResponse.fromJson(Map<String, dynamic> json) {
    return MaterialFileResponse(
      code: json['code'] ?? -1,
      message: json['message'] ?? '',
      data:
          (json['data'] as List<dynamic>?)
              ?.map((item) => MaterialFileModel.fromJson(item))
              .toList() ??
          [],
    );
  }

  bool get isSuccess => code == 0;
}
