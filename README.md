# ESOP 客户端

## 项目简介

ESOP 客户端是基于 Flutter 跨平台开发的工业设备通信与文件管理应用，支持 Android、iOS、Windows、macOS、Linux 及 Web。核心功能包括 MQTT 消息通信、文件下载与解压、设备信息采集、WebView/PDF 预览、多语言切换与自定义设置。

---

## 目录结构

- `lib/`：核心业务代码
  - `main.dart`：应用入口，初始化 Provider、主题、本地化
  - `models/`：数据模型（如 `SettingsModel`）
  - `providers/`：状态管理（如 `SettingsProvider`、`MqttProvider`）
  - `services/`：业务服务（如 `MqttService`、`FileService`）
  - `screens/`：页面（如 `HomeScreen`、`SettingsScreen`）
  - `utils/`：工具类
  - `l10n/`：国际化资源
- `android/`、`ios/`、`macos/`、`windows/`、`linux/`、`web/`：各平台适配
- `assets/`：静态资源
- `test/`、`integration_test/`：测试代码
- `docs/`：文档

---

## 安装步骤

1. **环境准备**
   - 安装 [Flutter SDK](https://flutter.dev/docs/get-started/install)（建议 3.8.0 及以上）
   - 配置好各平台开发环境（如 Android Studio、Xcode）

2. **获取依赖**
   ```bash
   flutter pub get
   ```

3. **运行项目**
   - Android/iOS/macOS/Windows/Linux：
     ```bash
     flutter run
     ```
   - Web：
     ```bash
     flutter run -d chrome
     ```

4. **打包发布**
   - 参考 Flutter 官方文档进行各平台打包。

---

## 功能介绍

- **MQTT 通信**：支持配置服务器地址、端口、主题，自动重连，实时消息收发。
- **文件管理**：支持文件下载、解压、历史记录、重复文件检测。
- **设备信息采集**：自动获取设备唯一标识、MAC 地址等。
- **WebView/PDF 预览**：内置网页浏览与 PDF 文件预览。
- **多语言支持**：中英文切换，支持自定义本地化。
- **设置管理**：支持 MQTT、API、分组、别名、语言、屏幕方向等参数配置，持久化存储。

---

## 使用示例

1. **首次启动**
   - 应用自动进入全屏模式，加载本地设置。
   - 若未配置 MQTT/服务器参数，需进入设置页填写。

2. **配置参数**
   - 双指双击主界面快速进入设置页。
   - 填写服务器地址、端口、主题、分组、别名等，保存后自动重连。

3. **文件操作**
   - 支持通过 MQTT 消息触发文件下载与解压，自动检测重复文件。

4. **切换语言/屏幕方向**
   - 设置页可切换中英文及横竖屏，切换后立即生效。

---

## 常见问题解答（FAQ）

- **Q: MQTT 连接不上怎么办？**
  - 检查服务器地址、端口、主题是否正确，确保网络畅通。
  - 查看设置页 MQTT 状态提示，必要时重启应用。

- **Q: 文件下载失败或打不开？**
  - 检查网络与服务器可用性，确认文件格式支持。
  - 清理缓存后重试。

- **Q: 如何切换语言？**
  - 设置页选择语言后保存即可，界面会自动刷新。

- **Q: 如何适配不同平台？**
  - 请确保已为目标平台配置好 Flutter 环境及原生权限（如网络、存储、后台启动等）。

---

## 贡献指南

1. Fork 本仓库并新建分支
2. 提交代码前请确保通过 `flutter analyze` 和相关测试
3. 提交 PR 时请详细描述修改内容
4. 欢迎补充文档、完善国际化、优化体验

---

## 依赖与配置说明

- 主要依赖见 [`pubspec.yaml`](pubspec.yaml:1)，如 `mqtt_client`、`dio`、`device_info_plus`、`shared_preferences`、`flutter_inappwebview`、`intl` 等
- 国际化配置见 `l10n.yaml`，如需新增语言请参考 `lib/l10n/`
- 各平台原生权限需在 `AndroidManifest.xml`、`Info.plist` 等文件中配置

---

## 注意事项

- 建议使用稳定版 Flutter SDK，避免 beta/dev 版本兼容性问题
- 生产环境请妥善配置服务器与安全策略
- 如遇问题请先查阅 FAQ 或提交 Issue

---
## mqtt信息格式事例：


### 指定设备接收指令
```
{
  "type": 1, // 1: 指定设备接收指令，2: 全部接收指令，3: 规则接收指令，4: 设备重启/关机指令，5: OTA升级指令
  "group_name": "scx2", //指定设备组名 *表示所有分组
  "list": [
    {
      "download_file": "assets/zippack/123.zip", //下载文件地址
      "file_type": "pdf", // 通过判断file_type 选择使用哪种方式打开 file_type格式有 pdf、image、ppt、excel、word
      "equipment_alias_name": "1号",  //指定设备别名（匹配才执行下载文件）
      "hash": "0603b2dfae928f2787e49db304c1d52aadc01415b06e4c111e3758ef99addc5e" // 文件hash值，用于判断是否需要重新下载
    }
  ]
}
```

**文件Hash功能说明：**
- `hash`字段用于判断相同`download_file`地址的文件内容是否发生变化
- 如果hash值与本地存储的不同，则重新下载文件
- 如果hash值相同且本地文件存在，则直接使用现有文件
- 解压文件夹使用格式：`文件名_hash值`（如：ww2_0603b2dfae928f2787e49db304c1d52aadc01415b06e4c111e3758ef99addc5e）

**🔧 重要修复：同名文件冲突问题**
- ✅ 修复了当下载目录存在同名旧文件时，新文件无法正确下载的问题
- ✅ 现在系统会智能比较hash值，自动删除过期文件并下载新版本
- ✅ 确保用户始终看到最新的文件内容

### 全部接收指令
```
{
  "type": 2,
  "list": [
    {
      "download_file": "assets/zippack/123.zip",
      "file_type": "pdf",
      "equipment_alias_name": "1号",
      "hash": "0603b2dfae928f2787e49db304c1d52aadc01415b06e4c111e3758ef99addc5e"
    }
  ]
}
```

### 规则接收指令
```
{
  "type": 3,
  "group_name": "scx2", //指定设备组名 *表示所有分组
  "list": [
    {
      "download_file": "assets/zippack/123.zip",
      "file_type": "pdf",
      "equipment_alias_name": "1号",
      "hash": "0603b2dfae928f2787e49db304c1d52aadc01415b06e4c111e3758ef99addc5e"
    }
  ]
}
```

### 下发设备重启/关机指令
```
{
  "type": 4, //设备控制指令
  "group_name": "scx2",  //指定设备组名 *表示所有分组
  "equipment_alias_name": "1号", //指定设备别名（匹配才执行）
  "command":"reboot" // reboot 重启，shutdown 关机
}
```

### 下发ota升级指令
```
{
  "type": 5, // OTA升级指令
  "group_name": "scx2",  //指定设备组名 *表示所有分组
  "equipment_alias_name": "1号",  //指定设备别名（匹配才执行）
  "version":"1.0.0",  //匹配版本号
  "ota_url":"http://*************:8080/ota/1.0.0.apk" //ota升级地址
}
```
### 下发调节音量指令
```
{
  "type": 6, // 调节音量指令
  "group_name": "scx2",  //指定设备组名 *表示所有分组
  "equipment_alias_name": "1号",  //指定设备别名 （匹配才执行） ，* 表示分组下的所有设备
  "volume": 50, //音量
  "mute": false //是否静音
}
```


### 下发定时开关机指令
```
{
  "type": 7, // 定时开关机指令
  "group_name": "scx2",  //指定设备组名 *表示所有分组
  "equipment_alias_name": "1号",  //指定设备别名 （匹配才执行） ，* 表示分组下的所有设备
  "powerOnTime": "{8,30}", //开机时间，时分
  "powerOffTime": {18,30}, //关机时间，时分
  "weekdays":{1,1,1,1,1,0,1} // 周一到周日的工作状态


}
```


### 清除定时开关机指令
```
{
  "type": 8, // 清除定时开关机指令
  "group_name": "scx2",  //指定设备组名 *表示所有分组
  "equipment_alias_name": "1号",  //指定设备别名 （匹配才执行） ，* 表示分组下的所有设备


}
```

---

## License

MIT