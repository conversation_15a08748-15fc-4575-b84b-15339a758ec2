import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:media_kit/media_kit.dart';

import 'package:esop_client/screens/native_render_screen.dart';
import 'package:esop_client/models/template_model.dart';

void main() {
  group('MediaKit Video Player Error Handling Tests', () {
    testWidgets('MediaKitVideoPlayer should handle initialization safely', (
      WidgetTester tester,
    ) async {
      // 创建一个测试媒体文件路径
      const testMediaPath = '/test/video.mp4';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MediaKitVideoPlayer(media: Media(testMediaPath)),
          ),
        ),
      );

      // 验证组件创建成功
      expect(find.byType(MediaKitVideoPlayer), findsOneWidget);

      // 等待一帧确保初始化完成
      await tester.pump();

      // 验证没有异常抛出
      expect(tester.takeException(), isNull);
    });

    testWidgets('MediaKitVideoCarousel should handle empty files list', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MediaKitVideoCarousel(
              files: [],
              buildMedia: (path) => Media(path),
            ),
          ),
        ),
      );

      // 验证组件创建成功
      expect(find.byType(MediaKitVideoCarousel), findsOneWidget);

      // 等待一帧确保初始化完成
      await tester.pump();

      // 验证没有异常抛出
      expect(tester.takeException(), isNull);
    });

    testWidgets('MediaKitVideoCarousel should handle video files', (
      WidgetTester tester,
    ) async {
      // 创建测试文件列表
      final testFiles = [
        MultiFile(
          path: '/test/video1.mp4',
          type: 2, // 视频类型
          intervalTime: 5,
        ),
        MultiFile(
          path: '/test/video2.mp4',
          type: 2, // 视频类型
          intervalTime: 3,
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MediaKitVideoCarousel(
              files: testFiles,
              buildMedia: (path) => Media(path),
            ),
          ),
        ),
      );

      // 验证组件创建成功
      expect(find.byType(MediaKitVideoCarousel), findsOneWidget);

      // 等待一帧确保初始化完成
      await tester.pump();

      // 验证没有异常抛出
      expect(tester.takeException(), isNull);
    });

    testWidgets('MediaKitVideoPlayer should handle dispose safely', (
      WidgetTester tester,
    ) async {
      const testMediaPath = '/test/video.mp4';

      // 创建组件
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MediaKitVideoPlayer(media: Media(testMediaPath)),
          ),
        ),
      );

      expect(find.byType(MediaKitVideoPlayer), findsOneWidget);
      await tester.pump();

      // 销毁组件
      await tester.pumpWidget(
        const MaterialApp(home: Scaffold(body: SizedBox())),
      );

      // 等待dispose完成
      await tester.pump();

      // 验证没有异常抛出
      expect(tester.takeException(), isNull);
    });

    testWidgets('MediaKitVideoCarousel should handle dispose safely', (
      WidgetTester tester,
    ) async {
      final testFiles = [
        MultiFile(path: '/test/video1.mp4', type: 2, intervalTime: 5),
      ];

      // 创建组件
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MediaKitVideoCarousel(
              files: testFiles,
              buildMedia: (path) => Media(path),
            ),
          ),
        ),
      );

      expect(find.byType(MediaKitVideoCarousel), findsOneWidget);
      await tester.pump();

      // 销毁组件
      await tester.pumpWidget(
        const MaterialApp(home: Scaffold(body: SizedBox())),
      );

      // 等待dispose完成
      await tester.pump();

      // 验证没有异常抛出
      expect(tester.takeException(), isNull);
    });
  });

  group('Video Player Widget Creation Tests', () {
    testWidgets(
      'should create MediaKitVideoPlayer with required media parameter',
      (WidgetTester tester) async {
        final testMedia = Media('/test/video.mp4');
        final player = MediaKitVideoPlayer(media: testMedia);

        expect(player.media, equals(testMedia));
      },
    );

    testWidgets(
      'should create MediaKitVideoCarousel with required parameters',
      (WidgetTester tester) async {
        final testFiles = [
          MultiFile(path: '/test/video.mp4', type: 2, intervalTime: 5),
        ];

        final carousel = MediaKitVideoCarousel(
          files: testFiles,
          buildMedia: (path) => Media(path),
        );

        expect(carousel.files, equals(testFiles));
        expect(carousel.buildMedia, isNotNull);
      },
    );
  });
}
