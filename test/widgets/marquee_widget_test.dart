import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:marquee/marquee.dart';

import 'package:esop_client/screens/native_render_screen.dart';

void main() {
  group('Marquee Widget Tests', () {
    testWidgets('_buildMarqueeWidget should display text correctly', (
      WidgetTester tester,
    ) async {
      // 验证集成了marquee插件的NativeRenderScreen能正常运行
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: NativeRenderScreen(dataFilePath: null)),
        ),
      );

      await tester.pump();

      // 验证加载指示器存在（因为没有dataFilePath）
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('Marquee plugin should handle normal text', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 200,
              height: 50,
              child: Mar<PERSON><PERSON>(
                text: '测试文本',
                style: const TextStyle(fontSize: 16),
                scrollAxis: Axis.horizontal,
                crossAxisAlignment: CrossAxisAlignment.center,
                blankSpace: 20.0,
                velocity: 50.0,
                pauseAfterRound: const Duration(seconds: 0),
              ),
            ),
          ),
        ),
      );

      await tester.pump();
      await tester.pump(const Duration(milliseconds: 100));

      // 验证Marquee组件存在
      expect(find.byType(Marquee), findsOneWidget);
    });

    testWidgets('Marquee plugin should handle different velocities', (
      WidgetTester tester,
    ) async {
      const testText = '滚动测试文本';

      // 测试慢速度
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 200,
              height: 50,
              child: Marquee(
                text: testText,
                style: const TextStyle(fontSize: 16),
                scrollAxis: Axis.horizontal,
                crossAxisAlignment: CrossAxisAlignment.center,
                blankSpace: 20.0,
                velocity: 20.0, // 慢速度
                pauseAfterRound: const Duration(seconds: 0),
              ),
            ),
          ),
        ),
      );

      await tester.pump();
      await tester.pump(const Duration(milliseconds: 100));
      expect(find.byType(Marquee), findsOneWidget);

      // 清理定时器
      await tester.pumpWidget(Container());
      await tester.pumpAndSettle();

      // 测试快速度
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 200,
              height: 50,
              child: Marquee(
                text: testText,
                style: const TextStyle(fontSize: 16),
                scrollAxis: Axis.horizontal,
                crossAxisAlignment: CrossAxisAlignment.center,
                blankSpace: 20.0,
                velocity: 2000.0, // 快速度
                pauseAfterRound: const Duration(seconds: 0),
              ),
            ),
          ),
        ),
      );

      await tester.pump();
      await tester.pump(const Duration(milliseconds: 100));
      expect(find.byType(Marquee), findsOneWidget);

      // 清理定时器
      await tester.pumpWidget(Container());
      await tester.pumpAndSettle();
    });

    testWidgets('Marquee plugin should handle long text', (
      WidgetTester tester,
    ) async {
      const longText = '这是一个非常长的跑马灯文本，用来测试当文本长度超过容器宽度时的滚动行为，应该能够正常显示和滚动';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 200, // 设置较小的容器宽度
              height: 50,
              child: Marquee(
                text: longText,
                style: const TextStyle(fontSize: 16),
                scrollAxis: Axis.horizontal,
                crossAxisAlignment: CrossAxisAlignment.center,
                blankSpace: 20.0,
                velocity: 50.0,
                pauseAfterRound: const Duration(seconds: 0),
              ),
            ),
          ),
        ),
      );

      await tester.pump();
      await tester.pump(const Duration(milliseconds: 100));

      // 验证Marquee组件存在
      expect(find.byType(Marquee), findsOneWidget);

      // 清理定时器
      await tester.pumpWidget(Container());
      await tester.pumpAndSettle();
    });

    test('Marquee velocity calculation should be correct', () {
      // 测试速度计算公式

      // scrollSpeed = 1 时，pixelsPerSecond = 20 + (1 - 1) * 19.8 = 20
      double scrollSpeed1 = 1;
      double expectedVelocity1 = 20 + (scrollSpeed1 - 1) * 19.8;
      expect(expectedVelocity1, equals(20.0));

      // scrollSpeed = 50 时，pixelsPerSecond = 20 + (50 - 1) * 19.8 = 990.2
      double scrollSpeed50 = 50;
      double expectedVelocity50 = 20 + (scrollSpeed50 - 1) * 19.8;
      expect(expectedVelocity50, equals(990.2));

      // scrollSpeed = 100 时，pixelsPerSecond = 20 + (100 - 1) * 19.8 = 1980.2
      double scrollSpeed100 = 100;
      double expectedVelocity100 = 20 + (scrollSpeed100 - 1) * 19.8;
      expect(expectedVelocity100, equals(1980.2));
    });

    test('Empty text handling should not crash', () {
      // 测试空文本处理逻辑（在实际实现中已经处理）
      const emptyText = '';
      expect(emptyText.isEmpty, isTrue);

      // 验证我们的实现会为空文本返回空容器
      // 这在 _buildMarqueeWidget 中已经处理
    });
  });
}
