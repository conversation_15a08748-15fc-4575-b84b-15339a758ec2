import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:esop_client/screens/native_render_screen.dart';

void main() {
  group('NativeRenderScreen 视频播放优化测试', () {
    late NativeRenderScreen screen;

    testWidgets('组件基本创建测试', (WidgetTester tester) async {
      screen = const NativeRenderScreen();

      await tester.pumpWidget(MaterialApp(home: screen));

      // 验证组件能够正常创建
      expect(find.byType(NativeRenderScreen), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('数据加载状态管理测试', (WidgetTester tester) async {
      screen = const NativeRenderScreen();

      await tester.pumpWidget(MaterialApp(home: screen));

      // 初始状态应该显示加载指示器
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // 等待初始化完成
      await tester.pump();

      // 验证加载状态
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('数据路径变化处理测试', (WidgetTester tester) async {
      const String initialPath = '/test/path1/data.json';
      const String newPath = '/test/path2/data.json';

      screen = const NativeRenderScreen(dataFilePath: initialPath);

      await tester.pumpWidget(MaterialApp(home: screen));
      await tester.pump();

      // 更改数据路径
      await tester.pumpWidget(
        const MaterialApp(home: NativeRenderScreen(dataFilePath: newPath)),
      );

      // 验证组件响应路径变化
      expect(find.byType(NativeRenderScreen), findsOneWidget);
    });

    testWidgets('null 数据路径处理测试', (WidgetTester tester) async {
      screen = const NativeRenderScreen(dataFilePath: null);

      await tester.pumpWidget(MaterialApp(home: screen));

      // 验证 null 路径不会导致崩溃
      expect(find.byType(NativeRenderScreen), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('相同路径处理测试', (WidgetTester tester) async {
      const String samePath = '/test/same/data.json';

      screen = const NativeRenderScreen(dataFilePath: samePath);

      await tester.pumpWidget(MaterialApp(home: screen));
      await tester.pump();

      // 使用相同路径更新
      await tester.pumpWidget(
        const MaterialApp(home: NativeRenderScreen(dataFilePath: samePath)),
      );

      // 验证相同路径不会触发不必要的重新加载
      expect(find.byType(NativeRenderScreen), findsOneWidget);
    });

    testWidgets('界面更新验证测试', (WidgetTester tester) async {
      screen = const NativeRenderScreen();

      await tester.pumpWidget(MaterialApp(home: screen));

      // 验证初始界面状态
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // 等待状态更新
      await tester.pump(const Duration(milliseconds: 100));

      // 验证界面正确更新
      expect(find.byType(NativeRenderScreen), findsOneWidget);
    });

    group('ValueKey 机制测试', () {
      testWidgets('不同数据源触发 Widget 重建', (WidgetTester tester) async {
        // 第一个数据源
        const String path1 = '/test/data1/data.json';
        await tester.pumpWidget(
          const MaterialApp(
            home: NativeRenderScreen(
              key: ValueKey('/test/data1/data.json'),
              dataFilePath: path1,
            ),
          ),
        );

        final widget1 = tester.widget<NativeRenderScreen>(
          find.byType(NativeRenderScreen),
        );

        // 第二个数据源（使用不同的 ValueKey）
        const String path2 = '/test/data2/data.json';
        await tester.pumpWidget(
          const MaterialApp(
            home: NativeRenderScreen(
              key: ValueKey('/test/data2/data.json'),
              dataFilePath: path2,
            ),
          ),
        );

        final widget2 = tester.widget<NativeRenderScreen>(
          find.byType(NativeRenderScreen),
        );

        // 验证不同的 ValueKey 确实创建了新的 Widget 实例
        expect(widget1.key, isNot(equals(widget2.key)));
        expect(widget1.dataFilePath, isNot(equals(widget2.dataFilePath)));
      });

      testWidgets('数据变化时用户感知验证', (WidgetTester tester) async {
        // 初始状态
        await tester.pumpWidget(
          const MaterialApp(
            home: NativeRenderScreen(
              key: ValueKey('initial'),
              dataFilePath: '/test/initial/data.json',
            ),
          ),
        );

        expect(find.byType(CircularProgressIndicator), findsOneWidget);

        // 数据变化
        await tester.pumpWidget(
          const MaterialApp(
            home: NativeRenderScreen(
              key: ValueKey('updated'),
              dataFilePath: '/test/updated/data.json',
            ),
          ),
        );

        // 验证用户能感知到加载状态的变化
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.byType(NativeRenderScreen), findsOneWidget);
      });
    });

    group('视频播放优化验证', () {
      testWidgets('健康检查参数优化验证', (WidgetTester tester) async {
        // 这个测试验证健康检查间隔是否按预期优化
        screen = const NativeRenderScreen();

        await tester.pumpWidget(MaterialApp(home: screen));

        // 验证组件创建成功
        expect(find.byType(NativeRenderScreen), findsOneWidget);

        // 模拟时间流逝，验证不会过度频繁重启
        await tester.pump(const Duration(seconds: 5));
        expect(find.byType(NativeRenderScreen), findsOneWidget);

        await tester.pump(const Duration(seconds: 10));
        expect(find.byType(NativeRenderScreen), findsOneWidget);
      });

      testWidgets('播放器重启延迟优化验证', (WidgetTester tester) async {
        screen = const NativeRenderScreen();

        await tester.pumpWidget(MaterialApp(home: screen));

        // 验证组件在重启延迟期间保持稳定
        expect(find.byType(NativeRenderScreen), findsOneWidget);

        // 模拟重启延迟时间
        await tester.pump(const Duration(milliseconds: 500));
        expect(find.byType(NativeRenderScreen), findsOneWidget);
      });
    });

    tearDown(() {
      // 清理资源
    });
  });
}
