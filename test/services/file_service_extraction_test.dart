import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:path/path.dart' as path;

import 'package:esop_client/services/file_service.dart';

void main() {
  group('FileService ZIP Extraction Tests', () {
    late FileService fileService;
    late Directory tempDir;

    setUpAll(() {
      fileService = FileService();
    });

    setUp(() async {
      // Create a temporary directory for testing
      tempDir = await Directory.systemTemp.createTemp('file_service_test_');
    });

    tearDown(() async {
      // Clean up temporary directory
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    });

    testWidgets('should create separate directories for different zip files', (
      WidgetTester tester,
    ) async {
      // 测试不同zip包应该解压到不同目录
      final testZip1Name = 'ww1121.zip';
      final testZip2Name = 'ww1122.zip';

      // 验证目录命名逻辑
      expect(path.basenameWithoutExtension(testZip1Name), equals('ww1121'));
      expect(path.basenameWithoutExtension(testZip2Name), equals('ww1122'));
    });

    test('should extract zip file name without extension correctly', () {
      // 测试文件名解析
      expect(
        path.basenameWithoutExtension('/path/to/ww1121.zip'),
        equals('ww1121'),
      );
      expect(path.basenameWithoutExtension('test.zip'), equals('test'));
      expect(
        path.basenameWithoutExtension('/a/b/c/example_package.zip'),
        equals('example_package'),
      );
    });

    test('should parse marker file content correctly', () {
      // 测试标记文件格式解析
      const oldFormat = 'ww1121.zip';
      const newFormat = 'ww1121.zip|/path/to/extracted/ww1121';

      // 模拟新格式解析
      if (newFormat.contains('|')) {
        final parts = newFormat.split('|');
        expect(parts.first, equals('ww1121.zip'));
        expect(parts.last, equals('/path/to/extracted/ww1121'));
      }

      // 模拟旧格式解析
      if (!oldFormat.contains('|')) {
        expect(oldFormat.trim(), equals('ww1121.zip'));
      }
    });

    test('should generate correct extraction path for zip files', () {
      // 测试解压路径生成逻辑
      const baseExtractedPath = '/cache/extracted';
      const zipFileName1 = 'ww1121.zip';
      const zipFileName2 = 'package_v2.zip';

      // 模拟新的路径生成逻辑
      final extractPath1 = path.join(
        baseExtractedPath,
        path.basenameWithoutExtension(zipFileName1),
      );
      final extractPath2 = path.join(
        baseExtractedPath,
        path.basenameWithoutExtension(zipFileName2),
      );

      expect(extractPath1, equals('/cache/extracted/ww1121'));
      expect(extractPath2, equals('/cache/extracted/package_v2'));

      // 验证路径不同
      expect(extractPath1, isNot(equals(extractPath2)));
    });

    test('should handle file path changes correctly', () {
      // 测试路径变化检测
      const oldPath = '/cache/extracted/data.json';
      const newPath1 = '/cache/extracted/ww1121/data.json';
      const newPath2 = '/cache/extracted/ww1122/data.json';

      // 验证路径变化
      expect(oldPath, isNot(equals(newPath1)));
      expect(newPath1, isNot(equals(newPath2)));

      // 验证包含zip包名称
      expect(newPath1.contains('ww1121'), isTrue);
      expect(newPath2.contains('ww1122'), isTrue);
    });
  });

  group('ZIP File Directory Extraction Logic Tests', () {
    test('should create unique directory names for different zip packages', () {
      // 测试不同zip包的目录命名
      final zipFiles = [
        'ww1121.zip',
        'template_v1.zip',
        'assets_pack.zip',
        'content_2024.zip',
      ];

      final extractDirs = zipFiles
          .map((zip) => path.basenameWithoutExtension(zip))
          .toList();

      // 验证所有目录名都不同
      final uniqueDirs = extractDirs.toSet();
      expect(uniqueDirs.length, equals(extractDirs.length));

      // 验证目录名格式
      for (final dir in extractDirs) {
        expect(dir.isNotEmpty, isTrue);
        expect(dir.contains('.'), isFalse); // 不应包含扩展名
      }
    });

    test('should support backward compatibility with old marker format', () {
      // 测试向后兼容性
      const oldMarkerContent = 'ww1121.zip';
      const newMarkerContent = 'ww1121.zip|/cache/extracted/ww1121';

      // 解析旧格式
      String fileName1;
      String? extractPath1;

      if (oldMarkerContent.contains('|')) {
        final parts = oldMarkerContent.split('|');
        fileName1 = parts.first;
        extractPath1 = parts.last;
      } else {
        fileName1 = oldMarkerContent.trim();
        extractPath1 = null; // 旧格式没有路径信息
      }

      expect(fileName1, equals('ww1121.zip'));
      expect(extractPath1, isNull);

      // 解析新格式
      String fileName2;
      String? extractPath2;

      if (newMarkerContent.contains('|')) {
        final parts = newMarkerContent.split('|');
        fileName2 = parts.first;
        extractPath2 = parts.last;
      } else {
        fileName2 = newMarkerContent.trim();
        extractPath2 = null;
      }

      expect(fileName2, equals('ww1121.zip'));
      expect(extractPath2, equals('/cache/extracted/ww1121'));
    });
  });
}
