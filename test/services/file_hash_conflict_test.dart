import 'package:flutter_test/flutter_test.dart';
import 'package:esop_client/services/file_service.dart';
import 'package:esop_client/services/file_hash_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('文件Hash冲突处理测试', () {
    late FileService fileService;
    late FileHashService fileHashService;

    setUp(() {
      fileService = FileService();
      fileHashService = FileHashService();
    });

    test('应该能检测到hash变化并重新下载文件', () async {
      const downloadUrl = 'http://example.com/test.zip';
      const oldHash = 'old_hash_12345';
      const newHash = 'new_hash_67890';

      // 模拟第一次下载，保存旧hash
      await fileHashService.saveFileHash(
        downloadUrl: downloadUrl,
        hash: oldHash,
        localFilePath: '/tmp/test.zip',
        extractedDirPath: '/tmp/extracted/test_old_hash_12345',
      );

      // 检查旧hash - 不需要重新下载
      final shouldRedownload1 = await fileHashService.shouldRedownload(
        downloadUrl,
        oldHash,
      );
      expect(shouldRedownload1, isFalse, reason: '相同hash应该不需要重新下载');

      // 检查新hash - 需要重新下载
      final shouldRedownload2 = await fileHashService.shouldRedownload(
        downloadUrl,
        newHash,
      );
      expect(shouldRedownload2, isTrue, reason: '不同hash应该需要重新下载');
    });

    test('downloadFile方法应该在hash不匹配时删除旧文件', () async {
      // 注意：这个测试需要实际的网络环境才能完全验证
      // 这里主要测试逻辑

      const downloadUrl = 'http://example.com/test.zip';
      const oldHash = 'old_hash_12345';
      const newHash = 'new_hash_67890';

      // 模拟已存在旧文件的情况
      await fileHashService.saveFileHash(
        downloadUrl: downloadUrl,
        hash: oldHash,
        localFilePath: '/tmp/test.zip',
      );

      // 验证hash不匹配时应该重新下载
      final shouldRedownload = await fileHashService.shouldRedownload(
        downloadUrl,
        newHash,
      );
      expect(shouldRedownload, isTrue, reason: 'Hash不匹配时应该重新下载');
    });

    test('解压目录命名应该包含hash值', () {
      const downloadUrl = 'http://example.com/assets/zippack/presentation.zip';
      const hash =
          '0603b2dfae928f2787e49db304c1d52aadc01415b06e4c111e3758ef99addc5e';

      final dirName = fileHashService.generateExtractedDirName(
        downloadUrl,
        hash,
      );
      expect(dirName, equals('presentation_$hash'));

      // 验证不同hash生成不同目录名
      const newHash = 'abc123def456789';
      final newDirName = fileHashService.generateExtractedDirName(
        downloadUrl,
        newHash,
      );
      expect(newDirName, equals('presentation_$newHash'));
      expect(dirName, isNot(equals(newDirName)));
    });

    test('应该能正确处理同名文件的不同版本', () async {
      const downloadUrl = 'http://example.com/demo.zip';
      const version1Hash = 'version_1_hash';
      const version2Hash = 'version_2_hash';

      // 第一个版本
      await fileHashService.saveFileHash(
        downloadUrl: downloadUrl,
        hash: version1Hash,
        localFilePath: '/tmp/demo.zip',
        extractedDirPath: '/tmp/extracted/demo_version_1_hash',
      );

      // 第二个版本应该触发重新下载
      final shouldRedownload = await fileHashService.shouldRedownload(
        downloadUrl,
        version2Hash,
      );
      expect(shouldRedownload, isTrue);

      // 验证两个版本的解压目录名不同
      final dir1 = fileHashService.generateExtractedDirName(
        downloadUrl,
        version1Hash,
      );
      final dir2 = fileHashService.generateExtractedDirName(
        downloadUrl,
        version2Hash,
      );
      expect(dir1, isNot(equals(dir2)));
    });

    tearDown(() async {
      // 清理测试数据
      await fileHashService.clearAllHashes();
    });
  });

  group('文件冲突解决方案验证', () {
    test('修复前的问题模拟', () {
      // 模拟修复前的问题：
      // 1. 有同名旧文件
      // 2. Hash不同但文件没有被替换
      // 3. 导致解压的还是旧内容

      const fileName = 'presentation.zip';
      const oldHash = 'old_content_hash';
      const newHash = 'new_content_hash';

      // 这种情况下，旧的实现会返回现有文件而不检查hash
      // 新的实现会检查hash并删除旧文件

      expect(oldHash, isNot(equals(newHash)), reason: '模拟不同的文件内容');
    });

    test('修复后的预期行为', () async {
      const downloadUrl = 'http://server.com/presentation.zip';
      const oldHash = 'old_content_hash';
      const newHash = 'new_content_hash';

      final fileHashService = FileHashService();

      // 保存旧版本记录
      await fileHashService.saveFileHash(
        downloadUrl: downloadUrl,
        hash: oldHash,
        localFilePath: '/tmp/presentation.zip',
      );

      // 新版本应该触发重新下载
      final shouldRedownload = await fileHashService.shouldRedownload(
        downloadUrl,
        newHash,
      );
      expect(shouldRedownload, isTrue, reason: '新hash应该触发重新下载');

      // 清理
      await fileHashService.clearAllHashes();
    });
  });
}
