import 'package:flutter_test/flutter_test.dart';
import 'dart:io';
import 'dart:convert';

import 'package:esop_client/services/report_service.dart';
import 'package:esop_client/models/reported_data_model.dart';

void main() {
  group('文件打开立即上报功能测试', () {
    late ReportService reportService;

    setUp(() {
      reportService = ReportService();
    });

    test('文件打开上报功能应该可以正常调用', () async {
      // 测试所有文件类型的上报方法都能正常调用
      try {
        // 测试图片文件上报
        await reportService.reportCurrentOpenFile(
          filePath: '/test/image.jpg',
          fileName: 'image.jpg',
          fileType: 'jpg',
          fileSize: 1024000,
          viewerType: 'image_viewer',
          openMethod: 'in_app',
        );

        // 测试视频文件上报
        await reportService.reportCurrentOpenFile(
          filePath: '/test/video.mp4',
          fileName: 'video.mp4',
          fileType: 'mp4',
          fileSize: 50000000,
          viewerType: 'video_player',
          openMethod: 'in_app',
        );

        // 测试PDF文件上报
        await reportService.reportCurrentOpenFile(
          filePath: '/test/document.pdf',
          fileName: 'document.pdf',
          fileType: 'pdf',
          fileSize: 2000000,
          viewerType: 'pdf_viewer',
          openMethod: 'in_app',
        );

        // 测试压缩包文件上报
        await reportService.reportCurrentOpenFile(
          filePath: '/test/archive.zip',
          fileName: 'archive.zip',
          fileType: 'zip',
          fileSize: 10000000,
          viewerType: 'native_render',
          openMethod: 'in_app',
          zipContentName: '演示文档',
        );

        // 如果没有抛出方法签名错误，说明功能实现正确
      } catch (e) {
        // 预期会有网络错误等，这里只关心方法签名和功能实现
        expect(e.toString(), isNot(contains('Named parameter')));
        expect(e.toString(), isNot(contains('method')));
      }
    });

    test('上报服务配置应该正确', () {
      // 由于无法直接访问私有常量，我们通过文档说明验证
      // 批量上报间隔已在代码中修改为15秒
      // 文件打开上报类型已添加到立即上报列表中
      expect(reportService, isNotNull);
      expect(reportService.runtimeType.toString(), equals('ReportService'));
    });

    test('reportCurrentOpenFile方法应该包含zipContentName参数', () async {
      // 这个测试验证方法签名是否正确
      // 由于没有mock，我们只测试方法调用不报错
      try {
        await reportService.reportCurrentOpenFile(
          filePath: '/test/path',
          fileName: 'test.jpg',
          fileType: 'jpg',
          fileSize: 1024,
          viewerType: 'image_viewer',
          openMethod: 'in_app',
          zipContentName: 'test_name',
        );
        // 如果没有抛出异常，说明方法签名正确
      } catch (e) {
        // 预期会有网络错误等，这里只关心方法签名
        expect(e.toString(), isNot(contains('Named parameter')));
      }
    });

    test('reportZipFileOpened方法应该存在', () async {
      // 测试新增的压缩包文件上报方法
      try {
        await reportService.reportZipFileOpened(
          filePath: '/test/file.zip',
          fileName: 'file.zip',
          fileSize: 1000000,
          viewerType: 'native_render',
          openMethod: 'in_app',
          dataJsonPath: '/test/data.json',
        );
        // 如果没有抛出异常，说明方法存在且签名正确
      } catch (e) {
        // 预期会有网络错误等，这里只关心方法存在性
        expect(e.toString(), isNot(contains('method')));
      }
    });
  });

  group('data.json name字段读取测试', () {
    late ReportService reportService;

    setUp(() {
      reportService = ReportService();
    });

    test('成功读取data.json中的name字段', () async {
      // 创建临时data.json文件
      final tempDir = Directory.systemTemp.createTempSync('test_data_json_');
      final dataJsonFile = File('${tempDir.path}/data.json');
      
      await dataJsonFile.writeAsString(json.encode({
        'name': '公司介绍演示文档',
        'canvasRatio': '1920x1080',
        'templateSm': [
          {'templateSmType': 1, 'path': 'assets/image1.jpg'}
        ]
      }));

      // 使用反射访问私有方法进行测试
      // 由于_readZipContentName是私有方法，我们通过reportZipFileOpened来间接测试
      try {
        await reportService.reportZipFileOpened(
          filePath: '/test/file.zip',
          fileName: 'file.zip',
          fileSize: 1000,
          dataJsonPath: dataJsonFile.path,
        );
        
        // 如果没有抛出异常，说明data.json读取正常
      } catch (e) {
        // 预期可能有网络错误，但不应该有JSON解析错误
        expect(e.toString(), isNot(contains('FormatException')));
      }

      // 清理临时文件
      tempDir.deleteSync(recursive: true);
    });

    test('data.json文件不存在时不应该抛出异常', () async {
      const nonExistentPath = '/non/existent/data.json';
      
      try {
        await reportService.reportZipFileOpened(
          filePath: '/test/file.zip',
          fileName: 'file.zip',
          fileSize: 1000,
          dataJsonPath: nonExistentPath,
        );
        
        // 应该正常处理，不抛出文件不存在的异常
      } catch (e) {
        // 预期可能有网络错误，但不应该有文件不存在的异常
        expect(e.toString(), isNot(contains('FileSystemException')));
      }
    });

    test('data.json格式错误时不应该抛出异常', () async {
      // 创建格式错误的data.json文件
      final tempDir = Directory.systemTemp.createTempSync('test_invalid_json_');
      final dataJsonFile = File('${tempDir.path}/data.json');
      
      await dataJsonFile.writeAsString('invalid json content');

      try {
        await reportService.reportZipFileOpened(
          filePath: '/test/file.zip',
          fileName: 'file.zip',
          fileSize: 1000,
          dataJsonPath: dataJsonFile.path,
        );

        // 应该正常处理，不抛出JSON解析异常
      } catch (e) {
        // 预期可能有网络错误，但不应该有JSON解析异常
        expect(e.toString(), isNot(contains('FormatException')));
      }
      
      // 清理临时文件
      tempDir.deleteSync(recursive: true);
    });

    test('data.json中没有name字段时应该正常处理', () async {
      // 创建没有name字段的data.json文件
      final tempDir = Directory.systemTemp.createTempSync('test_no_name_json_');
      final dataJsonFile = File('${tempDir.path}/data.json');
      
      await dataJsonFile.writeAsString(json.encode({
        'canvasRatio': '1920x1080',
        'templateSm': []
      }));

      try {
        await reportService.reportZipFileOpened(
          filePath: '/test/file.zip',
          fileName: 'file.zip',
          fileSize: 1000,
          dataJsonPath: dataJsonFile.path,
        );

        // 应该正常处理，不抛出异常
      } catch (e) {
        // 预期可能有网络错误，但不应该有字段访问异常
        expect(e.toString(), isNot(contains('type')));
      }
      
      // 清理临时文件
      tempDir.deleteSync(recursive: true);
    });
  });

  group('ReportedDataModel测试', () {
    test('currentOpenFile工厂方法应该支持additionalInfo', () {
      final reportData = ReportedDataModel.currentOpenFile(
        macAddress: 'aa:bb:cc:dd:ee:ff',
        registrationCode: 'test123',
        deviceAlias: 'TestDevice',
        groupName: 'TestGroup',
        operationId: 'test_op_123',
        filePath: '/test/file.zip',
        fileName: 'file.zip',
        fileType: 'zip',
        fileSize: 1000000,
        viewerType: 'native_render',
        openMethod: 'in_app',
        additionalInfo: {
          'zip_content_name': '测试文档',
          'content_type': 'compressed_archive',
        },
      );

      expect(reportData.reportType, equals(ReportType.currentOpenFile));
      expect(reportData.data['file_name'], equals('file.zip'));
      expect(reportData.data['file_type'], equals('zip'));
      expect(reportData.data['viewer_type'], equals('native_render'));
      expect(reportData.data['additional_info'], isNotNull);
      expect(reportData.data['additional_info']['zip_content_name'], equals('测试文档'));
      expect(reportData.data['additional_info']['content_type'], equals('compressed_archive'));
    });
  });
}