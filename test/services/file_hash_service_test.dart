import 'package:flutter_test/flutter_test.dart';
import 'package:esop_client/services/file_hash_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('FileHashService Tests', () {
    late FileHashService fileHashService;

    setUp(() {
      fileHashService = FileHashService();
    });

    test('应该能保存和获取文件hash记录', () async {
      const downloadUrl = 'http://example.com/test.zip';
      const hash =
          '0603b2dfae928f2787e49db304c1d52aadc01415b06e4c111e3758ef99addc5e';
      const localFilePath = '/tmp/test.zip';
      const extractedDirPath = '/tmp/extracted/test_hash';

      // 保存hash记录
      await fileHashService.saveFileHash(
        downloadUrl: downloadUrl,
        hash: hash,
        localFilePath: localFilePath,
        extractedDirPath: extractedDirPath,
      );

      // 获取hash记录
      final hashData = await fileHashService.getFileHash(downloadUrl);

      expect(hashData, isNotNull);
      expect(hashData!['hash'], equals(hash));
      expect(hashData['downloadUrl'], equals(downloadUrl));
      expect(hashData['localFilePath'], equals(localFilePath));
      expect(hashData['extractedDirPath'], equals(extractedDirPath));
    });

    test('应该能判断是否需要重新下载文件', () async {
      const downloadUrl = 'http://example.com/test2.zip';
      const oldHash = 'old_hash_value';
      const newHash = 'new_hash_value';
      const localFilePath = '/tmp/test2.zip';

      // 保存旧的hash记录
      await fileHashService.saveFileHash(
        downloadUrl: downloadUrl,
        hash: oldHash,
        localFilePath: localFilePath,
      );

      // 检查相同hash - 不需要重新下载
      final shouldRedownload1 = await fileHashService.shouldRedownload(
        downloadUrl,
        oldHash,
      );
      expect(shouldRedownload1, isFalse);

      // 检查不同hash - 需要重新下载
      final shouldRedownload2 = await fileHashService.shouldRedownload(
        downloadUrl,
        newHash,
      );
      expect(shouldRedownload2, isTrue);

      // 检查无hash - 不重新下载
      final shouldRedownload3 = await fileHashService.shouldRedownload(
        downloadUrl,
        null,
      );
      expect(shouldRedownload3, isFalse);
    });

    test('应该能生成正确的解压目录名称', () {
      const downloadUrl = 'http://example.com/assets/zippack/test.zip';
      const hash =
          '0603b2dfae928f2787e49db304c1d52aadc01415b06e4c111e3758ef99addc5e';

      final dirName = fileHashService.generateExtractedDirName(
        downloadUrl,
        hash,
      );
      expect(dirName, equals('test_$hash'));

      // 无hash的情况
      final dirNameNoHash = fileHashService.generateExtractedDirName(
        downloadUrl,
        null,
      );
      expect(dirNameNoHash, equals('test'));
    });

    test('应该能删除文件hash记录', () async {
      const downloadUrl = 'http://example.com/test3.zip';
      const hash = 'test_hash_for_removal';
      const localFilePath = '/tmp/test3.zip';

      // 保存hash记录
      await fileHashService.saveFileHash(
        downloadUrl: downloadUrl,
        hash: hash,
        localFilePath: localFilePath,
      );

      // 确认记录存在
      var hashData = await fileHashService.getFileHash(downloadUrl);
      expect(hashData, isNotNull);

      // 删除记录
      await fileHashService.removeFileHash(downloadUrl);

      // 确认记录已删除
      hashData = await fileHashService.getFileHash(downloadUrl);
      expect(hashData, isNull);
    });

    test('应该能获取所有hash记录', () async {
      const downloadUrl1 = 'http://example.com/file1.zip';
      const downloadUrl2 = 'http://example.com/file2.zip';
      const hash1 = 'hash1';
      const hash2 = 'hash2';

      // 保存多个hash记录
      await fileHashService.saveFileHash(
        downloadUrl: downloadUrl1,
        hash: hash1,
        localFilePath: '/tmp/file1.zip',
      );

      await fileHashService.saveFileHash(
        downloadUrl: downloadUrl2,
        hash: hash2,
        localFilePath: '/tmp/file2.zip',
      );

      // 获取所有记录
      final allHashes = await fileHashService.getAllFileHashes();

      expect(allHashes.length, greaterThanOrEqualTo(2));
      expect(allHashes.containsKey('file1.zip'), isTrue);
      expect(allHashes.containsKey('file2.zip'), isTrue);
    });

    tearDown(() async {
      // 清理测试数据
      await fileHashService.clearAllHashes();
    });
  });
}
