import 'package:flutter_test/flutter_test.dart';
import 'package:esop_client/services/mqtt_command_queue.dart';
import 'package:esop_client/models/mqtt_message_model.dart';

void main() {
  group('MQTT 并行执行测试', () {
    late MqttCommandQueue queue;
    late List<String> executionLog;
    bool cancelCalled = false;

    setUp(() {
      executionLog = [];
      cancelCalled = false;
      queue = MqttCommandQueue(
        cancelHeavyOps: () {
          cancelCalled = true;
          executionLog.add('cancelHeavyOps_called');
        },
      );
    });

    test('操作指令应该并行执行，不等待下载指令完成', () async {
      // 创建一个长时间运行的下载指令
      final downloadMessage = MqttMessageModel(
        type: 1,
        groupName: 'test',
        fileList: [],
      );

      // 创建快速的操作指令
      final screenshotMessage = MqttMessageModel(
        type: 4,
        command: 'screenshot',
        equipmentAliasName: 'test',
      );

      final volumeMessage = MqttMessageModel(
        type: 6,
        volume: 50,
        equipmentAliasName: 'test',
      );

      // 先启动下载指令（耗时较长）
      queue.enqueue(
        topic: 'test/topic',
        payload: '{"type": 1}',
        json: {'type': 1},
        model: downloadMessage,
        executor: () async {
          executionLog.add('download_start');
          await Future.delayed(Duration(milliseconds: 200)); // 模拟下载时间
          executionLog.add('download_end');
          return true;
        },
        onComplete: (handled, error) {
          executionLog.add('download_complete');
        },
      );

      // 稍等一下确保下载开始
      await Future.delayed(Duration(milliseconds: 50));

      // 然后启动操作指令（应该立即并行执行）
      queue.enqueue(
        topic: 'test/topic',
        payload: '{"type": 4}',
        json: {'type': 4},
        model: screenshotMessage,
        executor: () async {
          executionLog.add('screenshot_start');
          await Future.delayed(Duration(milliseconds: 30)); // 快速操作
          executionLog.add('screenshot_end');
          return true;
        },
        onComplete: (handled, error) {
          executionLog.add('screenshot_complete');
        },
      );

      queue.enqueue(
        topic: 'test/topic',
        payload: '{"type": 6}',
        json: {'type': 6},
        model: volumeMessage,
        executor: () async {
          executionLog.add('volume_start');
          await Future.delayed(Duration(milliseconds: 20)); // 快速操作
          executionLog.add('volume_end');
          return true;
        },
        onComplete: (handled, error) {
          executionLog.add('volume_complete');
        },
      );

      // 等待所有操作完成
      await Future.delayed(Duration(milliseconds: 300));

      // 验证执行日志
      print('执行日志: $executionLog');

      // 验证下载指令开始执行
      expect(executionLog, contains('download_start'));

      // 验证操作指令在下载完成前就开始执行（并行）
      final downloadStartIndex = executionLog.indexOf('download_start');
      final screenshotStartIndex = executionLog.indexOf('screenshot_start');
      final volumeStartIndex = executionLog.indexOf('volume_start');
      final downloadEndIndex = executionLog.indexOf('download_end');

      expect(screenshotStartIndex, greaterThan(downloadStartIndex));
      expect(volumeStartIndex, greaterThan(downloadStartIndex));

      // 验证操作指令在下载完成前就结束了（并行执行）
      final screenshotEndIndex = executionLog.indexOf('screenshot_end');
      final volumeEndIndex = executionLog.indexOf('volume_end');

      expect(screenshotEndIndex, lessThan(downloadEndIndex));
      expect(volumeEndIndex, lessThan(downloadEndIndex));

      // 验证所有任务都完成
      expect(executionLog, contains('download_complete'));
      expect(executionLog, contains('screenshot_complete'));
      expect(executionLog, contains('volume_complete'));
    });

    test('新的下载指令应该取消旧的下载指令', () async {
      // 创建第一个下载指令
      final downloadMessage1 = MqttMessageModel(
        type: 1,
        groupName: 'test',
        fileList: [],
      );

      // 创建第二个下载指令
      final downloadMessage2 = MqttMessageModel(
        type: 2,
        groupName: 'test',
        fileList: [],
      );

      // 启动第一个下载指令
      queue.enqueue(
        topic: 'test/topic',
        payload: '{"type": 1}',
        json: {'type': 1},
        model: downloadMessage1,
        executor: () async {
          executionLog.add('download1_start');
          await Future.delayed(Duration(milliseconds: 100));
          executionLog.add('download1_end');
          return true;
        },
        onComplete: (handled, error) {
          executionLog.add('download1_complete');
        },
      );

      // 稍等确保第一个下载开始
      await Future.delayed(Duration(milliseconds: 30));

      // 启动第二个下载指令（应该取消第一个）
      queue.enqueue(
        topic: 'test/topic',
        payload: '{"type": 2}',
        json: {'type': 2},
        model: downloadMessage2,
        executor: () async {
          executionLog.add('download2_start');
          await Future.delayed(Duration(milliseconds: 50));
          executionLog.add('download2_end');
          return true;
        },
        onComplete: (handled, error) {
          executionLog.add('download2_complete');
        },
      );

      // 等待执行完成
      await Future.delayed(Duration(milliseconds: 200));

      print('执行日志: $executionLog');

      // 验证取消操作被调用
      expect(cancelCalled, isTrue);
      expect(executionLog, contains('cancelHeavyOps_called'));

      // 验证第二个下载指令执行完成
      expect(executionLog, contains('download2_start'));
      expect(executionLog, contains('download2_end'));
      expect(executionLog, contains('download2_complete'));
    });

    test('多个操作指令应该同时并行执行', () async {
      final messages = [
        MqttMessageModel(
          type: 4,
          command: 'reboot',
          equipmentAliasName: 'test',
        ),
        MqttMessageModel(
          type: 5,
          otaUrl: 'test.apk',
          equipmentAliasName: 'test',
        ),
        MqttMessageModel(type: 6, volume: 50, equipmentAliasName: 'test'),
      ];

      // 同时启动多个操作指令
      for (int i = 0; i < messages.length; i++) {
        queue.enqueue(
          topic: 'test/topic',
          payload: '{"type": ${messages[i].type}}',
          json: {'type': messages[i].type},
          model: messages[i],
          executor: () async {
            executionLog.add('operation${i + 1}_start');
            await Future.delayed(Duration(milliseconds: 50));
            executionLog.add('operation${i + 1}_end');
            return true;
          },
          onComplete: (handled, error) {
            executionLog.add('operation${i + 1}_complete');
          },
        );
      }

      // 等待所有操作完成
      await Future.delayed(Duration(milliseconds: 100));

      print('执行日志: $executionLog');

      // 验证所有操作都开始并完成
      for (int i = 1; i <= 3; i++) {
        expect(executionLog, contains('operation${i}_start'));
        expect(executionLog, contains('operation${i}_end'));
        expect(executionLog, contains('operation${i}_complete'));
      }

      // 验证并行执行：所有操作应该在很短时间内开始
      final startTimes = executionLog
          .where((log) => log.contains('_start'))
          .toList();
      expect(startTimes.length, equals(3));
    });

    test('getStatus方法应该返回正确的状态信息', () async {
      // 初始状态
      var status = queue.getStatus();
      expect(status['fileQueueLength'], equals(0));
      expect(status['fileProcessing'], equals(false));
      expect(status['runningOperations'], equals(0));

      // 添加一个下载指令
      queue.enqueue(
        topic: 'test/topic',
        payload: '{"type": 1}',
        json: {'type': 1},
        model: MqttMessageModel(type: 1, groupName: 'test', fileList: []),
        executor: () async {
          await Future.delayed(Duration(milliseconds: 100));
          return true;
        },
        onComplete: (handled, error) {},
      );

      // 添加操作指令
      queue.enqueue(
        topic: 'test/topic',
        payload: '{"type": 4}',
        json: {'type': 4},
        model: MqttMessageModel(
          type: 4,
          command: 'reboot',
          equipmentAliasName: 'test',
        ),
        executor: () async {
          await Future.delayed(Duration(milliseconds: 50));
          return true;
        },
        onComplete: (handled, error) {},
      );

      // 稍等确保任务开始
      await Future.delayed(Duration(milliseconds: 10));

      status = queue.getStatus();
      print('状态信息: $status');

      expect(status['fileProcessing'], equals(true));
      expect(status['runningOperations'], equals(1));

      // 等待所有任务完成
      await Future.delayed(Duration(milliseconds: 150));

      status = queue.getStatus();
      expect(status['fileQueueLength'], equals(0));
      expect(status['fileProcessing'], equals(false));
      expect(status['runningOperations'], equals(0));
    });
  });
}
