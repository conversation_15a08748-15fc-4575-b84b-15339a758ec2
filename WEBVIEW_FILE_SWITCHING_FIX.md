# WebView文件切换问题修复

## 问题描述
当前打开HTML文件后，再发送其他HTML打开指令时，终端上没有改变，WebView没有正确切换到新的文件。

## 问题根因分析
1. **Widget重用问题**: Flutter认为是同一个WebViewScreen widget，不会重新创建
2. **文件管理缺失**: 没有专门的机制来处理WebView中的文件切换
3. **状态管理问题**: 新文件到达时，WebView控制器没有重新加载新内容
4. **生命周期管理**: WebView实例的生命周期管理不当

## 修复方案

### 1. 添加Widget Key机制 (`lib/screens/home_screen.dart`)
```dart
// 使用文件路径作为key，确保文件变化时重新创建WebView
return WebViewScreen(
  key: ValueKey(_currentFile!.path),
  htmlFile: _currentFile!,
);
```

### 2. 创建专用文件管理器 (`lib/services/webview_file_manager.dart`)
- **文件切换检测**: 检测文件是否发生变化
- **智能重载**: 同一文件重载，不同文件切换
- **控制器管理**: 统一管理WebView控制器引用
- **错误处理**: 完善的错误处理和日志记录

### 3. 改进查看器切换逻辑 (`lib/screens/home_screen.dart`)
```dart
void _showFileViewer(File file, String fileType) {
  // 如果当前已经有文件在显示，先关闭现有的查看器
  if (_currentFile != null) {
    _viewerManager.closeAllViewers();
  }

  setState(() {
    _currentFile = file;
    _currentFileType = fileType;
    _currentPageIndex = 1;
  });
}
```

### 4. 增强WebView初始化 (`lib/screens/webview_screen.dart`)
```dart
onWebViewCreated: (controller) {
  _webViewController = controller;
  _viewerManager.currentWebViewController = controller;
  _fileManager.setController(controller);

  // 立即加载文件
  _fileManager.loadFile(widget.htmlFile);
},
```

## 修复效果

### 解决的问题
1. ✅ **文件切换响应**: 新HTML文件到达时立即切换显示
2. ✅ **Widget重建**: 使用Key机制确保WebView重新创建
3. ✅ **内存管理**: 正确清理旧的WebView实例
4. ✅ **状态同步**: 文件状态与UI状态保持同步
5. ✅ **错误处理**: 完善的错误处理和日志记录

### 技术改进
1. **专用文件管理器**: 统一处理WebView文件操作
2. **智能重载机制**: 区分文件重载和文件切换
3. **生命周期管理**: 正确管理WebView控制器生命周期
4. **调试支持**: 添加详细的调试日志

## 使用方法

修复后，当收到新的HTML文件指令时：

1. **自动检测**: 系统自动检测到新文件
2. **清理旧实例**: 自动清理当前WebView实例
3. **创建新实例**: 使用新的Key创建新的WebView
4. **加载新文件**: 自动加载新的HTML文件
5. **更新显示**: 终端显示立即更新为新内容

## 调试信息

修复后会输出以下调试信息：
```
I/flutter: _showFileViewer called with file: /path/to/new/file.html, type: zip
I/flutter: Closing existing viewer for: /path/to/old/file.html
I/flutter: Updated _currentFile to: /path/to/new/file.html
I/flutter: WebView initializing with file: /path/to/new/file.html
I/flutter: WebViewFileManager: Loading new file: /path/to/new/file.html
I/flutter: WebViewFileManager: Successfully loaded file: /path/to/new/file.html
```

## 注意事项

1. **性能考虑**: 频繁的文件切换会创建新的WebView实例，可能影响性能
2. **内存管理**: 确保旧的WebView实例被正确释放
3. **文件验证**: 在加载前验证文件的完整性和可读性
4. **错误恢复**: 如果新文件加载失败，提供错误提示和重试机制

## 测试验证

可以通过以下方式验证修复效果：
1. 打开第一个HTML文件
2. 发送第二个HTML文件指令
3. 观察终端是否立即切换到新文件
4. 检查调试日志确认文件切换过程