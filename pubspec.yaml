name: esop_client
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.2+5

environment:
  sdk: ^3.8.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # MQTT 5.0 client for Flutter
  mqtt5_client: ^4.14.0

  # Device info for getting device ID
  device_info_plus: ^9.1.2

  # Shared preferences for storing settings
  shared_preferences: ^2.2.2

  # Flutter InAppWebView for displaying HTML content
  flutter_inappwebview: ^6.0.0

  # Dio HTTP client for download progress
  dio: ^5.4.1

  # Path provider for file operations
  path_provider: ^2.1.2

  # Path package for file path operations
  path: ^1.9.0

  # Archive package for handling ZIP files
  archive: ^4.0.7

  # Provider for state management
  provider: ^6.1.1

  # Open file plugin for opening documents
  open_file: ^3.5.10

  # Connectivity for network status
  connectivity_plus: ^6.1.4

  mac_address_plus: ^0.0.1

  # PDF viewer for in-app PDF preview
  pdfx: ^2.5.0
  udp: ^5.0.3
  permission_handler: ^11.0.1

  # Screenshot plugin
  screenshot: ^3.0.0

  # Service locator
  get_it: ^7.2.0

  # Package info for getting app version from pubspec.yaml
  package_info_plus: ^8.0.0
  logger: ^2.4.0
  media_kit: ^1.2.0
  media_kit_video: ^1.3.0
  media_kit_libs_android_video: ^1.3.7
  marquee: ^2.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Internationalization support
  generate: true

  # Localization configuration
  assets:
    - lib/l10n/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
