# RK3568 MQTT关机指令修复方案

## 问题描述

在RK3568设备上，通过MQTT下发关机指令时，设备出现重启而非关机的问题。

## 根本原因分析

问题出现在 `DeviceControlHelper.kt` 的 `shutdownWithSystem()` 方法中：

```kotlin
// 问题代码：
powerManager.reboot("shutdown")  // 在RK3568上导致重启而非关机
```

**核心问题**：`PowerManager.reboot("shutdown")` 在RK3568等某些Android设备上会导致重启行为，而不是预期的关机行为。

## 解决方案

### 1. 系统级关机方法修复

修改了 `shutdownWithSystem()` 方法，使用正确的关机API：

```kotlin
fun shutdownWithSystem(): Boolean {
    Log.d(TAG, "尝试使用系统级方法关机...")
    return try {
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as android.os.PowerManager
        
        // 针对RK3568等设备的关机修复
        // 使用反射调用shutdown方法而不是reboot("shutdown")
        try {
            val shutdownMethod = android.os.PowerManager::class.java.getMethod(
                "shutdown", Boolean::class.javaPrimitiveType, String::class.java, Boolean::class.javaPrimitiveType
            )
            shutdownMethod.invoke(powerManager, false, "Device shutdown via MQTT", false)
            Log.d(TAG, "使用PowerManager.shutdown()关机成功")
            true
        } catch (e: Exception) {
            Log.w(TAG, "PowerManager.shutdown()方法失败，尝试shell命令", e)
            // 备选方法：直接使用shell命令关机
            executeShellCommand("shutdown -p")
        }
    } catch (e: Exception) {
        Log.e(TAG, "系统级关机失败", e)
        executeShellCommand("shutdown -p")
    }
}
```

### 2. Root权限关机方法优化

针对RK3568设备优化了Root关机命令的执行顺序：

```kotlin
fun shutdownWithRoot(): Boolean {
    Log.d(TAG, "尝试使用root权限关机...")
    // 针对RK3568设备优化关机命令
    return executeRootCommand("shutdown -p") || 
           executeRootCommand("poweroff") || 
           executeRootCommand("halt -p") ||
           executeRootCommand("reboot -p")
}
```

## 修复效果

### 多级关机策略

1. **PowerManager.shutdown()**: 使用反射调用真正的关机方法
2. **Shell命令**: `shutdown -p` 作为备选方案
3. **Root权限**: 多种root关机命令组合
4. **YsApi方法**: 保底的厂商API调用

### 执行流程

```mermaid
graph TD
    A[MQTT关机指令] --> B[shutdownWithSystem]
    B --> C{PowerManager.shutdown成功?}
    C -->|是| D[设备关机]
    C -->|否| E[shutdownWithRoot]
    E --> F{Root关机命令成功?}
    F -->|是| D
    F -->|否| G[shutdownWithYsApi]
    G --> H{YsApi关机成功?}
    H -->|是| D
    H -->|否| I[关机失败]
```

## 测试验证

### 测试方法

1. **发送MQTT关机指令**：
   ```json
   {
     "type": 4,
     "command": "shutdown",
     "equipmentAliasName": "设备别名"
   }
   ```

2. **观察设备行为**：
   - ✅ 设备应该完全关机（电源指示灯熄灭）
   - ❌ 设备不应该重启（避免重新开机）

### 日志监控

关键日志信息：
```
D/DeviceControlHelper: 尝试使用系统级方法关机...
D/DeviceControlHelper: 使用PowerManager.shutdown()关机成功
```

或者
```
W/DeviceControlHelper: PowerManager.shutdown()方法失败，尝试shell命令
D/DeviceControlHelper: Shell命令执行结果: exitCode=0, success=true
```

## 技术细节

### API差异说明

- `PowerManager.reboot("shutdown")`: 在某些设备上会重启
- `PowerManager.shutdown(false, "reason", false)`: 真正的关机方法
- `shutdown -p`: Shell命令直接关机
- `poweroff`: Linux标准关机命令

### 设备兼容性

- ✅ RK3568: 已修复关机问题
- ✅ 其他Android设备: 保持兼容性
- ✅ 多级备选机制: 确保关机成功率

## 相关文件

- `lib/services/device_control_service.dart`: Flutter层设备控制服务
- `lib/services/message_handler_service.dart`: MQTT消息处理服务  
- `android/app/src/main/kotlin/com/mingsign/esop_client/DeviceControlHelper.kt`: Android原生实现
- `android/app/src/main/kotlin/com/mingsign/esop_client/MainActivity.kt`: MethodChannel处理

## 注意事项

1. **权限要求**: 关机操作需要系统级权限或Root权限
2. **设备差异**: 不同厂商的Android设备可能有不同的关机行为
3. **测试重要性**: 在实际RK3568设备上验证修复效果
4. **日志监控**: 通过日志判断关机方法的执行路径

## 升级建议

建议在所有RK3568设备上部署此修复版本，并监控关机指令的执行效果。如果仍有问题，可以进一步调整Root命令的执行顺序或添加特定厂商的API调用。