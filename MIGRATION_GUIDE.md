# 版本管理系统迁移指南

## 概述

本项目已从自定义版本管理系统迁移到 Flutter 官方推荐的版本管理方式。

## 主要变化

### 旧系统 (已弃用)
- 使用 `VersionService` 和 `VersionUtils`
- 版本号格式：`YYYY.MM.DD+构建号`（基于日期）
- 版本号存储在 SharedPreferences 中
- 自动生成基于时间的版本号

### 新系统 (推荐使用)
- 使用 `AppVersionService` 和 `AppVersionUtils`
- 版本号格式：`major.minor.patch+buildNumber`（语义化版本）
- 版本号定义在 pubspec.yaml 中
- 使用 package_info_plus 插件获取版本信息

## 迁移步骤

### 1. 更新依赖
在 pubspec.yaml 中已添加：
```yaml
dependencies:
  package_info_plus: ^8.0.0
```

### 2. 更新版本号格式
将 pubspec.yaml 中的版本号改为语义化版本：
```yaml
version: 1.0.0+1  # major.minor.patch+buildNumber
```

### 3. 更新代码引用

#### 旧代码：
```dart
import '../utils/version_utils.dart';

// 获取版本号
String version = await VersionUtils.getCurrentVersion();
int buildNumber = await VersionUtils.getBuildNumber();
String formatted = await VersionUtils.getFormattedVersion();
```

#### 新代码：
```dart
import '../utils/app_version_utils.dart';

// 获取版本号
String version = await AppVersionUtils.getFullVersion();
String buildNumber = await AppVersionUtils.getBuildNumber();
String formatted = await AppVersionUtils.getFormattedVersion();
```

## API 对照表

| 旧方法 | 新方法 | 说明 |
|--------|--------|------|
| `VersionUtils.getCurrentVersion()` | `AppVersionUtils.getFullVersion()` | 获取完整版本号 |
| `VersionUtils.getBuildNumber()` | `AppVersionUtils.getBuildNumber()` | 获取构建号 |
| `VersionUtils.getFormattedVersion()` | `AppVersionUtils.getFormattedVersion()` | 获取格式化版本 |
| `VersionUtils.getVersionInfo()` | `AppVersionUtils.getVersionInfo()` | 获取版本信息 |
| `VersionUtils.getApiVersionInfo()` | `AppVersionUtils.getApiVersionInfo()` | 获取API版本信息 |
| - | `AppVersionUtils.getVersionName()` | 获取版本名称（新增） |
| - | `AppVersionUtils.getAppName()` | 获取应用名称（新增） |
| - | `AppVersionUtils.getPackageName()` | 获取包名（新增） |

## 版本号管理最佳实践

### 1. 版本号规则
- **主版本号 (major)**：不兼容的API修改
- **次版本号 (minor)**：向下兼容的功能性新增
- **修订号 (patch)**：向下兼容的问题修正
- **构建号 (build)**：每次构建递增

### 2. 更新版本号
```bash
# 开发构建（只更新构建号）
flutter build apk --build-number=2

# 补丁版本发布
# 手动更新 pubspec.yaml: version: 1.0.1+1
flutter build apk --build-name=1.0.1 --build-number=1

# 功能版本发布
# 手动更新 pubspec.yaml: version: 1.1.0+1
flutter build apk --build-name=1.1.0 --build-number=1
```

### 3. 自动化版本管理
可以使用脚本自动更新版本号：
```bash
#!/bin/bash
# 自动递增构建号
current_build=$(grep "version:" pubspec.yaml | sed 's/.*+//')
new_build=$((current_build + 1))
sed -i "s/+$current_build/+$new_build/" pubspec.yaml
```

## 文件清理

以下旧文件可以考虑删除（在确认不再使用后）：
- `lib/services/version_service.dart`
- `lib/utils/version_utils.dart`
- `lib/examples/version_usage_example.dart`

## 注意事项

1. **向后兼容**：新系统与旧系统的版本号格式不同，需要注意数据迁移
2. **构建脚本**：更新CI/CD脚本以使用新的版本管理方式
3. **测试**：确保所有使用版本号的功能都正常工作
4. **文档**：更新相关文档和README

## 示例用法

```dart
import 'package:flutter/material.dart';
import '../utils/app_version_utils.dart';

class AboutPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('关于')),
      body: FutureBuilder<String>(
        future: AppVersionUtils.getFormattedVersion(),
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            return Text('当前版本: ${snapshot.data}');
          }
          return CircularProgressIndicator();
        },
      ),
    );
  }
}
```

## 总结

新的版本管理系统更符合 Flutter 官方标准，提供了更好的兼容性和可维护性。建议尽快完成迁移，并删除旧的版本管理代码。