# 外部存储路径迁移文档

## 概述
本次修改将ZIP文件下载存储、解压文件存储、截图文件存储从临时目录迁移到外部存储目录，以提供更好的存储管理和用户访问性。

## 修改的文件

### 1. lib/services/file_service.dart
**修改内容：**
- `_getZippakDirectory()` 方法：从使用 `getTemporaryDirectory()` 改为优先使用 `getExternalStorageDirectory()`
- `_getExtractedDirectory()` 方法：从使用 `getTemporaryDirectory()` 改为优先使用 `getExternalStorageDirectory()`

**存储路径变化：**
- ZIP文件存储：`{外部存储}/zippak/` (原：`{临时目录}/zippak/`)
- 解压文件存储：`{外部存储}/extracted/` (原：`{临时目录}/extracted/`)

**降级策略：**
如果外部存储不可用（如iOS或权限问题），自动降级到临时目录。

### 2. lib/services/device_control_service.dart
**修改内容：**
- `takeScreenshot()` 方法：从使用 `getTemporaryDirectory()` 改为优先使用 `getExternalStorageDirectory()`

**存储路径变化：**
- 截图存储：`{外部存储}/screenshots/screenshot_{timestamp}.png` (原：`{临时目录}/screenshot.png`)

**改进：**
- 添加了时间戳到文件名，避免文件覆盖
- 创建专门的screenshots目录
- 降级策略：如果外部存储不可用，使用临时目录

### 3. lib/services/storage_cleanup_service.dart
**修改内容：**
- `performCleanup()` 方法：增加对外部存储目录的清理

**清理范围扩展：**
- 原来只清理应用文档目录
- 现在同时清理应用文档目录和外部存储目录

## 技术细节

### 存储路径优先级
1. **首选：** `getExternalStorageDirectory()` - 外部存储（Android）
2. **降级：** `getTemporaryDirectory()` - 临时目录（iOS/Android）

### 目录结构
```
外部存储根目录/
├── zippak/           # ZIP文件下载存储
├── extracted/        # 解压文件存储
│   └── {hash_dirs}/  # 按hash命名的解压目录
└── screenshots/      # 截图文件存储
```

### 兼容性
- **Android：** 使用外部存储，提供更好的用户访问性
- **iOS：** 自动降级到临时目录（iOS不支持外部存储概念）
- **权限：** 如果外部存储权限不足，自动降级

## 优势

1. **更好的存储管理：** 外部存储通常有更大的空间
2. **用户可访问：** 用户可以通过文件管理器访问文件（Android）
3. **持久性：** 外部存储的文件在应用卸载时可能保留
4. **向后兼容：** 保持与现有代码的兼容性
5. **自动降级：** 在不支持外部存储的平台上自动使用临时目录

## 测试验证

### 编译检查
```bash
flutter analyze lib/services/file_service.dart lib/services/device_control_service.dart lib/services/storage_cleanup_service.dart
```
结果：只有2个未使用方法的警告，无编译错误。

### 现有测试
现有的文件服务测试仍然通过，确保功能兼容性。

## 注意事项

1. **权限：** Android应用需要确保有外部存储权限
2. **清理：** 存储清理服务现在会清理两个位置的文件
3. **路径变化：** 应用重新安装后，文件路径可能发生变化
4. **平台差异：** iOS和Android的存储行为可能不同

## 后续建议

1. 在真实设备上测试外部存储功能
2. 验证权限处理是否正确
3. 测试存储空间不足时的行为
4. 考虑添加用户设置来选择存储位置
