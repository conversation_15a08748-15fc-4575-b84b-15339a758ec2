# Flutter-specific ProGuard rules.
#
# This file is used by the Flutter tool to generate ProGuard rules for the app.
# You can add your own rules here, but it is recommended to keep this file
# as-is and add your own rules in a separate file.
#
# For more information, see:
# https://flutter.dev/docs/deployment/android#obfuscating-your-code
# Keep rules for ysapi.jar to prevent obfuscation/removal in release mode.
# This is necessary because the API is accessed via reflection.
-keep class com.ys.rkapi.** { *; }
-dontwarn com.ys.rkapi.**
