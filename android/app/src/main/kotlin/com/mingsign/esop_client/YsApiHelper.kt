package com.mingsign.esop_client

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log

/** YsApi集成助手类 用于兼容多种开机自启方式 */
class YsApiHelper(private val context: Context) {

    companion object {
        private const val TAG = "YsApiHelper"
        private const val PACKAGE_NAME = "com.mingsign.esop_client"
    }

    private var myManager: Any? = null

    /** 初始化YsApi管理器 */
    fun initializeYsApi(): Boolean {
        return try {
            Log.d(TAG, "开始初始化YsApi...")

            // 通过反射获取MyManager类
            val myManagerClass = Class.forName("com.ys.rkapi.MyManager")
            val getInstanceMethod = myManagerClass.getMethod("getInstance", Context::class.java)

            // 获取MyManager实例
            myManager = getInstanceMethod.invoke(null, context)
            Log.d(TAG, "MyManager实例创建成功")

            // 绑定AIDL服务
            val bindAIDLServiceMethod =
                    myManagerClass.getMethod("bindAIDLService", Context::class.java)
            bindAIDLServiceMethod.invoke(myManager, context)
            Log.d(TAG, "AIDL服务绑定成功")

            // 设置连接回调
            try {
                val setConnectClickInterfaceMethod =
                        myManagerClass.getMethod(
                                "setConnectClickInterface",
                                Class.forName("com.ys.rkapi.MyManager\$ServiceConnectedInterface")
                        )

                // 创建回调接口的代理实例
                val serviceConnectedInterface =
                        java.lang.reflect.Proxy.newProxyInstance(
                                context.classLoader,
                                arrayOf(
                                        Class.forName(
                                                "com.ys.rkapi.MyManager\$ServiceConnectedInterface"
                                        )
                                )
                        ) { _, method, _ ->
                            if (method.name == "onConnect") {
                                Log.d(TAG, "YsApi服务连接成功，可以正常调用接口")
                                // 在连接成功后自动设置开机自启和守护进程
                                setupAutoStartAndDaemon()
                            }
                            null
                        }

                setConnectClickInterfaceMethod.invoke(myManager, serviceConnectedInterface)
                Log.d(TAG, "连接回调设置成功")
            } catch (e: Exception) {
                Log.w(TAG, "设置连接回调失败，但不影响基本功能", e)
            }

            // 等待服务连接
            Thread.sleep(1000)

            true
        } catch (e: ClassNotFoundException) {
            Log.e(TAG, "未找到YsApi MyManager类，可能jar文件未正确加载", e)
            false
        } catch (e: Exception) {
            Log.e(TAG, "初始化YsApi失败", e)
            false
        }
    }

    /** 设置开机自启 使用ysapi.jar的selfStart方法替换现有的BootCompleteReceiver */
    fun setSelfStart(): Boolean {
        return try {
            Log.d(TAG, "开始设置开机自启...")

            if (myManager == null) {
                Log.e(TAG, "MyManager未初始化，无法设置开机自启")
                return false
            }

            // 通过反射调用selfStart方法
            val myManagerClass = myManager!!.javaClass
            val selfStartMethod = myManagerClass.getMethod("selfStart", String::class.java)

            val result = selfStartMethod.invoke(myManager, PACKAGE_NAME) as? Boolean ?: true

            if (result) {
                Log.d(TAG, "YsApi开机自启设置成功: $PACKAGE_NAME")
            } else {
                Log.e(TAG, "YsApi开机自启设置失败")
            }

            result
        } catch (e: NoSuchMethodException) {
            Log.e(TAG, "未找到YsApi selfStart方法", e)
            false
        } catch (e: Exception) {
            Log.e(TAG, "设置YsApi开机自启失败", e)
            false
        }
    }

    /** 设置程序守护 使用ysapi.jar的daemon方法进行程序守护 */
    fun setDaemon(): Boolean {
        return try {
            Log.d(TAG, "开始设置程序守护...")

            if (myManager == null) {
                Log.e(TAG, "MyManager未初始化，无法设置程序守护")
                return false
            }

            // 通过反射调用daemon方法
            val myManagerClass = myManager!!.javaClass
            val daemonMethod =
                    myManagerClass.getMethod(
                            "daemon",
                            String::class.java,
                            Int::class.javaPrimitiveType
                    )

            // 设置守护时间：0=30秒，1=60秒，2=180秒，默认使用60秒
            val guardTime = 1 // 60秒
            val result = daemonMethod.invoke(myManager, PACKAGE_NAME, guardTime) as? Boolean ?: true

            if (result) {
                Log.d(TAG, "YsApi程序守护设置成功: $PACKAGE_NAME, 守护时间: ${guardTime * 30 + 30}秒")
            } else {
                Log.e(TAG, "YsApi程序守护设置失败")
            }

            result
        } catch (e: NoSuchMethodException) {
            Log.e(TAG, "未找到YsApi daemon方法", e)
            false
        } catch (e: Exception) {
            Log.e(TAG, "设置YsApi程序守护失败", e)
            false
        }
    }

    /** 自动设置开机自启和守护进程 在YsApi服务连接成功后调用 */
    private fun setupAutoStartAndDaemon() {
        try {
            Log.d(TAG, "开始自动设置开机自启和守护进程...")

            // 设置开机自启
            val selfStartSuccess = setSelfStart()

            // 设置程序守护
            val daemonSuccess = setDaemon()

            if (selfStartSuccess && daemonSuccess) {
                Log.d(TAG, "YsApi开机自启和程序守护设置完成")
            } else {
                Log.w(TAG, "YsApi设置部分失败 - 开机自启: $selfStartSuccess, 程序守护: $daemonSuccess")
            }
        } catch (e: Exception) {
            Log.e(TAG, "自动设置开机自启和守护进程失败", e)
        }
    }

    /** 取消绑定AIDL服务 在组件销毁时调用 */
    fun unbindAIDLService() {
        try {
            if (myManager != null) {
                val myManagerClass = myManager!!.javaClass
                val unbindMethod =
                        myManagerClass.getMethod("unBindAIDLService", Context::class.java)
                unbindMethod.invoke(myManager, context)
                Log.d(TAG, "YsApi AIDL服务解绑成功")
            }
        } catch (e: Exception) {
            Log.e(TAG, "解绑YsApi AIDL服务失败", e)
        }
    }

    /** 检查YsApi是否可用 */
    fun isYsApiAvailable(): Boolean {
        return try {
            Class.forName("com.ys.rkapi.MyManager")
            true
        } catch (e: ClassNotFoundException) {
            false
        }
    }

    /** 获取YsApi版本信息 */
    fun getYsApiVersion(): String {
        return try {
            if (myManager == null) {
                return "YsApi未初始化"
            }

            val myManagerClass = myManager!!.javaClass
            val getApiVersionMethod = myManagerClass.getMethod("getApiVersion")
            val version = getApiVersionMethod.invoke(myManager) as? String ?: "未知版本"

            Log.d(TAG, "YsApi版本: $version")
            version
        } catch (e: Exception) {
            Log.e(TAG, "获取YsApi版本失败", e)
            "获取版本失败"
        }
    }

    /** 检测设备是否支持YsApi selfStart方法 */
    fun isYsApiSelfStartSupported(): Boolean {
        return try {
            // 检查YsApi类是否存在
            if (!isYsApiAvailable()) {
                Log.d(TAG, "YsApi类不存在，不支持YsApi selfStart")
                return false
            }

            // 检查MyManager实例是否存在
            if (myManager == null) {
                Log.d(TAG, "MyManager未初始化，无法检测selfStart支持")
                return false
            }

            // 检查selfStart方法是否存在
            val myManagerClass = myManager!!.javaClass
            myManagerClass.getMethod("selfStart", String::class.java)

            Log.d(TAG, "YsApi selfStart方法支持检测通过")
            true
        } catch (e: Exception) {
            Log.d(TAG, "YsApi selfStart方法不支持: ${e.message}")
            false
        }
    }

    /** 检测设备是否支持BootCompleteReceiver */
    fun isBootCompleteReceiverSupported(): Boolean {
        return try {
            // 检查Android版本（某些版本可能有限制）
            val isSupportedVersion = Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH

            // 对于Android 6.0以下版本，不需要运行时权限检查
            // RECEIVE_BOOT_COMPLETED 权限在AndroidManifest.xml中声明即可
            val permissionGranted = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // Android 6.0+ 需要检查运行时权限
                try {
                    context.checkSelfPermission(
                        android.Manifest.permission.RECEIVE_BOOT_COMPLETED
                    ) == PackageManager.PERMISSION_GRANTED
                } catch (e: Exception) {
                    // 如果checkSelfPermission方法不存在，默认认为权限已授予
                    Log.w(TAG, "checkSelfPermission方法不可用，默认认为权限已授予", e)
                    true
                }
            } else {
                // Android 6.0以下版本，静态权限声明即可
                true
            }

            Log.d(TAG, "BootCompleteReceiver支持检测: 权限=$permissionGranted, 版本支持=$isSupportedVersion")
            permissionGranted && isSupportedVersion
        } catch (e: Exception) {
            Log.e(TAG, "检测BootCompleteReceiver支持失败", e)
            false
        }
    }

    /** 智能选择开机自启方式 */
    fun setupAutoStart(): AutoStartResult {
        Log.d(TAG, "开始智能选择开机自启方式...")

        val ysApiSupported = isYsApiSelfStartSupported()
        val bootReceiverSupported = isBootCompleteReceiverSupported()

        Log.d(TAG, "兼容性检测结果 - YsApi: $ysApiSupported, BootReceiver: $bootReceiverSupported")

        return when {
            // 优先使用YsApi（系统级，更稳定）
            ysApiSupported -> {
                Log.d(TAG, "选择YsApi selfStart方式")
                val success = setSelfStart()
                AutoStartResult(AutoStartMethod.YS_API, success)
            }

            // YsApi不可用时，使用BootCompleteReceiver
            bootReceiverSupported -> {
                Log.d(TAG, "选择BootCompleteReceiver方式")
                // BootCompleteReceiver已在AndroidManifest中注册，无需额外设置
                AutoStartResult(AutoStartMethod.BOOT_RECEIVER, true)
            }

            // 两种方式都不支持
            else -> {
                Log.e(TAG, "两种开机自启方式都不支持")
                AutoStartResult(AutoStartMethod.NONE, false)
            }
        }
    }

    /** 获取设备信息用于调试 */
    fun getDeviceInfo(): String {
        return "设备信息: Android ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT}), " +
               "制造商: ${Build.MANUFACTURER}, 型号: ${Build.MODEL}, " +
               "硬件: ${Build.HARDWARE}"
    }
}

/** 开机自启方式枚举 */
enum class AutoStartMethod {
    YS_API,      // YsApi selfStart方法
    BOOT_RECEIVER, // BootCompleteReceiver
    NONE         // 不支持任何方式
}

/** 开机自启设置结果 */
data class AutoStartResult(
    val method: AutoStartMethod,
    val success: Boolean
)
