package com.mingsign.esop_client

import android.app.ActivityManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageInstaller
import android.content.pm.PackageManager
import android.media.MediaCodecList
import android.os.Build
import android.webkit.WebView
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.io.File
import java.io.FileInputStream

class MainActivity : FlutterActivity() {
    private val INSTALL_CHANNEL = "com.mingsign.esop_client/silent_install"
    private val DEVICE_CONTROL_CHANNEL = "com.mingsign.esop_client/device_control"
    private val H265_COMPATIBILITY_CHANNEL = "h265_compatibility"
    private val INSTALL_REQUEST_CODE = 1001
    private val deviceControlHelper by lazy { DeviceControlHelper(this) }
    private val ysApiHelper by lazy { Ys<PERSON><PERSON>Helper(this) }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        android.util.Log.d("MainActivity", "=== 开始配置Flutter引擎 ===")
        android.util.Log.d("MainActivity", "Flutter引擎状态: ${flutterEngine.dartExecutor}")

        // 初始化YsApi助手
        initializeYsApiHelper()

        // 静默安装通道
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, INSTALL_CHANNEL)
                .setMethodCallHandler { call, result ->
                    when (call.method) {
                        "silentInstall" -> {
                            val apkPath = call.argument<String>("apkPath")
                            if (apkPath != null) {
                                val success = silentInstallApk(apkPath)
                                result.success(success)
                            } else {
                                result.error("INVALID_ARGUMENT", "APK路径不能为空", null)
                            }
                        }
                        "hasInstallPermission" -> {
                            result.success(hasInstallPermission())
                        }
                        else -> {
                            result.notImplemented()
                        }
                    }
                }

        // MAC地址通道
        val MAC_ADDRESS_CHANNEL = "mac_address"
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, MAC_ADDRESS_CHANNEL)
                .setMethodCallHandler { call, result ->
                    when (call.method) {
                        "getMacAddress" -> {
                            try {
                                val macAddress = deviceControlHelper.getMacAddressWithYsApi()
                                result.success(macAddress)
                            } catch (e: Exception) {
                                result.error("MAC_ADDRESS_ERROR", "获取MAC地址失败: ${e.message}", null)
                            }
                        }
                        else -> {
                            result.notImplemented()
                        }
                    }
                }

        // 设备控制通道
        android.util.Log.d("MainActivity", "=== 注册设备控制通道 ===")
        android.util.Log.d("MainActivity", "通道名称: $DEVICE_CONTROL_CHANNEL")
        android.util.Log.d(
                "MainActivity",
                "BinaryMessenger: ${flutterEngine.dartExecutor.binaryMessenger}"
        )

        val deviceControlChannel =
                MethodChannel(flutterEngine.dartExecutor.binaryMessenger, DEVICE_CONTROL_CHANNEL)
        android.util.Log.d("MainActivity", "MethodChannel创建成功: $deviceControlChannel")

        deviceControlChannel.setMethodCallHandler { call, result ->
            android.util.Log.d("MainActivity", "=== 收到方法调用 ===")
            android.util.Log.d("MainActivity", "方法名: ${call.method}")
            android.util.Log.d("MainActivity", "参数: ${call.arguments}")

            when (call.method) {
                "test" -> {
                    android.util.Log.d("MainActivity", "执行测试方法")
                    result.success("测试成功 - MethodChannel工作正常")
                }
                "rebootWithRoot" -> {
                    android.util.Log.d("MainActivity", "执行rebootWithRoot方法")
                    try {
                        val success = deviceControlHelper.rebootWithRoot()
                        if (success) {
                            result.success(true)
                        } else {
                            result.error("REBOOT_FAILED", "Root重启失败", null)
                        }
                    } catch (e: Exception) {
                        result.error("REBOOT_FAILED", "Root重启失败", e.message)
                    }
                }
                "shutdownWithRoot" -> {
                    android.util.Log.d("MainActivity", "执行shutdownWithRoot方法")
                    try {
                        val success = deviceControlHelper.shutdownWithRoot()
                        if (success) {
                            result.success(true)
                        } else {
                            result.error("SHUTDOWN_FAILED", "Root关机失败", null)
                        }
                    } catch (e: Exception) {
                        result.error("SHUTDOWN_FAILED", "Root关机失败", e.message)
                    }
                }
                "rebootWithSystem" -> {
                    android.util.Log.d("MainActivity", "执行rebootWithSystem方法")
                    try {
                        val success = deviceControlHelper.rebootWithSystem()
                        if (success) {
                            result.success(true)
                        } else {
                            result.error("SYSTEM_REBOOT_FAILED", "系统级重启失败", null)
                        }
                    } catch (e: Exception) {
                        result.error("SYSTEM_REBOOT_FAILED", "系统级重启失败", e.message)
                    }
                }
                "shutdownWithSystem" -> {
                    android.util.Log.d("MainActivity", "执行shutdownWithSystem方法")
                    try {
                        val success = deviceControlHelper.shutdownWithSystem()
                        if (success) {
                            result.success(true)
                        } else {
                            result.error("SYSTEM_SHUTDOWN_FAILED", "系统级关机失败", null)
                        }
                    } catch (e: Exception) {
                        result.error("SYSTEM_SHUTDOWN_FAILED", "系统级关机失败", e.message)
                    }
                }
                "rebootWithYsApi" -> {
                    try {
                        val success = deviceControlHelper.executeYsApiReboot()
                        if (success) {
                            result.success(true)
                        } else {
                            result.error("YSAPI_REBOOT_FAILED", "YsApi重启失败", null)
                        }
                    } catch (e: Exception) {
                        result.error("YSAPI_REBOOT_FAILED", "YsApi重启失败", e.message)
                    }
                }
                "shutdownWithYsApi" -> {
                    try {
                        val success = deviceControlHelper.executeYsApiShutdown()
                        if (success) {
                            result.success(true)
                        } else {
                            result.error("YSAPI_SHUTDOWN_FAILED", "YsApi关机失败", null)
                        }
                    } catch (e: Exception) {
                        result.error("YSAPI_SHUTDOWN_FAILED", "YsApi关机失败", e.message)
                    }
                }
                "setVolume" -> {
                    val volume = call.argument<Int>("volume")
                    if (volume != null) {
                        android.util.Log.d("MainActivity", "收到设置音量请求: $volume%")
                        try {
                            val success = deviceControlHelper.setSystemVolume(volume)
                            if (success) {
                                android.util.Log.d("MainActivity", "音量设置成功: $volume%")
                                result.success(null)
                            } else {
                                android.util.Log.e("MainActivity", "音量设置失败: $volume%")
                                result.error("VOLUME_FAILED", "设置音量失败", "音量设置操作返回false")
                            }
                        } catch (e: Exception) {
                            android.util.Log.e("MainActivity", "音量设置异常: $volume%", e)
                            result.error(
                                    "VOLUME_FAILED",
                                    "设置音量失败: ${e.message}",
                                    e.stackTraceToString()
                            )
                        }
                    } else {
                        android.util.Log.e("MainActivity", "音量参数为空")
                        result.error("INVALID_ARGUMENT", "音量值不能为空", null)
                    }
                }
                "setPowerOnOffWithWeekly" -> {
                    val powerOnHour = call.argument<Int>("powerOnHour")
                    val powerOnMinute = call.argument<Int>("powerOnMinute")
                    val powerOffHour = call.argument<Int>("powerOffHour")
                    val powerOffMinute = call.argument<Int>("powerOffMinute")
                    val weekdaysArg = call.argument<List<Int>>("weekdays")
                    if (powerOnHour != null &&
                                    powerOnMinute != null &&
                                    powerOffHour != null &&
                                    powerOffMinute != null &&
                                    weekdaysArg != null &&
                                    weekdaysArg.size == 7
                    ) {
                        try {
                            val success =
                                    deviceControlHelper.setPowerOnOffWithWeekly(
                                            powerOnHour,
                                            powerOnMinute,
                                            powerOffHour,
                                            powerOffMinute,
                                            weekdaysArg
                                    )
                            if (success) {
                                result.success(null)
                            } else {
                                result.error("YSAPI_SCHEDULE_FAILED", "ysapi 设置定时失败", null)
                            }
                        } catch (e: Exception) {
                            android.util.Log.e("MainActivity", "设置定时开关机异常", e)
                            result.error(
                                    "YSAPI_SCHEDULE_FAILED",
                                    "设置定时开关机异常: ${e.message}",
                                    e.stackTraceToString()
                            )
                        }
                    } else {
                        result.error(
                                "INVALID_ARGUMENT",
                                "参数缺失或格式错误，需 powerOnHour/powerOnMinute/powerOffHour/powerOffMinute 以及长度为7的 weekdays",
                                null
                        )
                    }
                }
                "clearPowerOnOffTime" -> {
                    android.util.Log.d("MainActivity", "执行clearPowerOnOffTime方法")
                    try {
                        val success = deviceControlHelper.clearPowerOnOffTime()
                        if (success) {
                            android.util.Log.d("MainActivity", "清除定时开关机成功")
                            result.success(null)
                        } else {
                            android.util.Log.e("MainActivity", "清除定时开关机失败")
                            result.error("CLEAR_SCHEDULE_FAILED", "清除定时开关机失败", null)
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("MainActivity", "清除定时开关机异常", e)
                        result.error(
                                "CLEAR_SCHEDULE_FAILED",
                                "清除定时开关机异常: ${e.message}",
                                e.stackTraceToString()
                        )
                    }
                }
                else -> result.notImplemented()
            }
        }

        // H.265兼容性检测通道
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, H265_COMPATIBILITY_CHANNEL)
                .setMethodCallHandler { call, result ->
                    when (call.method) {
                        "getDeviceInfo" -> {
                            try {
                                val deviceInfo = getDeviceInfo()
                                result.success(deviceInfo)
                            } catch (e: Exception) {
                                result.error("DEVICE_INFO_ERROR", "获取设备信息失败: ${e.message}", null)
                            }
                        }
                        "checkH265HardwareSupport" -> {
                            try {
                                val h265Support = checkH265HardwareSupport()
                                result.success(h265Support)
                            } catch (e: Exception) {
                                result.error(
                                        "H265_CHECK_ERROR",
                                        "检测H.265硬件支持失败: ${e.message}",
                                        null
                                )
                            }
                        }
                        "checkWebViewH265Support" -> {
                            try {
                                val webViewSupport = checkWebViewH265Support()
                                result.success(webViewSupport)
                            } catch (e: Exception) {
                                result.error(
                                        "WEBVIEW_CHECK_ERROR",
                                        "检测WebView H.265支持失败: ${e.message}",
                                        null
                                )
                            }
                        }
                        "testAutoStartCompatibility" -> {
                            try {
                                val compatibilityResult = testAutoStartCompatibility()
                                result.success(compatibilityResult)
                            } catch (e: Exception) {
                                result.error(
                                        "AUTOSTART_TEST_ERROR",
                                        "开机自启兼容性测试失败: ${e.message}",
                                        null
                                )
                            }
                        }
                        else -> result.notImplemented()
                    }
                }

        // 视频性能监控通道
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "video_performance")
                .setMethodCallHandler { call, result ->
                    when (call.method) {
                        "getMemoryInfo" -> {
                            try {
                                val memoryInfo = getMemoryInfo()
                                result.success(memoryInfo)
                            } catch (e: Exception) {
                                result.error("MEMORY_INFO_ERROR", "获取内存信息失败: ${e.message}", null)
                            }
                        }
                        else -> {
                            result.notImplemented()
                        }
                    }
                }
    }

    private fun silentInstallApk(apkPath: String): Boolean {
        return try {
            val apkFile = File(apkPath)
            if (!apkFile.exists()) {
                return false
            }

            // 方法1：使用PackageInstaller（适用于系统应用）
            if (hasInstallPermission()) {
                installUsingPackageInstaller(apkFile)
            } else {
                // 方法2：使用Shell命令（需要root权限）
                installUsingShellCommand(apkPath)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    private fun installUsingPackageInstaller(apkFile: File): Boolean {
        return try {
            val packageInstaller = packageManager.packageInstaller
            val params =
                    PackageInstaller.SessionParams(PackageInstaller.SessionParams.MODE_FULL_INSTALL)

            val sessionId = packageInstaller.createSession(params)
            val session = packageInstaller.openSession(sessionId)

            val inputStream = FileInputStream(apkFile)
            val outputStream = session.openWrite("package", 0, apkFile.length())

            inputStream.copyTo(outputStream)
            session.fsync(outputStream)
            inputStream.close()
            outputStream.close()

            // 创建安装意图
            val intent = Intent(this, InstallResultReceiver::class.java)
            val pendingIntent =
                    PendingIntent.getBroadcast(
                            this,
                            INSTALL_REQUEST_CODE,
                            intent,
                            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                    )

            session.commit(pendingIntent.intentSender)
            session.close()
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    private fun installUsingShellCommand(apkPath: String): Boolean {
        return try {
            val process = Runtime.getRuntime().exec("su -c 'pm install -r $apkPath'")
            val exitCode = process.waitFor()
            exitCode == 0
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    private fun hasInstallPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            checkSelfPermission(android.Manifest.permission.INSTALL_PACKAGES) ==
                    PackageManager.PERMISSION_GRANTED
        } else {
            true
        }
    }

    /** 获取设备信息 */
    private fun getDeviceInfo(): Map<String, Any> {
        return mapOf(
                "model" to Build.MODEL,
                "manufacturer" to Build.MANUFACTURER,
                "androidVersion" to Build.VERSION.RELEASE,
                "apiLevel" to Build.VERSION.SDK_INT,
                "chipset" to getChipsetInfo(),
                "board" to Build.BOARD,
                "hardware" to Build.HARDWARE,
                "product" to Build.PRODUCT
        )
    }

    /** 获取芯片组信息 */
    private fun getChipsetInfo(): String {
        return try {
            // 尝试从多个来源获取芯片组信息
            val sources = mutableListOf(Build.HARDWARE, Build.BOARD)

            // 只在Android 12+ (API 31+) 中添加SOC信息
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                try {
                    val socManufacturer =
                            Build::class.java.getField("SOC_MANUFACTURER").get(null) as? String
                    val socModel = Build::class.java.getField("SOC_MODEL").get(null) as? String
                    socManufacturer?.let { sources.add(it) }
                    socModel?.let { sources.add(it) }
                } catch (e: Exception) {
                    // 忽略反射错误
                }
            }

            // 查找包含已知芯片组名称的信息
            val knownChipsets =
                    listOf(
                            "rk3568",
                            "rk3566",
                            "rk3399",
                            "snapdragon",
                            "exynos",
                            "kirin",
                            "mediatek",
                            "allwinner",
                            "amlogic"
                    )

            for (source in sources) {
                for (chipset in knownChipsets) {
                    if (source.lowercase().contains(chipset)) {
                        return source
                    }
                }
            }

            // 如果没有找到已知芯片组，返回硬件信息
            Build.HARDWARE
        } catch (e: Exception) {
            "Unknown"
        }
    }

    /** 检测H.265硬件解码支持 */
    private fun checkH265HardwareSupport(): Map<String, Any> {
        val result = mutableMapOf<String, Any>()

        try {
            val codecList = MediaCodecList(MediaCodecList.ALL_CODECS)
            val codecs = codecList.codecInfos

            var hasDecoder = false
            var decoderName = ""
            val profiles = mutableListOf<String>()
            var maxResolution = ""
            var reliable = false

            // 专门查找H.265/HEVC解码器
            for (codec in codecs) {
                if (!codec.isEncoder) {
                    val supportedTypes = codec.supportedTypes
                    for (type in supportedTypes) {
                        // 只检测H.265/HEVC，不包括VP9
                        if (type.equals("video/hevc", ignoreCase = true)) {
                            android.util.Log.d("H265Check", "Found H.265 decoder: ${codec.name}, type: $type")

                            // 检查是否为硬件解码器
                            val isHardware =
                                    !codec.name.lowercase().contains("software") &&
                                            !codec.name.lowercase().contains("sw") &&
                                            !codec.name.lowercase().contains("google") &&
                                            !codec.name.lowercase().contains("ffmpeg")

                            android.util.Log.d("H265Check", "Decoder ${codec.name} is hardware: $isHardware")

                            if (isHardware) {
                                hasDecoder = true
                                decoderName = codec.name

                                try {
                                    val capabilities = codec.getCapabilitiesForType(type)
                                    val videoCapabilities = capabilities.videoCapabilities

                                    if (videoCapabilities != null) {
                                        val maxWidth = videoCapabilities.supportedWidths.upper
                                        val maxHeight = videoCapabilities.supportedHeights.upper
                                        maxResolution = "${maxWidth}x${maxHeight}"

                                        android.util.Log.d("H265Check", "Max resolution: $maxResolution")

                                        // 检查解码器可靠性（基于设备和解码器名称）
                                        reliable = isDecoderReliable(codec.name, Build.HARDWARE)
                                        android.util.Log.d("H265Check", "Decoder reliable: $reliable")
                                    }

                                    // 获取支持的配置文件
                                    val profileLevels = capabilities.profileLevels
                                    for (profileLevel in profileLevels) {
                                        profiles.add(
                                                "Profile: ${profileLevel.profile}, Level: ${profileLevel.level}"
                                        )
                                    }
                                    android.util.Log.d("H265Check", "Supported profiles: ${profiles.size}")
                                } catch (e: Exception) {
                                    android.util.Log.w(
                                            "H265Check",
                                            "Error getting codec capabilities for ${codec.name}: ${e.message}"
                                    )
                                }
                                // 找到第一个硬件解码器就停止
                                break
                            }
                        }
                    }
                    // 如果已经找到硬件解码器，跳出外层循环
                    if (hasDecoder) break
                }
            }

            result["hasDecoder"] = hasDecoder
            result["decoderName"] = decoderName
            result["profiles"] = profiles
            result["maxResolution"] = maxResolution
            result["reliable"] = reliable
        } catch (e: Exception) {
            android.util.Log.e("H265Check", "Error checking H.265 support: ${e.message}")
            result["hasDecoder"] = false
            result["decoderName"] = ""
            result["profiles"] = emptyList<String>()
            result["maxResolution"] = ""
            result["reliable"] = false
        }

        return result
    }

    /** 检查解码器可靠性 */
    private fun isDecoderReliable(decoderName: String, hardware: String): Boolean {
        val unreliableDecoders =
                listOf(
                        "rk", // RK芯片的硬件解码器通常不稳定
                        "mali", // Mali GPU的解码器
                        "vivante" // Vivante GPU的解码器
                )

        val unreliableHardware = listOf("rk3568", "rk3566", "rk3399")

        val decoderLower = decoderName.lowercase()
        val hardwareLower = hardware.lowercase()

        // 检查是否为不可靠的解码器
        for (unreliable in unreliableDecoders) {
            if (decoderLower.contains(unreliable)) {
                return false
            }
        }

        // 检查是否为不可靠的硬件
        for (unreliable in unreliableHardware) {
            if (hardwareLower.contains(unreliable)) {
                return false
            }
        }

        return true
    }

    /** 检测WebView H.265支持 */
    private fun checkWebViewH265Support(): Map<String, Any> {
        val result = mutableMapOf<String, Any>()

        try {
            // 获取WebView版本信息
            val webViewVersion =
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
                        try {
                            val webViewPackage = WebView.getCurrentWebViewPackage()
                            webViewPackage?.versionName ?: "Unknown"
                        } catch (e: Exception) {
                            // 在某些设备上可能不支持getCurrentWebViewPackage方法
                            "Unknown"
                        }
                    } else {
                        // Android 8.1以下版本不支持getCurrentWebViewPackage
                        "Unknown (API ${Build.VERSION.SDK_INT})"
                    }

            // 尝试获取Chromium版本
            val chromiumVersion = extractChromiumVersion(webViewVersion)

            // 基于WebView版本判断H.265支持
            // 一般来说，较新版本的WebView/Chromium才支持H.265
            val supportsH265 = isWebViewH265Capable(webViewVersion, chromiumVersion)

            result["supportsH265"] = supportsH265
            result["webViewVersion"] = webViewVersion
            result["chromiumVersion"] = chromiumVersion
        } catch (e: Exception) {
            android.util.Log.e(
                    "WebViewH265Check",
                    "Error checking WebView H.265 support: ${e.message}"
            )
            result["supportsH265"] = false
            result["webViewVersion"] = "Unknown"
            result["chromiumVersion"] = "Unknown"
        }

        return result
    }

    /** 从WebView版本中提取Chromium版本 */
    private fun extractChromiumVersion(webViewVersion: String): String {
        return try {
            // WebView版本通常包含Chromium版本信息
            val regex = Regex("""(\d+\.\d+\.\d+\.\d+)""")
            val matchResult = regex.find(webViewVersion)
            matchResult?.value ?: "Unknown"
        } catch (e: Exception) {
            "Unknown"
        }
    }

    /** 判断WebView是否支持H.265 */
    private fun isWebViewH265Capable(webViewVersion: String, chromiumVersion: String): Boolean {
        return try {
            // 提取主版本号
            val versionParts = chromiumVersion.split(".")
            if (versionParts.isNotEmpty()) {
                val majorVersion = versionParts[0].toIntOrNull() ?: 0
                // Chromium 88+ 开始更好地支持H.265
                return majorVersion >= 88
            }
            false
        } catch (e: Exception) {
            false
        }
    }

    /** 初始化YsApi助手和开机自启 */
    private fun initializeYsApiHelper() {
        try {
            android.util.Log.d("MainActivity", "开始初始化YsApi助手")

            // 输出设备信息用于调试
            val deviceInfo = ysApiHelper.getDeviceInfo()
            android.util.Log.d("MainActivity", deviceInfo)

            // 初始化YsApi
            val initResult = ysApiHelper.initializeYsApi()
            android.util.Log.d("MainActivity", "YsApi初始化结果: $initResult")

            // 无论YsApi是否初始化成功，都尝试设置开机自启
            android.util.Log.d("MainActivity", "开始设置开机自启...")
            val autoStartResult = ysApiHelper.setupAutoStart()

            android.util.Log.d("MainActivity",
                "开机自启设置结果 - 方法: ${autoStartResult.method}, 成功: ${autoStartResult.success}")

            when (autoStartResult.method) {
                AutoStartMethod.YS_API -> {
                    if (autoStartResult.success) {
                        android.util.Log.d("MainActivity", "✓ 使用YsApi selfStart方式设置开机自启成功")
                    } else {
                        android.util.Log.w("MainActivity", "✗ YsApi selfStart方式设置失败")
                    }
                }
                AutoStartMethod.BOOT_RECEIVER -> {
                    if (autoStartResult.success) {
                        android.util.Log.d("MainActivity", "✓ 使用BootCompleteReceiver方式设置开机自启成功")
                    } else {
                        android.util.Log.w("MainActivity", "✗ BootCompleteReceiver方式设置失败")
                    }
                }
                AutoStartMethod.NONE -> {
                    android.util.Log.e("MainActivity", "✗ 没有可用的开机自启方式")
                }
            }

            // 如果YsApi初始化成功，设置程序守护
            if (initResult) {
                try {
                    val daemonResult = ysApiHelper.setDaemon()
                    android.util.Log.d("MainActivity", "程序守护设置结果: $daemonResult")
                } catch (e: Exception) {
                    android.util.Log.w("MainActivity", "设置程序守护失败", e)
                }
            }

        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "YsApi助手初始化失败", e)
        }
    }

    override fun onResume() {
        super.onResume()
        // 确保程序守护在应用恢复时重新启用
        try {
            ysApiHelper.setDaemon()
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "恢复程序守护失败", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理YsApi资源
        try {
            ysApiHelper.unbindAIDLService()
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "清理YsApi资源失败", e)
        }
    }

    /** 测试开机自启兼容性 */
    private fun testAutoStartCompatibility(): Map<String, Any> {
        val result = mutableMapOf<String, Any>()

        try {
            // 设备信息
            result["deviceInfo"] = ysApiHelper.getDeviceInfo()

            // YsApi兼容性检测
            val ysApiSupported = ysApiHelper.isYsApiSelfStartSupported()
            result["ysApiSupported"] = ysApiSupported

            // BootCompleteReceiver兼容性检测
            val bootReceiverSupported = ysApiHelper.isBootCompleteReceiverSupported()
            result["bootReceiverSupported"] = bootReceiverSupported

            // 当前使用的开机自启方式
            val currentMethod = when {
                ysApiSupported -> "YsApi selfStart"
                bootReceiverSupported -> "BootCompleteReceiver"
                else -> "不支持任何方式"
            }
            result["recommendedMethod"] = currentMethod

            // 测试结果
            val testResult = when {
                ysApiSupported && bootReceiverSupported -> "两种方式都支持"
                ysApiSupported -> "仅支持YsApi方式"
                bootReceiverSupported -> "仅支持BootCompleteReceiver方式"
                else -> "不支持任何开机自启方式"
            }
            result["compatibilityTest"] = testResult

            android.util.Log.d("AutoStartTest", "兼容性测试结果: $result")

        } catch (e: Exception) {
            android.util.Log.e("AutoStartTest", "兼容性测试失败", e)
            result["error"] = e.message ?: "未知错误"
        }

        return result
    }

    /** 获取内存信息 */
    private fun getMemoryInfo(): Map<String, Any> {
        val result = mutableMapOf<String, Any>()

        try {
            val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            // 总内存
            result["totalMemory"] = memoryInfo.totalMem
            // 可用内存
            result["availableMemory"] = memoryInfo.availMem
            // 内存使用率
            val usedMemory = memoryInfo.totalMem - memoryInfo.availMem
            val memoryUsagePercent = (usedMemory.toDouble() / memoryInfo.totalMem.toDouble() * 100).toInt()
            result["memoryUsagePercent"] = memoryUsagePercent
            // 是否内存不足
            result["isLowMemory"] = memoryInfo.lowMemory
            // 内存阈值
            result["threshold"] = memoryInfo.threshold

            // 获取应用内存使用情况
            val runtime = Runtime.getRuntime()
            result["appMaxMemory"] = runtime.maxMemory()
            result["appTotalMemory"] = runtime.totalMemory()
            result["appFreeMemory"] = runtime.freeMemory()
            result["appUsedMemory"] = runtime.totalMemory() - runtime.freeMemory()

            android.util.Log.d("MemoryInfo", "内存信息: $result")

        } catch (e: Exception) {
            android.util.Log.e("MemoryInfo", "获取内存信息失败: ${e.message}")
            result["error"] = e.message ?: "未知错误"
        }

        return result
    }
}
