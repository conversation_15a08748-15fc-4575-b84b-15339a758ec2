package com.mingsign.esop_client

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

/**
 * 开机自启动广播接收器
 * 用于在设备开机时自动启动应用
 */
class BootCompleteReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "BootCompleteReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        if (Intent.ACTION_BOOT_COMPLETED == intent.action) {
            Log.d(TAG, "收到开机完成广播，开始启动应用")

            try {
                // 创建启动应用的Intent
                val launchIntent = Intent(context, MainActivity::class.java).apply {
                    action = Intent.ACTION_MAIN
                    addCategory(Intent.CATEGORY_LAUNCHER)
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }

                // 启动应用
                context.startActivity(launchIntent)
                Log.d(TAG, "应用启动成功")

            } catch (e: Exception) {
                Log.e(TAG, "启动应用失败", e)
            }
        }
    }
}