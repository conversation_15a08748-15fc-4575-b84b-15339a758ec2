package com.mingsign.esop_client

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.pm.PackageInstaller
import android.util.Log

class InstallResultReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        val status = intent.getIntExtra(PackageInstaller.EXTRA_STATUS, PackageInstaller.STATUS_FAILURE)
        val message = intent.getStringExtra(PackageInstaller.EXTRA_STATUS_MESSAGE)

        when (status) {
            PackageInstaller.STATUS_SUCCESS -> {
                Log.d("SilentInstall", "安装成功")
                // 可以发送广播通知Flutter层安装成功
            }
            PackageInstaller.STATUS_FAILURE -> {
                Log.e("SilentInstall", "安装失败: $message")
            }
            PackageInstaller.STATUS_FAILURE_ABORTED -> {
                Log.e("SilentInstall", "安装被中止: $message")
            }
            PackageInstaller.STATUS_FAILURE_BLOCKED -> {
                Log.e("SilentInstall", "安装被阻止: $message")
            }
            PackageInstaller.STATUS_FAILURE_CONFLICT -> {
                Log.e("SilentInstall", "安装冲突: $message")
            }
            PackageInstaller.STATUS_FAILURE_INCOMPATIBLE -> {
                Log.e("SilentInstall", "不兼容: $message")
            }
            PackageInstaller.STATUS_FAILURE_INVALID -> {
                Log.e("SilentInstall", "无效的APK: $message")
            }
            PackageInstaller.STATUS_FAILURE_STORAGE -> {
                Log.e("SilentInstall", "存储空间不足: $message")
            }
            else -> {
                Log.e("SilentInstall", "未知错误: $status, $message")
            }
        }
    }
}