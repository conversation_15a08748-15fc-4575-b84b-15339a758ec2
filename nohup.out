Launching lib/main.dart on rk3568 r in debug mode...
Running Gradle task 'assembleDebug'...                          
I/flutter ( 1693): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 1693): [38;5;12m│ #0   StorageCleanupService._checkStorageAndCleanup (package:esop_client/services/storage_cleanup_service.dart:59:14)[0m
I/flutter ( 1693): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 1693): [38;5;12m│ 17:12:09.373 (+1:55:00.950399)[0m
I/flutter ( 1693): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 1693): [38;5;12m│ 💡 当前存储使用率: 2.1%[0m
I/flutter ( 1693): [38;5;12m└───────────────────────────────────────────────────────────────────────────────[0m
Running Gradle task 'assembleDebug'...                             23.5s
✓ Built build/app/outputs/flutter-apk/app-debug.apk
Installing build/app/outputs/flutter-apk/app-debug.apk...           4.6s
I/flutter ( 6326): [IMPORTANT:flutter/shell/platform/android/android_context_vk_impeller.cc(61)] Using the Impeller rendering backend (Vulkan).
Error connecting to the service protocol: failed to connect to http://127.0.0.1:55556/Y3UMfBVwAFc=/ HttpException: Connection closed before full header was received, uri = http://127.0.0.1:55556/Y3UMfBVwAFc=/ws
