import 'package:flutter/material.dart';
import 'lib/services/mac_address_service.dart';

void main() {
  runApp(MacAddressTestApp());
}

class MacAddressTestApp extends StatelessWidget {
  const MacAddressTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(title: 'MAC地址测试', home: MacAddressTestPage());
  }
}

class MacAddressTestPage extends StatefulWidget {
  const MacAddressTestPage({super.key});

  @override
  _MacAddressTestPageState createState() => _MacAddressTestPageState();
}

class _MacAddressTestPageState extends State<MacAddressTestPage> {
  String _macAddress = '未获取';
  bool _isLoading = false;

  Future<void> _getMacAddress() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final macAddressService = MacAddressService();
      final macAddress = await macAddressService.getMacAddress();
      setState(() {
        _macAddress = macAddress;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _macAddress = '获取失败: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('MAC地址获取测试')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'MAC地址:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            _isLoading
                ? CircularProgressIndicator()
                : Text(
                    _macAddress,
                    style: TextStyle(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: _isLoading ? null : _getMacAddress,
              child: Text('获取MAC地址'),
            ),
          ],
        ),
      ),
    );
  }
}
